include "../base.thrift"
include "./graph.thrift"
include "./hotfix.thrift"
include "./pipeline/workflow.thrift"
namespace go bytedance.bits.optimus

enum MrState {
    all
    opened
    closed
    merged
}
enum MrType {
    feature,
    bug,
    optimize,
    merge,
    package,
    patch,
    lab,
    slardar
}
enum ReviewRole {
    RD
    QA
}

enum PipelineStatus {
    running
    pending
    success
    succeded
    failed
}

enum MrMode {
    multiple_master_mr_parent // 多宿主主仓
    multiple_master_mr_child // 多宿主子仓
    multiple_mr_common // 多宿主公共依赖
    single_master_mr_parent
    single_master_mr_child // 单宿主子仓
    isolate_master
}

enum AppReleaseStatus {
    START                                       // 开始封版
    LOCKING_DEVELOP_BRANCH                      // dev分支加锁
    CHECKING_DEVELOP_ATOMIC_LOCK                // 检查dev分支是否加锁成功
    CREATING_GREY_BRANCH                        // 创建灰度分支
    CREATING_RC_TO_GREY_MR                      // 创建rc/dev分支到灰度分支的MR
    CHANGING_TARGET_BRANCH_TO_GREY              // 改变MR(dev)的目标分支为灰度分支
    UPDATING_FEATURE_STATUS_TO_MEEGO            // 更新需求状态
    WAITING_CLOSE_VERSION                       // 等待封版结束事件
    LOCKING_GREY_BRANCH                         // 灰度分支加锁
    CHECKING_GREY_ATOMIC_LOCK                   // 检查灰度分支是否加锁成功
    CHANGING_TARGET_BRANCH_TO_DEVELOP           // 改变目标分支到deve
    DELETING_GREY_RELEASE_LOCK                  // 删除灰度分支锁
    MODIFYING_PROJECT_CONFIG                    // 更改工程配置
    DELETING_DEVELOP_RELEASE_LOCK               // 删除dev分支锁
    END                                         // 封版结束
}

enum RepoStatusOnGitlab {
    CONFLICT // 冲突
    DISABLE_OUTDATED_MERGE //
    EMPTY_CONTENT // 空内容
    FORBID_EMPTY_CONTENT_MERGE //
    MERGED // 成功合入
    NEW_COMMITS_IN_TARGET // target上有新提交
}

enum CreateMrType{
    SINGLE_MR,
    MULTI_MR,
    MULTI_HOST_MR,
    MULTI_INTEGRATION
}


struct MRUniqueID {
    1: optional i64 mrID
    2: optional i64 projectID
    3: optional i64 iID
    4: optional i64 devID
}

struct SlardarFeatureInfo  {
    1: required string title
    2: required string region
    3: required string value
    4: required string label
    5: required string issueID
    6: required i32 aID
    7: required string detailURL
    8: required string os
    9: required string ID
    10: required string crashType
}
struct MeegoAndRocketUserInfo {
    1: required string userID
    2: required string name
}
struct MeegoAndRocketFeatureInfo {
    1: required string requirementID
    2: required string URL
    3: required string value
    4: required string label
    5: required list<MeegoAndRocketUserInfo> qa
    6: required string supportedApps
    7: required string requirementTitle
    8: required list<MeegoAndRocketUserInfo> pm
}
struct MeegoBugInfo {
	1: required string URL
    2: required i64 bugID
    3: required string projectKey
    4: required string bugType
    5: required string label
}
struct JiraFeatureInfo {
    1: required string URL
    2: required string label
}

struct TimeRangeFilter {
    1: required i64 gte
    2: required i64 lte
}

struct versionDependency {

}
struct User {
    1: required string enName
    2: required string zhName
    3: required string avatar
    4: required i64 ID
}
struct Version {
    1: required string version
    2: required string name
    3: optional i64 componentID
    4: optional string originalVersion
    5: optional string source
}
struct MrIDInfo {
    1: required i64 iID
    2: required i64 projectID
    3: required i64 mrID
}

struct Reviewer {
    1: required string username
    2: required string status
    3: required string reviewRole
    4: required i64 createTime
    5: required i64 finishTime
    6: optional string avatarUrl
    7: optional bool removable
    11: optional MrIDInfo mrIDInfo
    12: optional string zhName
    13: optional i64 ID
}

struct MrProjectInfo {
    1: required string zhName
    2: required string enName
    3: required i64 projectID
    4: required i64 appID
    5: required string RepoAddr
    6: optional i8 appType
}

struct ProjectInfo {
    1: required string name
    2: required i64 projectID
    3: required i64 appID
    4: required string RepoAddr
    5: required i8 appType
    6: required string groupName
}

struct GroupInfo {
    1: required string zhName
    2: required string enName
    3: required i64 projectID
    4: required i64 appID
    5: required string RepoAddr
    6: optional i8 appType
}
struct MrDependency {
    1: required i64 ID
    2: required string projectName
    3: required string fromBranch
    4: required string targetBranch
    5: required i64 mergedTime
    6: required string mrRole
    8: required string state
    9: required list<Version> version
    10: required string intergrationState
    11: required string mrMode
    12: optional i64 parentMrID
    13: optional i64 parentMrDependencyID
    14: optional string gitRepoAddr
    15: optional string fromBranchUrl
    16: optional string targetBranchUrl
    17: optional i64 projectID
    18: optional i64 iid
}

struct bitsMrRelation {
    1: required i64 ID
    2: required string sourceBranch
    3: required string targetBranch
    4: required i64 mergedTime
    5: required string mrRole
    6: required string state
    7: required string mrMode
    8: required ProjectInfo project
    9: required string MergedCommitID
    10: required i64 parentMrID
    11: required i64 projectID
    12: required i64 iID
    13: optional i64 codebaseProjectID
    14: optional i64 codebaseIID
    15: optional string lastCommitID
}
struct MrLock {
    1: required string submitterName
    2: required string lockType
    3: required string projectName
    4: required string mrTitle
    5: required string mrUrl
    6: required i64 mrID
}

struct LockInfo {
    1: required i64 ID
    2: required i64 createTime
    3: required i64 IID
    4: required string lockType
    5: required i64 MrID
    6: required string MrTitle
    7: required string MrUrl
    8: required i64 ProjectID
    9: required string submitterName
    10: required string targetBranch
    11: required string type
}

struct MrTag{
    1: required i64 id
    2: required string name
    3: required string color
    4: required string catalog
    5: required string groupProjectName
    6: required string creator
    7: optional string enName
    8: optional string reason
}

enum CodeReviewMode{
    bits = 0,
    gitlab = 1,
}

// 此接口废弃了不要再家逻辑了, 有需求加MainInfo中
struct MrInfo {
    1: required i64 ID
    2: required string groupName
    3: required i64 projectID
    4: required i64 iID
    5: required string projectName
    6: required string fromBranch
    7: required string toBranch
    8: required string mrTitle
    9: required string mrType
    10: required string submitterEnName
    12: required string submitterZhName
    13: required i64 createTime
    14: required string avatarUrl
    15: required string mrState
    16: required string authorName
    17: required string pipelineStatus
    18: required i16 isGitlabMr
    19: required string productVersion
    20: required string conflicted
    21: required i64 latestInternalID
    22: required list<Version> versionDependency
    23: optional LockInfo lockInfo
    24: optional i64 mergedTime
    25: optional string integrationState
    26: optional string gitRepoAddr
    27: optional string MrMode
    28: required string featureInfo
    29: required string discription
    30: required string url
    31: optional string mergeCommitSha
    32: optional bool superior
    33: optional bool removeSourceBranch
    34: optional bool isWip
    35: optional i64 lastPipelineID
    36: optional string larkGroupID
    37: optional i64 platform
    38: optional list<MrTag> tags
    39: optional CodeReviewMode reviewMode
    40: optional string lastCommitID
    41: optional string pipelineEnv
    42: optional string mergeBase
    43: optional i32 businessType
    44: optional i64 businessID
    45: optional string mergeBaseCommitID
    46: optional i64 spaceID
    47: optional string note
}

struct MrMainInfo {
    1: required i64 ID
    2: required string groupName
    3: required i64 projectID
    4: required i64 iID
    5: required string projectName
    6: required string fromBranch
    7: required string toBranch
    8: required string mrTitle
    9: required string mrType
    10: required i64 createTime
    11: required string mrState
    12: required string authorName
    13: required string productVersion
    14: required i16 conflicted
    15: required i64 mergedTime
    16: required string lastCommitID // 不保证一致性
    17: optional i16 appType
    18: optional string integrationState // 不保证一致性
    19: optional i64 integrationStartTime // 不保证一致性
    20: optional i64 integrationEndTime // 不保证一致性
    21: optional bool isWip
    22: optional bool isDeleteSourceBranch // 不保证一致性
    23: optional string mergedCommitID
    24: optional string description
    25: optional string extra
    26: optional CodeReviewMode reviewMode // 不保证一致性
    27: optional i64 OptimusMrID
    28: optional i64 lastSecurityTaskID // 不保证一致性
    29: optional string larkGroupID
    30: optional i64 codebaseRepoID
    31: optional i64 codebaseChangeID
    32: optional string url // gitlab url
    33: optional i32 businessType
    34: optional i64 businessID
    35: optional string mergeBaseCommitID
}


 struct Pipeline {
    1: required i64 ID
    2: required i64 mergeRequestID
    3: required string status
    4: required i64 createdTime
    8: required i64 finishTime
    5: required string commitID
    6: required string branch
    7: required string envVar
    9: required i64 projectID
    10: required string url
    11: required i64 gitlabPipelineID
    12: optional i16 pipelineType
    13: optional i64 thirdPlatformPipelineID
    14: optional string platform
 }

// 获取mr列表
struct GetMrListQuery {
    1: required string groupName
    2: required string source
    3: required i64 lastID
    4: optional i64 appID
    5: optional i16 wip
    6: optional i16 conflicted
    7: optional i64 targetProjectID
    8: optional string targetBranch
    9: optional string mrType
    10: optional string sourceBranch
    11: optional string keyword
    13: optional i32 count
    14: optional string authorName
    15: optional string state
    16: optional string reviewerName
    17: optional string productVersion
    18: optional string reviewState
    19: optional string tags
    20: optional string sort
    21: optional string order
    22: optional string targetVersion
    23: optional TimeRangeFilter createdTime
    24: optional TimeRangeFilter mergedTime
    25: optional i64 from // 分页使用, skip 多少个
    255: optional base.Base Base
}
struct GetMrListResponse {
    1: optional list<MrInfo> List, //mergerequest表的主键ID
    255: required base.BaseResp BaseResp;
}

// 获取mr详情
struct GetMrInfoQuery {
    1: required i64 mrID // mergerequest表的主键ID
    2: optional i64 projectID
    3: optional i64 iID
    4: optional i64 devID
    255: required base.Base Base
}
struct GetMrInfoResponse {
    1: optional MrInfo info, //文件内容数据
    255: required base.BaseResp BaseResp;
}
// 获取mr主体信息
struct GetMrMainInfoQuery {
    1: required i64 mrID // mergerequest表的主键ID
    2: optional i64 projectID
    3: optional i64 iID
    4: optional i64 devID
    255: required base.Base Base
}
struct GetMrMainInfoResponse {
    1: optional MrMainInfo info, // mr的主体信息
    255: required base.BaseResp BaseResp;
}
// 获取mr review
struct GetMrReviewersQuery {
    1: required i64 mrID // mergerequest表的主键ID
    2: optional i64 projectID
    3: optional i64 iID
    4: optional i64 devID
    255: required base.Base Base
}
struct GetMrReviewersResponse {
    1: optional list<Reviewer> List,
    255: required base.BaseResp BaseResp;
}
// 获取 mr review的原因
struct ReviewerReasonsContent {
	1: required list<string> files
	2: required list<string> branch
	3: required list<string> operator
	4: required list<string> customizedReason
	5: required string projectName
	6: required string projectFullName
	7: required i64 reviewerNum
	8: required i64 reviewerNumUnderAdmin
	9: required string key
    10: optional i64 rdBmNum
    11: optional i64 qaBmNum
}
struct GetMrReviewerCommonReasonsQuery {
    1: required i64 reviewId // review人在表中的主键id
    2: required string actionUsername // 请求触发人
    255: required base.Base Base
}
struct GetMrReviewerCommonReasonsResponse {
    1: required list<ReviewerReasonsContent> reasons,
    2: required list<ReviewerReasonsContent> irrRemovableReasons,
    3: required bool canRemove,
    255: required base.BaseResp BaseResp;
}

// 获取mr pipeline的信息 一个mr的多次pipeline信息
struct GetMrPipelinesQuery {
    1: required i64 mrID // mergerequest表的主键ID
    2:  optional i64 devID // mergerequest表的主键ID
    255: required base.Base Base
}
struct GetMrPipelinesResponse {
    1: optional list<Pipeline> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp;
}
// 获取mr relation信息, mr的子仓信息
struct GetMrRelationsQuery {
    1: required i64 mrID // mergerequest表的主键ID
    2: optional i64 devID // mergerequest表的主键ID
    255: required base.Base Base
}
struct GetMrRelationsResponse {
    1: required list<MrDependency> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp;
}
// 获取mr relation信息, mr的子仓信息
struct GetBitsMrRelationsQuery {
    1: required i64 mrID // mergerequest表的主键ID
    2: optional i64 projectID
    3: optional i64 iID
    4: optional i64 devID
    255: required base.Base Base
}
struct GetBitsMrRelationsResponse {
    1: required list<bitsMrRelation> mrs,
    255: required base.BaseResp BaseResp;
}
// 获取mr relation信息, mr的子仓信息
struct GetMrRequestIDByProjectIDAndIIDQuery {
    1: required i64 projectID // gitlab的仓库ID
    2: required i64 iID // gitlab的mr ID
    255: optional base.Base Base
}
struct GetMrRequestIDByProjectIDAndIIDResponse {
    1: optional i64 mrID,
    2: optional i64 devID
    255: optional base.BaseResp BaseResp;
}
// 获取mr的发布情况
struct PublishVersionComponentInfo {
    1: required i64 componentID
    2: required string logUrl
    3: required string name
    4: required i16 repoTechType
    5: required string status
    6: required string version
}
struct PublishVersionInfo {
    1: required string commit
    2: required string versionBase
    3: required string versionFinal
    4: required string status
    5: required string repoStatus
    6: required string publishType
    7: required string branch
    8: required string username
    9: required i64 mrID
    10: optional PublishVersionComponentInfo componentInfo
    11: optional i64 projectId
}
struct GetMrDependencyPublishVersionInfoByProjectIDAndIIDQuery {
    1: required i64 projectID // gitlab的仓库ID
    2: required i64 iID // gitlab的mr ID
    255: optional base.Base Base
}
struct GetMrDependencyPublishVersionInfoByProjectIDAndIIDResponse {
    1: optional list<PublishVersionInfo> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp;
}
// 获取mr锁的信息
struct MrLockInfo {
    1: required string targetBranch
    2: required string mrUrl
    3: required i64 iID
    4: required double createTime
    5: required i64 projectID
    6: required string type
    7: required string title
    8: required i64 mrID
    9: required string authorName
}
struct GetMrLockInfoByProjectIDAndTargetBranchQuery {
    1: required i64 projectID // gitlab的仓库ID
    2: required i64 iID // gitlab的mr ID
    3: required string targetBranch
    255: optional base.Base Base
}
struct GetMrLockInfoByProjectIDAndTargetBranchResponse {
    1: optional list<MrLockInfo> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp;
}
// 获取原子锁信息
struct GetAtomicLockInfoByProjectAndTargetBranchQuery {
    1: required i64 projectID // gitlab的仓库ID
    2: required i64 iID // gitlab的mr ID
    3: required string targetBranch
    255: optional base.Base Base
}
struct GetAtomicLockInfoByProjectAndTargetBranchResponse {
    1: optional MrLockInfo lockInfo,
    255: required base.BaseResp BaseResp;
}
// cony settings
struct GetConySettingsByGroupNameQuery{
    1: required string group_name,
    255: optional base.Base Base,
}
struct GetConySettingsByGroupNameResponse{
    1: required ConySettings settings,
    255: required base.BaseResp BaseResp,
}
struct ConySettings {
    1: optional i64 calendar_workspace_id,
    2: optional GroupSettingsReleaseVersion release_version,
    3: optional string develop_branch,
    4: optional string grey_branch,
}
// 获取config_group中的BuildPackageJson
struct ConfigGroupBuildPackageJsonItem {
    1: required string value
    2: required string label
}
struct ConfigGroupBuildPackageJson {
    1: required string fieldType
    2: required string placeholder
    3: required string key
    4: required string label
    5: required list<ConfigGroupBuildPackageJsonItem> options
    6: required string defaultValue
	7: required bool Optional (go.tag = "json:\"optional\"")
}
struct GetConfigGroupBuildConfigQuery {
    1: required i64 projectID
    255: optional base.Base Base,
}
struct GetConfigGroupBuildConfigResponse {
    1: required list<ConfigGroupBuildPackageJson> buildPackageJson
    2: required string buildPackageType
    3: required string buildPackageURL
    255: required base.BaseResp BaseResp;
}
// 获取MR下用到过的所有版本号
struct GetVersionsHasUsedInMrQuery {
    1: required string groupName
    255: optional base.Base Base,
}
struct GetVersionsHasUsedInMrResponse {
    1: required list<string> versions
    255: required base.BaseResp BaseResp
}

// 校验创建MR的参数是否合法
struct CreateMrParamsValidCheckQuery {
    1: required string params
    2: required string username
    255: optional base.Base Base
}
struct CreateMrParamsValidCheckResponse {
    1: required bool checkResult
    2: required string msg
    255: required base.BaseResp BaseResp
}

struct GroupSettingsTimeItem {
    1: required i64 minute,
    2: required i64 hour,
}
// 获取仓库中的所有分支
enum SortOption {
    UserCreatedFirst = 1, // 平台创建分支（username!=ci_cony 且前缀为 optimus/ 或 integration/）后移
}
enum FilterOption {
    RC = 1, // 过滤 dev 分支，只显示 rc 分支
}
struct BranchInfo {
    1: required string name
    2: required string username
    3: required i64 updateTime
    4: required bool isFormal
    5: required i64 createTime
    6: required string commit
    7: required i64 projectID
}
struct SortInfo {
    1: required string order
    2: required string field
}
struct GetProjectBranchesQuery {
    1: required string text
    2: required list<SortInfo> sorts
    3: required i64 limit
    4: optional i64 projectID
    5: optional string username
    6: optional string groupName
    7: optional SortOption sortOption
    8: optional FilterOption filterOption
    255: optional base.Base Base
}
struct GetProjectBranchesResponse {
    1: required list<BranchInfo> List (go.tag = "json:\"list\"")
    2: required i64 total
    255: required base.BaseResp BaseResp
}
struct GetMrCustomModifyParamsQuery {
    1: optional i64 mrID
    2: optional i64 projectID
    3: optional i64 iID
    4: optional i64 devID
    255: optional base.Base Base
}
struct GetMrCustomModifyParamsResponse {
    1: required string params
    255: required base.BaseResp BaseResp
}

struct GroupSettingsReleaseVersion {
	1: required bool openReleaseVersion,               // 开启封板 (字段和目前线上的保持一致)
    2: required string event_name,                     // 封板事件的名称
    3: required list<string> group_numbers,            // 封板群
    4: optional GroupSettingsTimeItem release_time,    // 封板开始时间
    5: optional GroupSettingsTimeItem cancel_time,     // 结束时间
    6: optional GroupSettingsTimeItem warn_time,       // 封板前发送封板通知的时间
}

struct FilterVersionCloseGroupsQuery {
    1: required i64 hour (go.tag = "json:\"hour\" form:\"hour\""), // 小时 24 小时制
    2: required i64 min (go.tag = "json:\"min\" form:\"min\""),    // 分钟
    3: optional i64 flag (go.tag = "json:\"flag\" form:\"flag\""), // 默认 0 表示是开始时间 1 表示是结束时间
    255: optional base.Base Base,
}

struct VersionCloseGroup {
    1: required string group_name,
    2: required ConySettings cony_settings,
    3: optional i64 project_id,
}

struct FilterVersionCloseGroupsResponse {
    1: required list<VersionCloseGroup> groups,
    255: required base.BaseResp BaseResp,
}


struct GetProjectInfoByProjectEnNameOrRepoAddrQuery {
    1: optional string enName// gitlab的仓库ID
    2: optional string repoAddr // xxx.git
    255: optional base.Base Base
}
struct GetProjectInfoByProjectEnNameOrRepoAddrResponse {
    1: optional MrProjectInfo info,
    255: required base.BaseResp BaseResp;
}
// 获取一个项目下有使用过的子仓信息
struct GetProjectHasUsedRepoInfoByProjectIDQuery {
    1: required i64 projectID // gitlab的仓库ID
    255: optional base.Base Base
}
struct GetProjectHasUsedRepoInfoByProjectIDResponse {
    1: optional list<MrProjectInfo> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp;
}

struct GetProjectInfoByProjectIDReq {
    1: required i64 projectID
    255: optional base.Base Base
}

struct GetProjectInfoByProjectIDResponse {
    1: required MrProjectInfo info,
    255: required base.BaseResp BaseResp;
}
// 获取仓库的信息
struct GetGroupInfoByProjectIDQuery {
    1: required i64 projectID
    255: optional base.Base base
}
struct GetGroupInfoByProjectIDResponse {
    1: required GroupInfo info,
    255: required base.BaseResp BaseResp;
}
// 获取空间的主仓信息
struct GetGroupInfoByGroupNameQuery {
    1: required string groupName
    255: optional base.Base base
}
struct GetGroupInfoByGroupNameResponse {
    1: required GroupInfo info,
    255: required base.BaseResp BaseResp;
}

// 获取主mr的timeline信息
struct TimelineInfo {
    1: required i64 ID
    2: required string Data
    3: required i64 MrID
    4: required i64 CreateTime
    5: required i64 UpdateTime
    6: required string EventType
    7: required string Operator
    8: optional i64 projectID
    9: optional i64 iID
    10: optional string Level
}

struct TimeLineEventBuildPackageData{
    1: optional string Status (go.tag="json:\"status\""),
    2: optional string ProjectName (go.tag="json:\"project_name\""),
    3: optional string PipelineUrl (go.tag="json:\"pipeline_url\"")
    4: optional string Jobs (go.tag="json:\"jobs\""),
    5: optional i64 BuildPackageID (go.tag="json:\"build_package_id\"")
}

struct TimeLineEventJobSkipData{
    1: optional string Status (go.tag="json:\"status\"")
    2: optional string ProjectName (go.tag="json:\"project_name\"")
    3: optional string Message (go.tag="json:\"message\"")
    4: optional i64 JobID (go.tag="json:\"job_id\"")
    5: optional string JobName (go.tag="json:\"job_name\"")
}

struct TimeLineEventThirdPartyCheckTaskStartOrRerunItem {
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional string TaskType (go.tag="json:\"task_type\"")
    3: optional i64    TaskID (go.tag="json:\"task_id\"")
    4: optional string Label (go.tag="json:\"label\"")
}

struct TimeLineEventThirdPartyCheckTaskCheckEndItem{
    1: optional string Status (go.tag="json:\"status\"")
    2: optional string ProjectName (go.tag="json:\"project_name\"")
    3: optional i64    TaskID (go.tag="json:\"task_id\"")
    4: optional string Label (go.tag="json:\"label\"")
    5: optional string TaskType (go.tag="json:\"task_type\"")
    6: optional string DetailURL (go.tag="json:\"detail_url\"")
}

struct TimeLineEventMergeTargetData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional string Content (go.tag="json:\"content\"")
}

struct TimeLineEventCodebaseTaskEndItem{
    1: optional string Status (go.tag="json:\"status\"")
    2: optional string ProjectName (go.tag="json:\"project_name\"")
    3: optional i64    TaskID (go.tag="json:\"task_id\"")
    4: optional string Label (go.tag="json:\"label\"")
    5: optional string TaskType (go.tag="json:\"task_type\"")
}

struct TimeLineEventCodebaseTaskStartItem{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional i64    TaskID (go.tag="json:\"task_id\"")
    3: optional string Label (go.tag="json:\"label\"")
    4: optional string TaskType (go.tag="json:\"task_type\"")
}

struct TimeLineEventResolvePodspecConflictData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional string Content (go.tag="json:\"content\"")
}

struct TimeLineEventRemoveEmptyMRData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional string Content (go.tag="json:\"content\"")
}

struct TimeLineEventPipelineTriggerData {
    1: optional string Status (go.tag="json:\"status\"")
    2: optional string ProjectName (go.tag="json:\"project_name\"")
    3: optional string Message (go.tag="json:\"message\"")
}

struct TimeLineEventForceMergeData{

}

struct TimeLineEventChangeReviewersData{
    1: optional list<string> To (go.tag="json:\"to\"")
    2: optional bool  All (go.tag="json:\"all\"")
    3: optional string  ProjectName (go.tag="json:\"project_name\"")
    4: optional list<string> From (go.tag="json:\"from\"")
}

struct TimeLineEventJobRerunData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional string JobID (go.tag="json:\"job_id\"")
    3: optional string URL (go.tag="json:\"url\"")
}

struct TimeLineEventPublishVersionRerunData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
}

struct TimeLineEventMRRerunData{

}

struct TimeLineEventIntegrationRerunData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional string JobID (go.tag="json:\"job_id\"")
    3: optional string URL (go.tag="json:\"url\"")
}

struct TimeLineEventPublishVersionEndData {
    1: optional string State (go.tag="json:\"state\"")
    2: optional string ProjectName (go.tag="json:\"project_name\"")
    3: optional string Version (go.tag="json:\"version,omitempty\"")
    4: optional string Reason (go.tag="json:\"reason,omitempty\"")
    5: optional string LogURL (go.tag="json:\"log_url\"")
}

struct TimeLineEventPipelineRerunData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
}

struct TimeLineEventAddReviewersData{
    1: optional bool All (go.tag="json:\"all\""),
    2: optional string ProjectName (go.tag="json:\"project_name\""),
    3: optional list<string> RDReviewers (go.tag="json:\"rd_reviewers\""),
    4: optional list<string> QAReviewers (go.tag="json:\"qa_reviewers\"")
}

struct TimeLineEventIntegrationEndData {
    1: optional string State (go.tag="json:\"state\"")
    2: optional string ProjectName (go.tag="json:\"project_name\"")
    3: optional string LogURL (go.tag="json:\"log_url\"")
}

struct TimeLineEventCancelApprovalData{}

struct TimeLineDataMRDependency{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional string TargetBranch (go.tag="json:\"target_branch\"")
    3: optional string SourceBranch (go.tag="json:\"source_branch\"")
}

struct TimeLineEventCloseMRData {
    1: optional list<TimeLineDataMRDependency> MRDependencies (go.tag="json:\"mr_dependencies\"")
    2: optional TimeLineDataMRDependency Host (go.tag="json:\"host\"")
}

struct TimeLineEventIntegrationStartData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
}

struct TimeLineEventPublishVersionStartData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
}

struct TimeLineEventDetectConflictsData{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
}

struct TimeLineEventRemoveReviewersData{
    1: optional bool All (go.tag="json:\"all\"")
    2: optional string ProjectName (go.tag="json:\"project_name\"")
    3: optional list<string> RDReviewers (go.tag="json:\"rd_reviewers\"")
    4: optional list<string> QAReviewers (go.tag="json:\"qa_reviewers\"")
}

struct TimeLineEventAssignReviewersItem{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional list<string> QAReviewers (go.tag="json:\"rd_reviewers\"")
    3: optional list<string> RDReviewers (go.tag="json:\"qa_reviewers\"")
}

struct TimeLineDataPodInfo{
    1: required string TagsDetail (go.tag="json:\"tagsDetail\""),
    2: required string DeprecatedInfo (go.tag="json:\"deprecatedInfo\""),
    3: required string OperateUser (go.tag="json:\"operateUser\""),
    4: required string BaseVersion (go.tag="json:\"baseVersion\""),
    5: required string BuildFrom (go.tag="json:\"buildFrom\""),
    6: required string Flavor (go.tag="json:\"flavor\""),
    7: required string DetailStatus (go.tag="json:\"detailStatus\""),
    8: required string CommitId (go.tag="json:\"commitId\""),
    9: required string BuildResult (go.tag="json:\"buildResult\""),
    10: required i64 Id (go.tag="json:\"id\""),
    11: required string SizeFramework (go.tag="json:\"sizeFramework\""),
    12: required string Strict (go.tag="json:\"strict\""),
    13: required string Version (go.tag="json:\"version\""),
    14: required i64 WorkflowJobId (go.tag="json:\"workflow_job_id\""),
    15: required string RepoName (go.tag="json:\"repoName\""),
    16: required string Branch (go.tag="json:\"branch\""),
    17: required i64 GitlabMrId (go.tag="json:\"gitlabMrId\""),
    18: required i64 RepoId (go.tag="json:\"repoId\""),
    19: required string Subspecs (go.tag="json:\"subspecs\""),
    20: required string LogURL (go.tag="json:\"logURL\""),
    21: required string BusExtInfo (go.tag="json:\"busExtInfo\""),
    22: required string ChangeLog (go.tag="json:\"changeLog\""),
    23: required string NeedFramework (go.tag="json:\"needFramework\""),
    24: required string TaskId (go.tag="json:\"taskId\""),
    25: required string AppId (go.tag="json:\"appId\""),
    26: required string IosExtInfo (go.tag="json:\"iosExtInfo\""),
    27: required string TaskUrl (go.tag="json:\"taskUrl\""),
    28: required string AndroidExtInfo (go.tag="json:\"androidExtInfo\""),
    29: required string CreatTime (go.tag="json:\"creatTime\""),
    30: required i64 BitsUpgradeId (go.tag="json:\"bits_upgrade_id\""),
    31: required string OptimusResponse (go.tag="json:\"optimus_response\""),
    32: required string RepoGroupName (go.tag="json:\"repoGroupName\""),
    33: required string SemVersion (go.tag="json:\"semVersion\""),
}

struct TimeLineDataVersionDependency{
    1: optional TimeLineDataPodInfo PodInfo (go.tag="json:\"pod_info\""),
    2: optional string  PodSource (go.tag="json:\"pod_source\""),
    3: optional string  PodVersion (go.tag="json:\"pod_version\""),
    4: optional i64     Type (go.tag="json:\"type\""),
    5: optional string  PodName (go.tag="json:\"pod_name\""),
}

struct TimeLineEventCreateMRData{
    1: optional list<TimeLineDataMRDependency> MRDependencies (go.tag="json:\"mr_dependencies\"")
    2: optional list<TimeLineDataVersionDependency> VersionDependencies (go.tag="json:\"version_dependencies\"")
    3: optional TimeLineDataMRDependency Host (go.tag="json:\"host\"")
}

struct TimeLineJobs{
    1: optional string JobUrl (go.tag="json:\"job_url\"")
    2: optional string JobName (go.tag="json:\"job_name\"")
}

struct TimeLineEventPipelineEndData{
    1: optional string URL (go.tag="json:\"url\"")
    2: optional i64 PipelineID (go.tag="json:\"pipeline_id\"")
    3: optional string  State (go.tag="json:\"state\"")
    4: optional string ProjectName (go.tag="json:\"project_name\"")
    5: optional list<TimeLineJobs> FailedJobs (go.tag="json:\"failed_jobs,omitempty\"")
}

struct TimeLineProject{
    1: optional string URL (go.tag="json:\"url\"")
    2: optional i64 PipelineID (go.tag="json:\"pipeline_id\"")
    3: optional string ProjectName (go.tag="json:\"project_name\"")
}

struct TimeLineEventPipelineStartData{
    1: optional string Source (go.tag="json:\"source\"")
    2: optional list<TimeLineProject> Projects (go.tag="json:\"projects\"")
}

struct TimeLineCommitData{
    1: optional string URL (go.tag="json:\"url\"")
    2: optional string SHA (go.tag="json:\"sha\"")
    3: optional string Message (go.tag="json:\"message\"")
}

struct TimeLineEventPushCommitsData{
    1: optional list<TimeLineCommitData> Commits (go.tag="json:\"commits\"")
    2: optional string ProjectName (go.tag="json:\"project_name\"")
}

struct TimeLineEventMergeMRItem{
    1: optional string ProjectName (go.tag="json:\"project_name\"")
    2: optional string TargetBranch (go.tag="json:\"target_branch\"")
    3: optional string SourceBranch (go.tag="json:\"source_branch\"")
    4: optional string Message (go.tag="json:\"message\"")
    5: optional string Status (go.tag="json:\"status\"")
}

struct TimeLineEventAppData{
    1: optional string URL (go.tag="json:\"url\"")
    2: optional string SHA (go.tag="json:\"sha\"")
}

struct TimeLineEventReviewerApproveItem{
    1: optional string Key (go.tag="json:\"key\"")
    2: optional TimeLineEventAppData Value (go.tag="json:\"value\"")
}

struct TimeLineEventReviewComment{
    1: optional string comment (go.tag="json:\"comment\"")
    2: optional list<i64> gitlabCommentIDs (go.tag="json:\"gitlab_comment_ids\"")
    3: optional string action (go.tag="json:\"action\"")
    4: optional i64 patchsetNum (go.tag="json:\"patchset_num\"")
    5: optional i64 commentID (go.tag="json:\"comment_id\"")
    6: optional i64 replyToID (go.tag="json:\"reply_to_id\"")
}

struct TimeLineEventCreateContribution{
}

struct TimeLineEventCloseContribution{
}

struct TimeLineEventMergeContribution{
}

struct TimeLineEventEditContribution{
}

struct TimeLineEventDevAddReviewer{
    1: required string reviewer (go.tag="json:\"reviewer\"")
    2: required string operator (go.tag="json:\"operator\"")
}

struct TimeLineEventDevRemoveReviewer{
    1: required string reviewer (go.tag="json:\"reviewer\"")
    2: required string operator (go.tag="json:\"operator\"")
}

struct TimeLineEventDevResetReviewer{
    1: required string reviewer (go.tag="json:\"old_reviewer\"")
}

struct TimeLinetEventDevReplaceReviewer{
    1: required string oldReviewer (go.tag="json:\"old_reviewer\"")
    2: required string newReviewer (go.tag="json:\"new_reviewer\"")
    3: required string operator (go.tag="json:\"operator\"")
}

struct TimeLinetEventSubmitCommits{
}

struct TimeLineEventData{
    1: optional TimeLineEventCreateMRData CreateMR (go.tag="json:\"create_mr\"")
    2: optional string EditMR (go.tag="json:\"edit_mr\"")
    3: optional TimeLineEventCloseMRData CloseMR (go.tag="json:\"close_mr\"")
    4: optional list<TimeLineEventMergeMRItem> MergeMR (go.tag="json:\"merge_mr\"")
    5: optional TimeLineEventPushCommitsData PushCommits (go.tag="json:\"push_commits\"")
    6: optional TimeLineEventForceMergeData ForceMerge (go.tag="json:\"force_merge\"")
    7: optional TimeLineEventMergeTargetData MergeTarget (go.tag="json:\"merge_target\"")
    8: optional list<TimeLineEventAssignReviewersItem> AssignReviewers (go.tag="json:\"assign_reviewers\"")
    9: optional TimeLineEventChangeReviewersData ChangeReviewers (go.tag="json:\"change_reviewers\"")
    10: optional TimeLineEventAddReviewersData AddReviewers (go.tag="json:\"add_reviewers\"")
    11: optional TimeLineEventRemoveReviewersData RemoveReviewers (go.tag="json:\"remove_reviewers\"")
    12: optional list<TimeLineEventReviewerApproveItem> ReviewerApprove (go.tag="json:\"reviewer_approve\"")
    13: optional TimeLineEventCancelApprovalData CancelApproval (go.tag="json:\"cancel_approval\"")
    14: optional TimeLineEventPipelineTriggerData PipelineTrigger (go.tag="json:\"pipeline_trigger\"")
    15: optional TimeLineEventPipelineStartData PipelineStart (go.tag="json:\"pipeline_start\"")
    16: optional TimeLineEventPipelineEndData PipelineEnd (go.tag="json:\"pipeline_end\"")
    17: optional TimeLineEventMRRerunData MRRerun (go.tag="json:\"mr_rerun\"")
    18: optional TimeLineEventPipelineRerunData PipelineRerun (go.tag="json:\"pipeline_rerun\"")
    19: optional TimeLineEventJobRerunData JobRerun (go.tag="json:\"job_rerun\"")
    20: optional TimeLineEventPublishVersionRerunData PublishVersionRerun (go.tag="json:\"publish_version_rerun\"")
    21: optional TimeLineEventIntegrationRerunData IntegrationRerun (go.tag="json:\"integration_rerun\"")
    22: optional TimeLineEventDetectConflictsData DetectConflicts (go.tag="json:\"detect_conflicts\"")
    23: optional TimeLineEventPublishVersionStartData PublishVersionStart (go.tag="json:\"publish_version_start\"")
    24: optional TimeLineEventPublishVersionEndData PublishVersionEnd (go.tag="json:\"publish_version_end\"")
    25: optional TimeLineEventIntegrationStartData IntegrationStart (go.tag="json:\"integration_start\"")
    26: optional TimeLineEventIntegrationEndData IntegrationEnd (go.tag="json:\"integration_end\"")
    27: optional TimeLineEventRemoveEmptyMRData RemoveEmptyMR (go.tag="json:\"remove_empty_mr\"")
    28: optional TimeLineEventResolvePodspecConflictData ResolvePodspecConflict (go.tag="json:\"resolve_podspec_conflict\"")
    29: optional list<TimeLineEventCodebaseTaskStartItem> CodebaseTaskStart (go.tag="json:\"codebase_task_start\"")
    30: optional list<TimeLineEventCodebaseTaskEndItem> CodebaseTaskEnd (go.tag="json:\"codebase_task_end\"")
    31: optional string ThirdEvent (go.tag="json:\"third_event\"")
    32: optional list<TimeLineEventThirdPartyCheckTaskStartOrRerunItem> ThirdPartyCheckTaskStart (go.tag="json:\"third_party_check_task_start\"")
    33: optional list<TimeLineEventThirdPartyCheckTaskStartOrRerunItem> ThirdPartyCheckTaskRerun (go.tag="json:\"third_party_check_task_rerun\"")
    34: optional list<TimeLineEventThirdPartyCheckTaskCheckEndItem> ThirdPartyCheckTaskCheckEnd (go.tag="json:\"third_party_check_task_check_end\"")
    35: optional TimeLineEventJobSkipData JobSkip (go.tag="json:\"job_skip\"")
    36: optional TimeLineEventBuildPackageData BuildPackage (go.tag="json:\"build_package\"")
    37: optional string CommentEvent (go.tag="json:\"comment_event\"")
    38: optional string TimeLineEventOpenAPIData (go.tag="json:\"open_api\"")
    39: optional string TimeLineEventRichTextComment (go.tag="json:\"rich_text_comment\"")
    40: optional TimeLineEventReviewComment ReviewComment (go.tag="json:\"review_comment\"")
    41: optional TimeLineEventCreateContribution CreateContribution (go.tag="json:\"create_contribution\"")
    42: optional TimeLineEventCloseContribution CloseContribution (go.tag="json:\"close_contribution\"")
    43: optional TimeLineEventMergeContribution MergeContribution (go.tag="json:\"merge_contribution\"")
    44: optional TimeLineEventEditContribution EditContribution (go.tag="json:\"edit_contribution\"")
    45: optional TimeLineEventDevAddReviewer DevAddReviewer (go.tag="json:\"dev_add_reviewer\"")
    46: optional TimeLineEventDevRemoveReviewer DevRemoveReviewer (go.tag="json:\"dev_remove_reviewer\"")
    47: optional TimeLineEventDevResetReviewer DevResetReviewer (go.tag="json:\"dev_reset_reviewer\"")
    48: optional TimeLinetEventDevReplaceReviewer DevReplaceReviewer (go.tag="json:\"dev_replace_reviewer\"")
    49: optional TimeLinetEventSubmitCommits SubmitCommits(go.tag="json:\"submit_commits\"")
}


struct GetMrTimelineInfoByMrIDQuery {
    1: required i64 mrID
    2: optional i64 pageSize
    3: optional i64 pageNum
    4: optional i64 devID
    255: optional base.Base Base
}

struct TimelineEvent{
    1: required i64 ID
    2: required TimeLineEventData Data
    3: required i64 MrID
    4: required i64 CreateTime
    5: required i64 UpdateTime
    6: required string EventType
    7: required string Operator
    8: optional i64 projectID
    9: optional i64 iID
    10: optional string Level
    11: optional i64 devID
}

struct GetMrTimeLineEventDataByMrIDResponse {
    1: optional list<TimelineEvent> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp;
}

struct GetMrTimelineInfoByMrIDResponse {
    1: optional list<TimelineInfo> List (go.tag = "json:\"list\"")
    2: optional i64 count,
    255: required base.BaseResp BaseResp;
}

struct GetDevTimelineInfoByDevIDQuery{
    1: required i64 devID
    2: optional i64 pageSize
    3: optional i64 pageNum
    4: optional i64 mrID
    255: optional base.Base Base
}
struct GetDevTimeLineEventDataByDevIDResponse{
     1: optional list<TimelineEvent> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp
}

struct GraphIntegrationNodePopoverItemProgressNodeRetryBodyData {
    1: required i64 ProjectID
    2: required i64 JobID
}
struct GraphIntegrationNodePopoverItemProgressNodeRetryBody {
    1: required GraphIntegrationNodePopoverItemProgressNodeRetryBodyData Data
    2: required string Type
}
struct GraphIntegrationNodePopoverItemProgressNode{
    1: required i64 ID
    2: required string ErrMsg
    3: required string JobID
    4: required string Label
    5: required GraphIntegrationNodePopoverItemProgressNodeRetryBody RetryBody
    6: required string RetryURL
    7: required string Stage
    8: required string Status
    10: required string Type
    11: required string URL
}
struct GraphIntegrationNodePopoverItemRetryBodyData {
    1: required i64 MrIID
    2: required string ProjectID
}
struct GraphIntegrationNodePopoverItemRetryBody {
    1: optional string Type
    2: optional GraphIntegrationNodePopoverItemRetryBodyData Data
    3: optional string Action
}
struct GraphIntegrationNodePopoverItem {
    1: required string Label
    2: required string Status
    3: required string Type
    4: required list<Reviewer> Reviewers
    5: optional i64 EstimatedDuration
    6: optional string Message
    7: optional list<GraphIntegrationNodePopoverItemProgressNode> ProgressNodes
    8: optional GraphIntegrationNodePopoverItemRetryBody RetryBody
    9: optional string RetryURL
    10: optional i64 StartTime
    11: optional string URL
}
struct GraphIntegrationProcessNode {
    1: required string Label
    2: required string NodeCommonStatus
    3: required string NodeType
    4: optional list<GraphIntegrationNodePopoverItem> ProcessNodes
}
struct GraphDependencyInfo  {
    1: required i64 IID
    2: required i64 ProjectID
    3: required string ProjectName
    4: required list<GraphIntegrationProcessNode> ProgressNodes
}
struct GraphIntegration {
    1: required list<GraphIntegrationProcessNode> ProcessNodes
}
struct GraphCommonMrsPipelineInfo {
    1: required list<GraphDependencyInfo> MrDependencies
    2: required list<GraphDependencyInfo> VersionDependencies
    3: required GraphDependencyInfo MrDependency
    4: required GraphDependencyInfo Host
    5: required GraphIntegration Integration
}
struct GetGraphPipelineInfoQuery {
    1: required i64 MrID
    2: optional i64 devID
}
struct GetGraphPipelineInfoResponse {
    1: required list<GraphCommonMrsPipelineInfo> CommonMrs
    2: required list<GraphCommonMrsPipelineInfo> MultiMrs
    255: required base.BaseResp BaseResp;
}

struct GetMrIDsByProjectAndVersionQuery {
    1: required i64 projetc_id, // Gitlab 仓库 ID
    2: required string version, // 主仓版本号

    255: required base.Base Base
}

struct GetMrIDsByProjectAndVersionResponse {
    1: required list<i64> mr_ids,

    255: required base.BaseResp BaseResp;
}

struct MrSimpleStatusInfo {
    1: required i64 id
    2: required MrState status
}

struct GetMrSimpleInfosByProjectAndVersionQuery {
    1: required i64 projetc_id, // Gitlab 仓库 ID
    2: required string version, // 主仓版本号
    3: string tags

    255: required base.Base Base
}

struct GetMrSimpleInfosByProjectAndVersionResponse {
    1: required list<MrSimpleStatusInfo> mr_simple_infos,

    255: required base.BaseResp BaseResp;
}

// 判断一个用户对一个mr review相关的权限
struct GetReviewPermissionsQuery {
    1: required i64 MrID
    2: required string actionUsername
    3: optional i64 devID
    255: optional base.Base Base
}
struct GetReviewPermissionsInfo {
    1: required bool CanClose
    2: required bool CanDowngradeMr
    3: required bool CanForceMerge
    4: required bool CanMergeBase
    5: required bool CanRetry
    6: required bool CanUpgradeMr
    7: required bool ShowOk
    8: required bool ShowCancelOk
    9: required string status
    10: optional bool CanFixConflict
    11: optional bool CanSkipReview
    12: list<string> ExtraForceMergeAdmins
}
struct GetReviewPermissionsResponse {
    1: required GetReviewPermissionsInfo info
    255: required base.BaseResp BaseResp;
}
// 获取项目的配置
struct GetConfigsByProjectNameQuery {
    1: required string GroupName
    2: required i16 flag
    3: optional bool filterInDirectHost // 是不是过滤掉间接关联的仓库的主仓 true 过滤 false 不过滤
    255: optional base.Base Base
}
struct GetConfigsByProjectNameInfo {
    1: required bool afterCheckEnable
    2: required string afterCheckType
    3: required i16 appType
    4: required bool autoDeleteMergedBranches
    5: required bool autoDeleteOldBranches
    6: required bool autoPublish
    7: required i16 autoPublishType
    8: required bool autoUpdateSeer
    9: required i16 binaryType
    10: required i64 buildPackageTemplateID
    11: required i64 codebaseRepoID
    12: required string conflictDetectLevel
    13: required string conySettings
    14: required bool createMrGroup
    15: required string customAfterCheckUrl
    16: required string customCiVars
    17: required string customizedReview
    18: required string dependencyLock
    19: required bool disableOutDatedMerge
    20: required bool enableTranslationCheck
    21: required string fileReviewerMap
    22: required bool flutterModule
    23: required string geckoAppProjects
    24: required string geckoPath
    25: required string gitUrl
    26: required string gitlabGroup
    27: required string greyBranchPattern
    28: required string integrationFilePath
    29: required i16 integrationType
    30: required bool isActived
    31: required bool isBizPod
    32: required string jenkins
    33: required bool jobAnalysisEnable
    34: required string mainGroup
    35: required string monitoredBranchPattern
    36: required string mrControlUrl
    37: required bool multiComponent
    38: required i64 patchTemplateID
    39: required string pipelineForBranch
    40: required i64 pipelineTemplateID
    41: required string pipelineToken
    42: required string pipelineTriggerTiming
    43: required i16 pipelineType
    44: required bool podChangeValidateEnable
    45: required string podFile
    46: required string podspecName
    47: required i64 projectID
    48: required string projectName
    49: required string projectShowName
    50: required string rcBranchSettings
    51: required i64 reviewerNumber
    52: required string reviewers
    53: required string rockBinarySource
    54: required bool rootProject
    55: required i64 scmID
    56: required bool skipPipeline
    57: required string skipPublicCommentsRegex
    58: required string sourceUrl
    59: required string versionBranchPattern
    60: required string xcodeProjectPath
    61: required i64 ID
    62: optional bool customComponentVersion
    63: optional i16 podSeerEnable
    65: optional bool podChangePipelineEnable
    66: optional i64 bitsPipelineConfigId
}
struct GetConfigsByProjectNameResponse{
    1: required list<GetConfigsByProjectNameInfo> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp;
}
// 创建mr支持绑定slardar的任务
struct GetUserSlardarTaskQuery{
    1: required i64 appID
    2: required string platform
    3: required string username
    4: optional string region
    5: optional i64 page
    6: optional i64 pageSize
    255: required base.Base Base
}
struct SlardarTaskInfoLabels {
    1: required i64 ID
    2: required string key
    3: required string value
}
struct SlardarTaskInfo{
    1: required i64 ID
    2: required string crashFile
    3: required string crashException
    4: required string crashReason
    5: required string issueID
    6: required string title
    7: required string eventDetail
    8: optional list<SlardarTaskInfoLabels> labels
    9: optional string crashType
    10: optional string crashTypeName
    11: optional string status
    12: optional string moduleName
    13: optional i64 moduleID
    14: optional string detailURL
    15: optional string region
}
struct GetUserSlardarTaskResponse{
    1: required list<SlardarTaskInfo> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp
}
// mr版本发布信息
struct MrComponentInfo {
	1: required i64 ID
	2: required string name
	3: required string groupName
	4: required string moduleName
	5: required i16 techType
    6: optional string repoAddr
    7: optional i64 componentID
}
struct MrComponentPublishInfo {
    1: required i64 ID
    2: required string type
    3: required string versionFinal
    4: required string versionOrigin
    5: required string status
    6: required i64 publishVersionID
    7: required string logURL
    8: required MrComponentInfo componentInfo
    9: required i64 parentMrID
    10: optional string versionType
    11: optional i64 projectID
    12: optional string versionBase
    13: optional string componentVersionBase
    14: optional i64 lastPipelineID
    15: optional string podSource
}
struct GetMrComponentPublishInfoQuery{
    1: required i64 mrID
    2: optional i64 devID
    255: required base.Base Base
}
struct GetMrComponentPublishInfoResponse {
    1: required list<MrComponentPublishInfo> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp
}
// 获取主MR的ID
struct HostMrIDInfo {
    1: required i64 mrID
    2: required i64 projectID
    3: required i64 iID
    4: optional string gitRepoAddr
    5: optional string groupName
    6: optional i64 devID
}

struct GetHostMrIDQuery {
    1: required i64 mrID
    2: optional i64 projectID
    3: optional i64 iID
    4: optional i64 devID
    255: required base.Base Base
}

struct GetHostMrIDResponse {
    1: required HostMrIDInfo info
    255: required base.BaseResp BaseResp
}
// 获取用户信息
struct GetUserInfoQuery {
    1: required string enName
    255: required base.Base Base
}
struct GetUserInfoResponse {
    1: required User info
    255: required base.BaseResp BaseResp
}
// 获取MRcommits信息
struct GetMrCommitsInfoQuery {
    1: required i64 mrID
    2: required i64 projectID
    3: required i32 page
    4: required i32 pageSize
    5: optional string type
    6: optional string podName
    7: optional i64 devID
    255: required base.Base Base
}
struct CommitInfo {
    1: required string commitID
    2: required string commitMessage
    3: required i64 createTime
    4: required string gitlabUrl
    5: required i64 projectID
    6: required i64 repoID
    7: required string repoName
    8: required string authorName
}
struct GetMrCommitsInfoData {
    1: required list<CommitInfo> List (go.tag = "json:\"list\"")
    2: required i64 total
}
struct GetMrCommitsInfoResponse {
    1: required GetMrCommitsInfoData Data
    255: required base.BaseResp BaseResp
}
// 获取MR branch上的commit变更
struct GetMrBranchCommitStateQuery {
    1: required i64 mrID
    2: optional i64 devID
    255: required base.Base Base
}
struct MrBranchCommitsStateDetail {
    1: required bool conflicted
    2: required bool disableOutdatedMerge
    3: required bool emptyContent
    4: required bool forbidEmptyContentMerge
    5: required i64 iID
    6: required string mergeTargetStatus
    7: required i64 newCommitsInTarget
    8: required i64 projectID
}
struct GetMrBranchCommitStateResponse {
    1: required list<MrBranchCommitsStateDetail> info
    255: required base.BaseResp BaseResp
}


// feature_info
struct FeatureInfo {
    1: required list<JiraFeatureInfo> jira
    2: required list<SlardarFeatureInfo> slardar
    3: required list<MeegoAndRocketFeatureInfo> meego
    4: required list<MeegoAndRocketFeatureInfo> rocket
    5: required string type
    6: optional list<MeegoBugInfo> meegoBug
}
struct GetMrFeatureInfoQuery {
    1: required i64 MrID
    2: optional i64 devID
    255: required base.Base Base
}
struct GetMrFeatureInfoResponse {
    1: required FeatureInfo info
    255: required base.BaseResp BaseResp
}

struct GetMrListBySearchQuery {
    1: optional string groupName
    2: optional string source
    3: required i64 lastID
    4: optional i64 appID
    5: optional i16 wip
    6: optional i16 conflicted
    7: optional i64 targetProjectID
    8: optional string targetBranch
    9: optional string mrType
    10: optional string sourceBranch
    11: optional string keyword
    13: optional i32 count
    14: optional string authorName
    15: optional string state
    16: optional string reviewerName
    17: optional i64 limit
    18: optional string thirdPlatform
    19: optional string platformID
    20: optional string productVersion
    21: optional string sort
    22: optional string order
    23: optional string tags
    24: optional TimeRangeFilter createdTime
    25: optional TimeRangeFilter mergedTime
    26: optional i64 from // 分页使用, skip 多少个
    27: optional i32 businessType
    28: optional string childTargetBranch
    29: bool withTotal
    30: optional string reviewState
    31: optional list<i32> businessTypes
    255: optional base.Base Base
}
struct GetMrListBySearchResponse {
    1: optional list<i64> List (go.tag = "json:\"list\"")
    2: optional i64 total
    255: required base.BaseResp BaseResp;
}
// 获取搜索总数
struct GetMrListBySearchTotalResponse {
    1: required  i64 total,
    255: required base.BaseResp BaseResp;
}
// 获取mr的仓库信息
struct GetMrRepoInfoQuery{
    1: required i64 mrID
    2: optional i64 devID
    255: optional base.Base Base
}
struct GetMrRepoInfoResponse {
    1: required i64 projectID
    2: required i64 iID
    3: required string RepoAddr
    255: required base.BaseResp BaseResp;
}
// 删除群聊里的用户
struct RemoveUserFromChatQuery {
    1: required i64 mrID
    2: required string username
    3: optional i64 devID
    255: optional base.Base Base
}
struct RemoveUserFromChatResponse {
    255: required base.BaseResp BaseResp;
}
// 拉指定用户进群
struct InviteUserJoinLarkGroupQuery {
    1: required string username
    2: required string chatId
    3: optional i64 devID

    255: required base.Base Base,
}
struct InviteUserJoinLarkGroupResponse {
    255: required base.BaseResp BaseResp,
}
// 创建群聊
struct CreateLarkGroupQuery {
    1: required i64 bitsMRID
    2: required string actionUsername
    3: optional i64 devID
    255: optional base.Base Base,
}
struct CreateLarkGroupResponse {
    1: required string chatID
    255: optional base.BaseResp BaseResp
}
// 解散群聊
struct DismissLarkGroupQuery {
    1: required i64 bitsMRID
    2: required string actionUsername
    3: optional i64 devID
}
struct DismissLarkGroupResponse {
    255: optional base.BaseResp BaseResp,
}


// workflow and enigne
struct EngineEndCallbackQuery {
    1: required i64 id,
    2: required string status,
    3: required i64 byteflow_execution, // 使用前需要转uint64
    4: required i64  host_mr_id,
    5: required string type,
    6: required string context,
    7: required string params,
    8: required string created_at, // time.Time形如2021-02-20T18:28:50.379121+08:00
    9: required string updated_at,
    10: required string operated_at,
    255: optional base.Base Base
}

struct NoneResponse {
    255: optional base.BaseResp BaseResp;
}

struct WorkflowTaskTimeoutAlarmQuery {
    1: required WorkflowTaskTimeoutAlarmQueryParams params,
}

struct WorkflowTaskTimeoutAlarmQueryParams {
    1: required i64 state_machine_id,
    2: required i64 mr_id,
    3: optional i64 devID,
}
// 获取MR block信息
struct MrBlockErrorResolveDetail {
    1: required string docURL,
    2: required string larkID,
    3: optional string larkType,
    4: optional string oncallTitle,
    5: optional string linkTitle,
}
struct MrBlockErrorResolveInfo {
    1: required string name,
    2: required string zhTitle,
    3: required string enTitle,
    4: required list<MrBlockErrorResolveDetail> resolves,
    5: required i16 level,
    6: optional bool showOncall,
    7: optional bool showDoc,
}
struct GetMrBlockErrorInfoQuery {
    1: required i64 mr_id,
    2: optional string actionUsername,
    3: optional i64 devID,
    255: optional base.Base Base
}
struct MrBlockErrorDetail {
    1: required string name
	2: required string title
	3: required string message
	4: required string level
    5: required string eventType
    6: required i64 mrID
}
struct MrBlockError {
    1: required string category
    2: required list<MrBlockErrorDetail> List (go.tag = "json:\"list\"")
    3: required i16 level
    4: optional MrBlockErrorResolveInfo resolve

}
struct GetMrBlockErrorInfoResponse {
    1: required list<MrBlockError> List (go.tag = "json:\"list\"")
    255: optional base.BaseResp BaseResp;
}
struct CreateMrBlockErrorInfoQuery {
    1: required string zhTitle
    2: required string enTitle
    3: required string zhMessage
    4: required string enMessage
    5: required string larkID
    6: required string docURL
    7: required string level
    8: required string author
    9: required i64 mrID
    255: optional base.Base Base
}
struct CreateMrBlockErrorInfoResponse {
    1: required i64 ID
    255: optional base.BaseResp BaseResp
}

struct CreateTimelineEventQuery{
    1: required string Data
    2: required i64 MrID
    3: required string EventType
    4: required string Level
    5: optional string Operator
    6: optional i64 DevID
    255: optional base.Base Base
}

struct CreateTimelineEventResponse{
    1: required i64 ID
    2: required TimeLineEventData Data
    3: required i64 MrID
    4: required i64 CreateTime
    5: required i64 UpdateTime
    6: required string EventType
    7: required string Operator
    8: optional i64 projectID
    9: optional i64 iID
    10: optional string Level
    11: optional i64 DevID
    255: optional base.BaseResp BaseResp
}

struct StoreErrorRequest {
    1: required string task_name,
    2: required i64 host_id,
    3: required string err_name,
    4: required string err_level,
    5: required string err_msg,
    6: required i64 state_machine_id,
    7: required string group_name,
    255: optional base.Base Base
}
// 用户点解解决方案
struct BlockErrorResolveClickQuery {
    1: required string actionUsername
    2: required string category
    3: required i64 mrID
    255: optional base.Base Base
}
struct BlockErrorResolveClickResponse {
    1: required string larkID
    2: required string larkType
    255: optional base.BaseResp BaseResp
}

// 获取MR的集成状态
struct GetMrIntegrationStateQuery {
    1: required i64 mrID
    2: optional i64 devID
    255: optional base.Base Base
}
struct GetMrIntegrationStateResponse {
    1: required bool currentState // false 不可集成, true可集成
    255: optional base.BaseResp BaseResp;
}

// 请求获取app封版信息
struct GetAppReleaseVersionInfoQuery {
    1: required string groupName    // 业务线
    2: required string version      // 版本
    255: optional base.Base Base
}

struct GetAppReleaseVersionInfoResponse {
    1: required i64 releaseId,                  // 封版id
    2: required AppReleaseStatus status,        // 封版状态
    3: required string blockReason,             // 阻塞信息
    4: required i64 startIntegraionTime,        // 开始集成时间
    5: required i64 startVersionCloseTime,      // 开始封版时间
    6: required i64 dismissVersionCloseTime,    // 触发封版结束事件时间
    7: required i64 endVersionCloseTime,        // 封版结束时间
    8: required string currentVersion,          // 正在集成的版本
    9: optional list<i64> pre_boarding_mrs,     // 预上车mr
    10: optional bool manual_confirm,           // 准入人工确认
    11: optional bool ticket_finished,          // 颁发车票任务结束
    12: optional string release_version,        //当前记录的版本
    255: optional base.BaseResp BaseResp;
}

struct GetMrVersionDependencyTotalQuery{
    1: required list<Version> deps
    2: required i64 mrID
    3: required i64 projcetID
    4: required i64 IID
    5: optional i64 devID
    255: optional base.Base Base
}

struct VersionDependencyTotal{
    1: required i64 projectID
    2: required string groupName
    3: required string projectName
    4: required i64 total
    5: optional string oldVersion
    6: optional string latestVersion
}

struct GetMrVersionDependencyTotalResponse{
    1: required list<VersionDependencyTotal> List (go.tag = "json:\"list\"")
    255: optional base.BaseResp BaseResp;
}

struct CreateMrTagsQuery{
    1: required string name
    2: required string color
    3: required string catalog
    4: required string groupProjectName
    5: required string creator
    6: optional string enName
    255: optional base.Base Base
}

struct CreateMrTagsResponse{
    1: required i64 id
    2: required string name
    3: required string color
    4: required string catalog
    5: required string groupProjectName
    6: required string creator
    7: required i64 createAt
    8: required i64 updateAt
    9: optional string enName
    255: optional base.BaseResp BaseResp;
}

struct DeleteMrTagsQuery{
    1: optional i64 id
    2: optional string name
    3: optional string projectName
    255: optional base.Base Base
}

struct DeleteMrTagResponse{
    255: optional base.BaseResp BaseResp;
}

struct BindMrTagQuery{
    1: required i64 mrID
    2: required string tagName
    3: optional i64 devID
    4: optional string groupName
    5: optional string reason
    255: optional base.Base Base
}

struct BindMrTagResponse{
    255: optional base.BaseResp BaseResp;
}

struct ListMrTagQuery{
    1: required string groupName
    2: optional list<string> catalogs
}

struct ListMrTagResponse{
    1: required list<MrTag> List (go.tag = "json:\"list\"")
    255: required base.BaseResp BaseResp;
}

// WebHook相关业务逻辑
struct WebHookEvent {
    1: required i64 ID
    2: required string name
    3: required string title
    4: required string params
    5: required string descriptions
    6: required WebHookCategory category
}
struct WebHookCategory {
    1: required i64 ID
    2: required string name
    3: required string title
}
struct WebHookSubscribeRecord {
    1: required i64 ID
    2: required string author
    3: required string groupName
    4: required string URL
    5: required WebHookEvent event
}
// 获取event
struct GetWebHookEventsQuery {
    1: optional i64 categoryID
    2: required i32 page
    3: required i32 pageSize
    255: optional base.Base Base
}
struct GetWebHookEventsResponse {
    1: required list<WebHookEvent> List (go.tag = "json:\"list\"")
    255: optional base.BaseResp BaseResp;
}
// 校验 webhook 地址是否合法
struct CheckWebhookURLQuery {
    1: required string URL
    255: optional base.Base Base
}
struct CheckWebhookURLResponse {
    255: optional base.BaseResp BaseResp;
}
// 获取所有category
struct GetWebHookCategoriesQuery {
    1: required i32 page
    2: required i32 pageSize
    255: optional base.Base Base
}
struct GetWebHookCatetgoriesResponse {
    1: required list<WebHookCategory> List (go.tag = "json:\"list\"")
    255: optional base.BaseResp BaseResp;
}
// 增加订阅
struct CreateWebHookSubscribeRecordQuery {
    1: required string actionUsername
    2: required string groupName
    3: required string URL
    4: required i64 eventID
}
struct CreateWebHookSubscribeRecordResponse {
    1: required i64 insertID
    255: optional base.BaseResp BaseResp;
}
// 产看订阅
struct GetWebHookSubscribeRecordQuery {
    1: required string groupName
    2: required i32 page
    3: required i32 pageSize
    4: optional i64 categoryID
    5: optional i64 eventID
    255: optional base.Base Base
}
struct GetWebHookSubscribeRecordResponse {
    1: required list<WebHookSubscribeRecord> List (go.tag = "json:\"list\"")
    2: required i64 total
    255: optional base.BaseResp BaseResp;
}
// 取消订阅
struct RemoveWebHookSubscribeRecordQuery {
    1: required i64 recordID
    2: required string actionUsername
    255: optional base.Base Base
}
struct RemoveWebHookSubscribeRecordResponse {
    255: optional base.BaseResp BaseResp;
}
// 通过chat_id获取mr的ID
struct GetMrIDByChatIDQuery {
    1: required string chatID
    255: optional base.Base Base
}
struct GetMrIDByChatIDResponse {
    1: required i64 mrID
    2: optional i64 devID
    255: optional base.BaseResp BaseResp;
}
// 获取仓库在gitlab上的状态
struct RepoStatusOnGitlabDetail {
    1: required i64 mrID
    2: required i64 iID
    3: required bool conflicted
    4: required bool forbidEmptyContentMerge
    5: required bool disableOutdatedMerge
    6: required string mergeTargetStatus
    7: required i64 projectID
    8: required bool emptyContent
    9: required i32 newCommitsInTarget
    10: required string mergeMethod
    11: optional bool missingComponents
}
struct *********************Query {
    1: required i64 mrID
    2: optional string actionUsername
    3: optional i64 devID
    255: optional base.Base Base
}
struct *********************Response {
    1: required list<RepoStatusOnGitlabDetail> reposStatus
    255: optional base.BaseResp BaseResp;
}
// 获取mr的版本依赖
struct GetVersionDependencyQuery {
    1: optional i64 mrID
    2: optional i64 projectID
    3: optional i64 iID
    4: optional i64 devID
    255: optional base.Base Base
}
struct GetVersionDependencyResponse {
    1: required list<Version> versions
    255: optional base.BaseResp BaseResp;
}

// 构建缓存
struct BuildMrCacheQuery {
    1: required list<i64> mrsID
    2: optional i64 devID
    255: optional base.Base Base
}
struct BuildMrCacheResponse {
}
// 设置缓存失效
struct ResetMrCacheQuery {
    1: required i64 projectID
    2: required i64 iID
    3: optional i64 devID
    255: optional base.Base Base
}
struct ResetMrCacheResponse {
}

// 保密项目权限检查
struct CheckPrivacyGroupPermissionByGitlabMRIDQuery {
    1: required string username
    2: required i64 mrID
    255: optional base.Base Base
}
struct CheckPrivacyGroupPermissionByGitlabMRIDResponse {
    1: required bool hasPermission
    255: optional base.BaseResp BaseResp;
}

// 获取空间下集成中的 MR
struct MRQueueInfo {
    1: required i64 mrID
    2: required string mrTitle
    3: required i64 iID
    4: required string mrType
    5: required string author
    6: required string lastCommitID
    7: required i64 duration
    8: required i64 weight
    9: required string targetBranch
}
// 获取空间下合入中的 MR
struct GetGroupMergingMRsQuery {
    1: required string groupName
    255: optional base.Base Base;
}
struct GetGroupMergingMRsResponse {
    1: required list<MRQueueInfo> mrs
    255: optional base.BaseResp BaseResp;
}
// 获取空间下集成中的 MR
struct GetMRIntegrationQueueQuery {
    1: required string groupName
    2: required string targetBranch
    255: optional base.Base Base;
}
struct GetMRIntegrationQueueResponse {
    1: required list<MRQueueInfo> mrs
    255: optional base.BaseResp BaseResp;
}
// 设置合入优先级为置顶
struct SetMRIntegrationPriorityQuery {
    1: required string actionUsername
    2: required i64 ID
    3: optional i64 projetID
    4: optional i64 iID
    255: optional base.Base Base;
}
struct SetMRIntegrationPriorityResponse {
    255: optional base.BaseResp BaseResp;
}
// 获取未合入的超级 MR
struct GetSuperMRWithNotMergedQuery {
    1: required string groupName
    2: required string targetBranch
    255: optional base.Base Base;
}
struct GetSuperMRWithNotMergedResponse {
    1: required list<MRQueueInfo> mrs
    255: optional base.BaseResp BaseResp;
}
// 获取所有持有合入锁的 MR
struct RemainLockMRInfo {
    1: required i64 bitsMRID
    2: required i64 gitlabMRID
    3: required i64 projectID
    4: required list<string> lockTypes
    5: required i64 scrambLockTimes
    6: required string groupName
    7: required string branch
    8: required string state
    9: required string title
}
struct GetAllRemainLockMRQuery {
}
struct GetAllRemainLockMRResponse {
    1: required list<RemainLockMRInfo> mrs
    255: optional base.BaseResp BaseResp;
}

struct SetMrDraftResp {
    1: required string draftKey
    255: optional base.Base BaseResp
}
//新basic接口基础信息
struct DevBasicInfo{
    1: required string title
    2: required string mrType
    3: required string userName
    4: required i64 createTime
    5: required bool superior
    6: required bool isGitlabMr
    7: required string featureInfo
    8: required bool removeSourceBranch
    9: required bool wip
    10: required string pipelineEnv
    11: required string note
    12: required  string groupName
    13: required i64 remindExpireTime
    14: required string state
    15: required list<string> groupNames
    16: required string titleContent
    17: required string larkGroupID
    18: required list<MrTag> tags
    19: optional bool canEdit
    20: optional string uneditableReasons
    21: required bool squash
    22: required string squashCommitMessage
    23: optional bool dagUpgradeEnable
    24: optional bool mergeTrainMR
    25: optional bool richTextDescriptionEnabled
    26: optional string richTextDescription
    27: optional i64 businessType
    28: optional i64 businessID
}
struct GetDevBasicInfoQuery{
    1: required i64 devID
    2: required string userName
    255: optional base.Base Base;
}
struct GetDevBasicInfoResponse{
    1: required  DevBasicInfo devBasicInfo
    255: optional base.BaseResp BaseResp
}

//新basic接口分支信息
struct DevBranchInfo{
    1: required list<MRBasicInfo> publicMrs
    2: required list<HostDependencyBasicInfo> mrs
    3: required i64 unmergedCount
    4: required DevStateCount devStateCount
}
struct HostDependencyBasicInfo{
    1:required MRBasicInfo host
    2:required list<VersionDependencyBasicInfo> VersionDependencies
    3:required list<MRBasicInfo> MrDependencies
}
struct MRBasicInfo{
    1:required string groupName
    2:required string projectName
    3:required  i16 appType
    4:required string gitlabURL
    5:required bool flutterModule
    6:required string rcTargetBranch
    7:required bool isRcBranch
    8:required bool skipPipeline
    9:required i64 ID
    10:required string sourceBranch
    11:required string targetBranch
    12:required bool conflicted
    13:required bool emptyContent
    14:required string state
    15:required string role
    16:required i64 projectID
    17:required string productVersion
    18:required i64 versionCloseHour
    19:required i64 versionCloseMinute
    20:required i64 IID
    21:required string lastCommitID
    22:required bool isPreBoarding
    23:required list<ComponentBasicInfo> components
    24:optional bool skipAutoPublish
}
struct ComponentBasicInfo {
    1:required string status
    2:required i64 componentID
    3:required string name
    4:required string logURL
    5:required string versionFinal
    6:required i16 repoTechType
    7:required string publishType
    8:required VersionSetting version
    9:required string hostProjectID
}
struct VersionSetting {
    1:required string versionBase
    2:required string customSuffix
    3:required string suffix
    4:required string upgradeType
}
struct VersionDependencyBasicInfo{
    1:required i64 componentID
    2:required string name
    3:required string source
    4:required string version
    5:required string versionOrigin
    6:required i64 ID
    7:required i8 repoTechType
}
struct DevStateCount{
    1:required i64 ConflictedMrCount
    2:required i64 ReleaseFailedComponentCount
    3:required i64 UnmergedMrCount
}
struct GetDevBranchInfoQuery{
    1: required i64 devID
    2: required string userName
    255: optional base.Base Base;
}
struct GetDevBranchInfoResponse{
    1: required  DevBranchInfo devBranchInfo
    255: optional base.BaseResp BaseResp
}
//获取mr相关所有空间名称&&第一主仓projectID IID
struct GetGroupNamesByMrIDQuery{
    1: required i64 mrID
    255: optional base.Base Base;
}

struct GetGroupNamesByMrIDResponse{
    1: required  list<string> groupNames
    2: required string projectID
    3: required i64 iid
    4: required i64 devID
    255: optional base.BaseResp BaseResp
}
struct GetMrCompletedIDQuery{
    1: optional string projectID
    2: optional i64 IID
    3: optional i64 ID
    255: optional base.Base Base;
}
struct GetMrCompletedIDResponse{
    1: required string projectID
    2: required i64 IID
    3: required i64 ID
    255: optional base.BaseResp BaseResp
}

struct ReviewerChangeCallbackReq{
    1: required i64 mrID
    2: optional i64 devID
    255: optional base.Base Base;
}
// 获取 gitlab MR 的状态
struct GetGitlabMRPipelineStateBatchQuery {
    1: required list<i64> mrsID
    255: optional base.Base Base;
}
struct GitlabMRPipelineInfo {
    1: required i64 mrID
    2: required i64 projectID
    3: required i64 iID
    4: required i8 pipelineState
    5: required i64 parentMRID
    6: required string mode
    7: optional i64 devID
}
struct GetGitlabMRPipelineStateBatchResponse {
    1: required map<i64, list<GitlabMRPipelineInfo>> pipelines
    255: optional base.BaseResp BaseResp
}

// 重置 gitlab MR 缓存信息
struct ResetGitlabMRCacheQuery {
    1: optional i64 mrID
    2: optional i64 projectID
    3: optional i64 iID
    4: optional i64 devID
    255: optional base.Base Base;
}
struct ResetGitlabMRCacheResponse {
    255: optional base.BaseResp BaseResp
}

// 更新 code review mode
struct UpdateCodeReviewModeBatchQuery {
    1: required map<i64,i8> mrsMode
    255: optional base.Base Base;
}
struct UpdateCodeReviewModeBatchResponse {
    255: optional base.BaseResp BaseResp
}
// 重建已关闭的mr的草稿
struct ReCreateMrDraftQuery {
    1: required i64 devID
    2: required string userName
    255: optional base.Base Base
}
struct ReCreateMrDraftResponse {
    1: required string draftKey
    255: optional base.BaseResp BaseResp
}
// new dev block error
enum blockErrorDomain {
    dev = 1,
    project = 5,
    biz = 10,
}
struct DevBlockError {
    1: required string name
    2: required string level
    3: required blockErrorDomain domain
    4: required string taskName
    5: required DevBlockErrorDetail detail
    6: optional i64 projectID
}
struct DevBlockErrorDetail {
    1: required string title
    2: required string msg
    3: required string doc_url
    4: optional string msgI18N
}
struct GetDevBlockErrorQuery {
    1: required i64 devID
    2: optional string locale
    255: optional base.Base Base
}
struct GetDevBlockErrorResponse {
    1: required list<DevBlockError> blockErrorList
    255: optional base.BaseResp BaseResp
}
// 获取 MR 的业务线信息
struct GetBitsMRTeamQuery {
    1: required i64 mrID
    2: optional i64 devID
    255: optional base.Base Base
}

struct GetBitsMRTeamResponse {
    1: list<i64> teams

    255: optional base.BaseResp BaseRes
}
// 设置 MR 的业务线信息
struct SetBitsMRTeamQuery {
    1: required i64 teamID
    2: required i64 mrID
    3: optional i64 devID
    4: required string username,
    255: optional base.Base Base
}
struct SetBitsMRTeamResponse {
    255: optional base.BaseResp BaseRes
}

struct GetMrHostBitsAppInfoRequest {
    1: required i64 mrID,
    255: optional base.Base Base,
}

struct MrHostBitsAppInfo {
    1: required string zhName
    2: required string enName
    3: required i64 bitsAppID
    4: required string repoAddr
    5: optional i16 bitsAppTech
}

struct GetMrHostBitsAppInfoResponse {
    1: required list<MrHostBitsAppInfo> mrHostBitsAppInfos,
    255: optional base.BaseResp BaseResp
}

struct GetVersionMrListRequest {
    1: required string group_name,
    2: string version,
    3: required string branch,
    4: string tag,
    5: optional i64 wip,
}

struct GetVersionMrListResponse {
    1: required list<i64> mr_ids,
    255: optional base.BaseResp BaseResp;
}

struct UpdateMRsVersionRequest {
    1: required string version,
    2: required list<i64> mr_ids,
}

struct GetMachineTaskQuery {
    1: required i64 projectID
    2: required i64 iid
    3: required string taskName
    4: optional i64 limit
    255: optional base.Base Base;
}
struct MachineTaskInfo {
    1: required string result
    2: required i64 startTime
    3: required i64 endTime
    4: required string logURL
    5: required string status
    6: optional string taskID
}
struct GetMachineTaskResponse {
    1: required list<MachineTaskInfo> detail
    255: optional base.BaseResp BaseResp
}

struct GetFrozenMrListRequest {
    1: required string group_name,
    2: optional string version,
    3: optional string branch,
    4: optional string tag,
    5: optional i64 wip,
    6: optional i64 limit,
    7: optional i64 last_id,
    8: optional string project_id,
    9: optional string state,
    255: optional base.Base Base
}

struct GetFrozenMrListResponse {
    1: required list<i64> mr_ids,
    255: optional base.BaseResp BaseResp,
}
// dev
struct GetSmallMrListReq{
    1: optional string sourceBranch,
    2: optional string state,
    3: optional i64 businessType,
    255: optional base.Base Base
}
struct GetSmallMrListResp{
    1: required list<i64> mrIDs,
    255: optional base.BaseResp BaseResp,
}

struct GetBranchGroupRelationReq{
    1: required string branch,
    2: required i64 project_id,
    255: optional base.Base Base
}

struct GetBranchGroupRelationResp{
    1: required string group_name,
    255: optional base.BaseResp BaseResp
}

struct CreateDevTimelineEventQuery{
    1: required string Data
    2: required i64 DevID
    3: required string EventType
    4: required string Level
    5: required string Operator
    255: optional base.Base Base
}

struct CreateDevTimelineEventResponse{
    1: required DevTimelineEvent Event
    255: optional base.BaseResp BaseResp
}

struct DevTimelineEvent{
    1: required i64 ID
    2: required TimeLineEventData Data
    3: required i64 DevID
    4: required i64 CreatedAt
    5: required i64 UpdatedAt
    6: required string EventType
    7: required string Operator
    8: required string Level
}

struct GetDevTimelineEventsQuery{
    1: required i64 DevID
    2: optional i64 Limit
    3: optional i64 LastID
    4: optional list<string> EventTypeList
    255: optional base.Base Base
}

struct GetDevTimeLineEventsResponse{
     1: optional list<DevTimelineEvent> List
    255: required base.BaseResp BaseResp
}

struct GetUpdatedPSMByMRIDQuery{
    1: required i64 mr_id,
    2: optional string file_path,
    255:optional base.Base Base
}

struct GetUpdatedPSMByIIDQuery{
    1: required i64 project_id,
    2:required i64 iid,
    3: optional string file_path,
    255:optional base.Base Base
}

struct GetUpdatedPSMByMRIDResp{
    1: required list<string> psm,
    255:optional base.BaseResp BaseResp
}


struct DevTag{
    1: required i64 id
    2: required string name
    3: required string color
    4: required string catalog
    5: required string groupProjectName
    6: required string creator
    7: optional string enName
    8: optional string reason
}

struct CreateDevTagsQuery{
    1: required string name
    2: required string color
    3: required string catalog
    4: required string groupProjectName
    5: required string creator
    6: optional string enName
    255: optional base.Base Base
}

struct CreateDevTagsResponse{
    1: required i64 id
    2: required string name
    3: required string color
    4: required string catalog
    5: required string groupProjectName
    6: required string creator
    7: required i64 createAt
    8: required i64 updateAt
    9: optional string enName
    255: optional base.BaseResp BaseResp;
}

struct DeleteDevTagsQuery{
    1: optional i64 id
    2: optional string name
    3: optional string projectName
    255: optional base.Base Base
}

struct DeleteDevTagResponse{
    255: optional base.BaseResp BaseResp;
}

struct ListDevTagsQuery{
    1: optional string groupName
    2: optional list<string> catalogs
}

struct GetDevTagsQuery{
    1: required i64 devID
    255: optional base.Base Base
}

struct GetDevTagsResponse{
    1: required list<DevTag> devTags
    255: required base.BaseResp BaseResp;
}

struct ChangeDevTagQuery{
    1: required i64 devID
    2: required string tagName
    3: optional string groupName
    4: optional string reason
    255: optional base.Base Base
}

struct ChangeDevTagResponse{
    255: optional base.BaseResp BaseResp;
}

enum ChangeType{
    Unchanged
	Modified
	Deleted
	Added
}

struct DiffCheckNodeExtra{
    1: optional ChangeType changeType
    2: optional list<i32> insertedLine
}

struct DiffCheckNode{
    1: required i64 id;
    2: required string funcName;
    3: required string signature;
    4: required string class;
    5: required string path;
    6: optional DiffCheckNodeExtra extra;
}

struct DiffCheckEdge{
    1: required i64 from
    2: required i64 to
    3: required map<i64,i32> callSite
}

struct SendMrDiffCheckResultReq{
    1: required list<i64> startId
    2: required list<DiffCheckNode> node
    3: required list<DiffCheckEdge> edge
    4: required i64 project_id
    5: required i64 iid
    6: required i64 pipeline_id
    7: required list<DiffPatches> patches
    255: optional base.Base Base
}

struct DiffPatches{
    1: required string fileName
    2: required string modType
    3: required i64 lineNum
}

struct GetMrDiffCheckResultReq{
    1: required i64 mr_id
    255: optional base.Base Base
}

struct GetDevDiffCheckResultReq{
    1: required i64 dev_id
    255: optional base.Base Base
}

struct GetDiffCheckResultByProjectIdIIDReq{
    1: required i64 project_id
    2: required i64 iid
    255: optional base.Base Base
}

struct DiffCheckPipelineInfo{
    1: required i64 pipelineId
    2: required string status
    3: required string msg
}

struct GetMrDiffCheckResultResp{
    1: optional MrDiffCheckResult result
    2: required DiffCheckPipelineInfo pipelineInfo
    255: optional base.BaseResp BaseResp
}

struct MrDiffCheckResult{
    1: required list<DiffCheckNode> node
    2: required list<DiffCheckEdge> edge
    3: required list<DiffCheckNode> starts
    4: optional string commitId
}

struct projectIdIIDQuery{
    1: required i64 project_id
    2: required i64 iid
}

union DiffCheckTargetQuery{
    1: i64 mr_id
    2: i64 dev_id
    3: projectIdIIDQuery project_id_iid
}

struct GetAffectedMethodsMonitorReq{
    1: required DiffCheckTargetQuery target
    2: required affectedMethodsQuery query
    3: required string userToken
    255: optional base.Base Base
}

enum aggreatorsType{
    p99
    p95
    p90
    p50
    avg
    max
    min
    weighted_p99
    weighted_p90
    weighted_p50
    weighted_avg
}

enum filterFunction{
    not_sliteral_or
    literal_or
}

struct queryFilters{
    1: required string key
    2: required string value
    3: required filterFunction function = filterFunction.literal_or
}

enum indexType{
    Latency
    QPS
    SuccessLatency
    ErrorQPS
}

struct affectedMethodsQuery{
    1: required bool withHttpRoute
    2: required i64 startTime
    3: required i64 endTime
    4: required string region
    5: required list<indexType> indices
    6: optional list<aggreatorsType> aggreators
    7: optional list<queryFilters> filters
}

struct IndexItem{
    1: required indexType index
    2: required double value
    3: required list<string> unit
    4: required double shift
}

struct TagInfo{
    1: required string tagId
    2: required string tag
    3: required string description
}

struct BamInfo{
    1: required string name
    2: required string note
    3: required i32 level
    4: required string owner
    5: required i64 endpointId
    6: required list<TagInfo> tags
}

struct affectedMethodMonitorItem{
    1: required string method
    2: optional string httpRoute
    3: required map<indexType,IndexItem> indices
    4: required list<DiffCheckNode> node
    5: optional DiffCheckNode handler
    6: optional BamInfo bamInfo
}

struct ServiceMonitorItem{
    1: required list<affectedMethodMonitorItem> method
    2: required ServiceMeta serviceMeta
}


struct GetAffectedMethodsMonitorResp{
    1: required map<string,ServiceMonitorItem> items
    2: optional string commitId
    255: optional base.BaseResp BaseResp
}

enum serviceType{
    tce,
    faas,
    cronjob,
    unknown
}

struct ServiceMeta{
    1: required string psm
    2: required string description
    3: required serviceType type
    4: required string idl
    5: required string prebuildCmd
    6: optional map<string,string> extra
}

struct GetUpdatePSMMetaByCACResp{
    1: required list<ServiceMeta> psmList
    255: optional base.BaseResp BaseResp
}

struct DiffCheckCallbackResp{
    255: optional base.BaseResp BaseResp
}

struct InvokeDiffCheckManuallyReq{
    1: required DiffCheckTargetQuery target
    2: required string username
    3: optional i64 pipelineId
    4: optional map<string,string> envs
    255: optional base.Base Base
}

struct InvokeDiffCheckManuallyResp{
    1: required i64 pipelineID
    255: optional base.BaseResp BaseResp
}

struct DevTaskDiffCheckRespPair{
    1: required i64 pipelineID
    2: required i64 checkRunID
}

struct InvokeDevTaskDiffCheckResp{
    1: required list<DevTaskDiffCheckRespPair> result
    255: optional base.BaseResp BaseResp
}

struct InvokeDevTaskDiffCheckReq{
    1: required i64 devBasicId
}

struct DiffCheckCallbackReq{
    1: required workflow.Pipeline pipeline
    2: required list<workflow.Job> job
    255: optional base.Base Base
}

struct GetDiffCheckFunctionInfoReq{
    1: required i64 project_id
    2: required i64 iid
    3: required list<i64> functionFilter
    255: optional base.Base Base
}

struct DiffCheckFunctionInfo{
    1: required list<DiffCheckNode> node
    2: required list<DiffCheckEdge> edge
}

struct GetDiffCheckFunctionInfoResp{
    1: required map<i64,DiffCheckFunctionInfo> functionInfo
    255: optional base.BaseResp BaseResp
}

struct EnableCodeImpactAutoInvokeReq{
    1: required string url
    2: required string operator
    255: optional base.Base Base
}

enum ConflictResulotionMode{
    manually = 0
    force_manually = 1
}

struct GetMrConflictResolutionQuery{
    1: required i64 mrID
    255: optional base.Base Base
}

struct GetMrConflictResolutionResponse{
    1: required ConflictResulotionMode mode
    2: optional string mergeTargetBranch
    255: optional base.BaseResp BaseResp
}

struct GetMrConflictStatusQuery{
    1: required i64 mrID
    255: optional base.Base Base
}

struct ConflictedFile {
    1: string conflictFile,
    2: string conflictType,
    3: string conflictFileMimeType,
    4: string conflictFileUrl,
    5: string conflictBlameFileUrl
}

struct GetMrConflictStatusResponse{
    1: required i64 conflicted // 0 1 2 3 4
    2: required string taskURL
    3: required list<ConflictedFile> conflictedFiles

    255: optional base.BaseResp BaseResp
}

enum MergeRequestRunningSate {
    failed = 0
    succeeded = 1
    closed = 2
    running = 3
}

struct MrRunningStateInfo{
    1: MergeRequestRunningSate state
    2: list<DevBlockErrorDetail> blockErrors
}

struct GetMrRunningStateQuery{
    1: required i64 mrID
    255: optional base.Base Base
}

struct GetMrRunningStateResponse{
    1: required  MrRunningStateInfo info
    255: optional base.BaseResp BaseResp
}

// 设置
service OptimusService {
    // merge_request
    GetMrListResponse GetMrList(1: GetMrListQuery req) // 获取MR列表
    GetMrListBySearchResponse GetMrListBySearch(1: GetMrListBySearchQuery req) // MR列表的自定义搜索
    GetMrListBySearchTotalResponse GetMrListBySearchTotal(1: GetMrListBySearchQuery req) // MR列表的自定义搜索获取总数
    GetMrInfoResponse GetMrInfo(1: GetMrInfoQuery req) // 获取单个MR详情
    ListMrTagResponse GetMrTags(1: GetMrInfoQuery req) // 获取单个MR的标签
    GetMrMainInfoResponse GetMainMrInfo(1: GetMrMainInfoQuery req) // 获取单个MR详情
    GetMrPipelinesResponse GetMrPipelines(1: GetMrPipelinesQuery req) // 获取单个MR的pipeline信息
    GetMrReviewersResponse GetMrReviewers(1: GetMrReviewersQuery req) // 获取单个MR的review人信息
    GetMrReviewerCommonReasonsResponse GetMrReviewerCommonReasons(1: GetMrReviewerCommonReasonsQuery req) // 获取review人的review的原因
    GetMrRequestIDByProjectIDAndIIDResponse GetMrRequestIDByProjectIDAndIID(1: GetMrRequestIDByProjectIDAndIIDQuery req) // 根据MR_ID获取project_id & iid
    GetMrRelationsResponse GetMrRelations(1: GetMrRelationsQuery req) // 获取MR下多个子MR信息(多主仓,多宿主, 不包含主仓)
    GetBitsMrRelationsResponse GetBitsMrRelations(1: GetBitsMrRelationsQuery req) // 返回bits 研发任务所有的 mr 信息
    GetMrDependencyPublishVersionInfoByProjectIDAndIIDResponse GetMrPublishVersion(1: GetMrDependencyPublishVersionInfoByProjectIDAndIIDQuery req) // 获取MR下的版本发布信息
    GetMrLockInfoByProjectIDAndTargetBranchResponse GetMrLockInfoByProjectIDAndTargetBranch(1: GetMrLockInfoByProjectIDAndTargetBranchQuery req) // 获取MR的锁信息
    GetAtomicLockInfoByProjectAndTargetBranchResponse GetAtomicLockInfoByProjectAndTargetBranch(1: GetAtomicLockInfoByProjectAndTargetBranchQuery req) // 获取原子锁信息
    GetHostMrIDResponse GetHostMrID(1: GetHostMrIDQuery req) // 获取主仓MR的ID
    GetMrVersionDependencyTotalResponse GetMrVersionDependencyTotal(1: GetMrVersionDependencyTotalQuery req) // 获取MR依赖的total信息
    GetMrTimelineInfoByMrIDResponse GetMrTimelineInfoByMrID(1: GetMrTimelineInfoByMrIDQuery req) // 获取MR下所有的timeline信息(包含多主仓)
    GetMrTimeLineEventDataByMrIDResponse GetMrTimelineDataByMrID(1: GetMrTimelineInfoByMrIDQuery req) // 获取MR下所有的timeline信息(包含多主仓)(data 已解析)
    GetGraphPipelineInfoResponse GetGraphPipelineInfo(1: GetGraphPipelineInfoQuery req) // MR pipeline 展示的接口(view层使用)
    GetMrIDsByProjectAndVersionResponse GetMrIDsByProjectAndVersion(1: GetMrIDsByProjectAndVersionQuery req) // 获取指定版本下的MR数据
    GetReviewPermissionsResponse GetReviewPermissions(1: GetReviewPermissionsQuery req) // 获取MR下的用户的权限信息
    GetMrSimpleInfosByProjectAndVersionResponse GetMrSimpleInfosByProjectAndVersion(1: GetMrSimpleInfosByProjectAndVersionQuery req) // 获取MR的主干信息
    GetMrComponentPublishInfoResponse GetMrComponentPublishInfo(1: GetMrComponentPublishInfoQuery req) // 获取组件发布信息
    GetMrCommitsInfoResponse GetMrCommitsInfo(1: GetMrCommitsInfoQuery req) // 获取单个MR的git commit信息
    GetMrBranchCommitStateResponse GetMrBranchCommitState(1: GetMrBranchCommitStateQuery req) // 获取MR下的commit变更信息
    GetMrFeatureInfoResponse GetMrFeatureInfo(1: GetMrFeatureInfoQuery req) // 获取MR的feature信息(slardar, jira, meego, )
    GetMrRepoInfoResponse GetMrRepoInfo(1: GetMrRepoInfoQuery req) // 获取MR的仓库信息
    CreateTimelineEventResponse CreateTimelineEventData(1: CreateTimelineEventQuery req) // 增加一个Timeline Event
    GetMrBlockErrorInfoResponse GetMrBlockErrorInfo(1: GetMrBlockErrorInfoQuery req) // 获取MR的block error信息
    BlockErrorResolveClickResponse BlockErrorResolveClick(1: BlockErrorResolveClickQuery req) // 用户点击解决方案
    CreateMrBlockErrorInfoResponse CreateMrBlockErrorInfo(1: CreateMrBlockErrorInfoQuery req) // 创建MR的block error信息
    GetMrIntegrationStateResponse GetMrIntegrationState(1: GetMrIntegrationStateQuery req) // 获取MR的集成状态
    NoneResponse StoreError(1: StoreErrorRequest req) // 处理流程error
    CreateMrTagsResponse CreateMrTags(1: CreateMrTagsQuery req) // 创建Tag
    DeleteMrTagResponse DeleteMrTags(1: DeleteMrTagsQuery req) // 删除Tag (id/name + project 2选一)
    BindMrTagResponse BindMrTag(1: BindMrTagQuery req) // 绑定Tag
    BindMrTagResponse RemoveMrTag(1: BindMrTagQuery req) // 移除绑定Tag
    ListMrTagResponse ListMrTag(1: ListMrTagQuery req) // 列出可用tag
    GetConfigGroupBuildConfigResponse GetConfigGroupBuildConfig(1: GetConfigGroupBuildConfigQuery req) // 获取项目的打包配置
    GetVersionsHasUsedInMrResponse GetVersionsHasUsedInMr(1: GetVersionsHasUsedInMrQuery req) // 获取MR下用到过的所有版本号
    CreateMrParamsValidCheckResponse CreateMrParamsValidCheck(1: CreateMrParamsValidCheckQuery req) // 校验创建MR参数的合法性
    GetProjectBranchesResponse GetProjectBranches(1: GetProjectBranchesQuery req) // 获取仓库的分支信息
    GetMrCustomModifyParamsResponse GetMrCustomModifyParams(1: GetMrCustomModifyParamsQuery req) // 获取MR创建时的自定义参数
    GetMrIDByChatIDResponse GetMrIDByChatID(1: GetMrIDByChatIDQuery req) // 通过chat_id获取mr_id
    *********************Response *********************(1: *********************Query req) // 获取仓库在gitlab上的一些状态
    GetVersionDependencyResponse GetVersionDependency(1: GetVersionDependencyQuery req) // 获取mr的版本依赖
    BuildMrCacheResponse BuildMrCache(1: BuildMrCacheQuery req) // python 工程使用, 迁移过渡中的调用
    ResetMrCacheResponse ResetMrCache(1: ResetMrCacheQuery req) // python 工程使用, 迁移过渡中使用
    GetMrCompletedIDResponse GetMrCompletedID(1: GetMrCompletedIDQuery req) // 根据 project_id & iid 获取 mr_id 或者根据 mr_id 获取 project_id & iid
    GetGitlabMRPipelineStateBatchResponse GetGitlabMRPipelineStateBatch(1: GetGitlabMRPipelineStateBatchQuery req) // 获取 gitlab mr 的pipeline的信息
    UpdateCodeReviewModeBatchResponse UpdateCodeReviewBatchMode(1: UpdateCodeReviewModeBatchQuery req) // 更新 code review mode
    ResetGitlabMRCacheResponse ResetGitlabMRCache(1: ResetGitlabMRCacheQuery req) // 刷新 gitlab mr 缓存
    GetBitsMRTeamResponse GetBitsMRTeam(1: GetBitsMRTeamQuery req) // 获取 mr 绑定的业务线
    SetBitsMRTeamResponse SetBitsMRTeam(1: SetBitsMRTeamQuery req) // 设置 mr 绑定的业务线
    GetProjectInfoByProjectIDResponse GetProjectInfoByProjectID(1: GetProjectInfoByProjectIDReq req) // 通过project_id获取项目信息
    GetMrHostBitsAppInfoResponse GetMrHostBitsAppInfo(1: GetMrHostBitsAppInfoRequest req) // Get host projects information of single/multi-hosts bits mr.
    GetVersionMrListResponse GetVersionMrList(1: GetVersionMrListRequest req) // deprecated: 从DB直接获取主仓某个版本的Mr列表（保证封版场景稳定性）
    GetFrozenMrListResponse GetFrozenMrList(1: GetFrozenMrListRequest req) // 从DB直接获取主仓某个版本的Mr列表（保证封版场景稳定性）
    base.EmptyResponse UpdateMRsVersion(1: UpdateMRsVersionRequest req) // 更新mr的版本信息
    GetMachineTaskResponse GetMachineTask(1: GetMachineTaskQuery req) // 获取内置 task 的执行日志
    GetMachineTaskResponse GetMachineTaskV2(1: GetMachineTaskQuery req) // get machine task log with cloud build
    GetUpdatedPSMByMRIDResp GetUpdatedPSMByMRID(1: GetUpdatedPSMByMRIDQuery req)//获取psm变更列表
    GetUpdatePSMMetaByCACResp GetUpdatePSMMetaByCAC(1: GetUpdatedPSMByMRIDQuery req)//获取psm变更列表
    GetUpdatePSMMetaByCACResp GetUpdatePSMMetaByIID(1: GetUpdatedPSMByIIDQuery req)//获取psm变更列表
    GetMrConflictResolutionResponse GetMrConflictResolution(1: GetMrConflictResolutionQuery req) // 获取 MR 冲突解决方案
    GetMrConflictStatusResponse GetMrConflictStatus(1: GetMrConflictStatusQuery req) // 获取 MR 冲突状态
    GetMrRunningStateResponse GetMrRunningState(1: GetMrRunningStateQuery req) // 获取 MR 运行状况信息

    // project
    GetConfigsByProjectNameResponse GetConfigsByProjectName(1: GetConfigsByProjectNameQuery req) // 获取项目下的所有仓库(直接|间接)的信息
    GetConfigsByProjectNameResponse GetProjectAndRelatedHostConfigsByProjectName(1: GetConfigsByProjectNameQuery req)
    GetProjectInfoByProjectEnNameOrRepoAddrResponse GetProjectInfoByProjectEnNameOrRepoAddr(1: GetProjectInfoByProjectEnNameOrRepoAddrQuery req) // 获取project的信息
    GetGroupInfoByProjectIDResponse GetGroupInfoByProjectID(1: GetGroupInfoByProjectIDQuery req) // 通过仓库id获取空间的信息
    GetGroupInfoByGroupNameResponse GetGroupInfoByGroupName(1: GetGroupInfoByGroupNameQuery req) // 通过空间的group_name获取空间的主仓信息
    GetProjectHasUsedRepoInfoByProjectIDResponse GetProjectHasUsedRepoInfoByProjectID(1: GetProjectHasUsedRepoInfoByProjectIDQuery req) // 只有metrics使用
    GetBranchGroupRelationResp GetBranchGroupRelation(1: GetBranchGroupRelationReq req) // 获取分支关联的空间名
    // group
    GetConySettingsByGroupNameResponse GetConySettingsByGroupName(1: GetConySettingsByGroupNameQuery req), // 获取setting数据
    FilterVersionCloseGroupsResponse FilterVersionCloseGroups(1: FilterVersionCloseGroupsQuery req), // 获取当前时间封板的 groups
    // slardar
    GetUserSlardarTaskResponse GetUserSlardarTask(1: GetUserSlardarTaskQuery req)
    // user
    GetUserInfoResponse GetUserInfo(1: GetUserInfoQuery req) // 获取用户信息
    // workflow and engine
    NoneResponse EngineEndCallback(1: EngineEndCallbackQuery req) // engine workflow end时的回调
    NoneResponse WorkflowTaskTimeoutAlarm(1: WorkflowTaskTimeoutAlarmQuery req), // CanMergeTask is_end成功后触发，将发送一定时间的延迟消息，用于判断workflow的流程是否超时
    // lark
    InviteUserJoinLarkGroupResponse InviteUserJoinLarkGroup(1: InviteUserJoinLarkGroupQuery req) // 邀请用户进群
    RemoveUserFromChatResponse RemoveUserFromChat(1: RemoveUserFromChatQuery req) // 移除MR对应群聊里的用户, 后期迁移到基座中
    CreateLarkGroupResponse CreateLarkGroup(1: CreateLarkGroupQuery req) // 创建 mr 的 lark 群聊
    DismissLarkGroupResponse DismissLarkGroup(1: DismissLarkGroupQuery req) // 解散 mr 的 lark 群聊

    // version close
    GetAppReleaseVersionInfoResponse GetAppReleaseVersionInfo(1: GetAppReleaseVersionInfoQuery req) // 获取App封版信息
    // WebHook
    GetWebHookEventsResponse GetWebHookEvent(1: GetWebHookEventsQuery req) // 获取事件
    CheckWebhookURLResponse CheckWebhookURL(1: CheckWebhookURLQuery req) // 获取事件
    GetWebHookCatetgoriesResponse GetWebHookCategories(1: GetWebHookCategoriesQuery req) // 获取事件分类
    GetWebHookSubscribeRecordResponse GetWebHookSubscribeRecord(1: GetWebHookSubscribeRecordQuery req) // 获取订阅记录
    CreateWebHookSubscribeRecordResponse CreateWebHookSubscribeRecord(1: CreateWebHookSubscribeRecordQuery req) // 创建订阅记录
    RemoveWebHookSubscribeRecordResponse RemoveWebHookSubscribeRecord(1: RemoveWebHookSubscribeRecordQuery req) // 删除订阅记录
    CheckPrivacyGroupPermissionByGitlabMRIDResponse CheckPrivacyGroupPermissionByGitlabMRID(1: CheckPrivacyGroupPermissionByGitlabMRIDQuery req) // 保密项目权限检查
    // 集成
    GetGroupMergingMRsResponse GetGroupMergingMRs(1: GetGroupMergingMRsQuery req) // 获取空间下正在合入的 MR
    GetMRIntegrationQueueResponse GetMRIntegrationQueue(1: GetMRIntegrationQueueQuery req) //  获取集成中的 MR
    SetMRIntegrationPriorityResponse SetMRIntegrationPriority(1: SetMRIntegrationPriorityQuery req) // 设置合入优先级别为置顶
    SetMrDraftResp SetMrDraft(1: CreateMrType mrType,2: string json) // 设置MR草稿
    GetSuperMRWithNotMergedResponse GetSuperMRWithNotMerged(1: GetSuperMRWithNotMergedQuery req) // 获取未合入的超级 MR
    GetAllRemainLockMRResponse GetAllRemainLockMR(1: GetAllRemainLockMRQuery req) // 获取系统中所有持有锁的 MR
    graph.GetGraphResp GetGraph(1: graph.GetGraphReq req)
    graph.DecodeNodeMetaResp DecodeNodeMeta(1: graph.DecodeNodeMetaReq req)
    graph.GetPopOverResp GetPopOver(1: graph.GetPopOverReq req)
    //详情页
    GetDevBasicInfoResponse GetDevBasicInfo(1: GetDevBasicInfoQuery req)
    GetDevBranchInfoResponse GetDevBranchInfo(1: GetDevBranchInfoQuery req)
    GetGroupNamesByMrIDResponse GetGroupNamesByMrID(1: GetGroupNamesByMrIDQuery req)
    base.EmptyResponse ReviewerChangeCallback(1: ReviewerChangeCallbackReq req)
    ReCreateMrDraftResponse ReCreateMrDraft (1: ReCreateMrDraftQuery req)
    //hotfix
    hotfix.CreateHotFixResp CreateHotFix(1: hotfix.CreateHotfixDTO req)
    hotfix.ResetHotfixApproveResp ResetHotfixApprove(1: hotfix.ResetHotfixApproveReq req)
    hotfix.ApproveRejectResp ApproveHotFix(1: hotfix.ApproveRejectReq req)
    hotfix.ApproveRejectResp RejectHotFix (1: hotfix.ApproveRejectReq req)
    hotfix.CreateVersionResp CreateHotfixVersion(1: hotfix.CreateVersionDTO req)
    hotfix.CreatePublishResp CreateHotfixPublish(1: hotfix.CreatePublishReq req)
    hotfix.GetHotFixVersionListResp GetHotFixVersionList(1: hotfix.GetHotFixVersionListReq req)
    base.EmptyResponse DeleteHotFixVersion(1:hotfix.DeleteHotFixVersionReq req)
    hotfix.GetPublishListResp GetPublishList(1: hotfix.GetPublishListReq req)
    hotfix.GetHotFixPublishBasicInfoResp GetHotFixPublishBasicInfo(1: hotfix.GetHotFixPublishBasicInfoReq req)
    hotfix.GetHotFixTaskApprovalStatusResp GetHotFixTaskApprovalStatus(1: hotfix.GetHotFixTaskApprovalStatusReq req)
    hotfix.GetPublishInfoResp GetPublishInfoByID(1: hotfix.GetPublishInfoReq req)
    hotfix.GetPublishGraphResp GetPublishGraph(1: hotfix.GetPublishGraphReq req)
    hotfix.GetHotfixRepoListByAppVersionResp GetHotfixRepoListByAppVersion(1: hotfix.GetHotfixRepoListByAppVersionReq req)
    base.EmptyResponse AddHotfixRepo(1: hotfix.AddHotfixRepoReq req)
    hotfix.GetRepoListResp GetHotfixVersionRepoList(1: hotfix.GetRepoListReq req)
    base.EmptyResponse DeleteHotfixRepo(1:hotfix.DeleteRepoListReq req)
    hotfix.InviteUserJoinLarkGroupResp InviteUserJoinHotfixLarkGroup(1:hotfix.InviteUserJoinLarkGroupReq req)
    hotfix.GetHotfixBranchInfoResp GetHotfixBranchInfo(1: hotfix.GetHotfixBranchInfoReq req)
    hotfix.CreatePublishTimelineResp CreatePublishTimeline(1: hotfix.CreatePublishTimelineReq req)
    hotfix.GetHotfixPublishTimelineResp GetHotfixPublishTimeline(1: hotfix.GetHotfixPublishTimelineReq req)
    hotfix.GetHotfixTaskListResp SearchHotfixTask(1: hotfix.SearchHotfixTaskReq req)
    hotfix.GetHotfixTaskByIDsResp GetHotfixTaskByIDs(1: hotfix.GetHotfixTaskByIDsReq req)
    hotfix.GetDynamicPkgTaskByIDsResp GetDynamicPkgTaskByIDs(1: hotfix.GetHotfixTaskByIDsReq req)
    base.EmptyResponse TerminatePublish(1: hotfix.TerminatePublishReq req)
    hotfix.GetHotfixTaskIDByTicketIDResp GetHotfixTaskIDByTicketID(1: hotfix.GetHotfixTaskIDByTicketIDReq req)
    hotfix.GetHotfixIntegrationInfoResp GetHotfixIntegrationInfo(1: hotfix.GetHotfixIntegrationInfoReq req)
    hotfix.GetHotfixBuildDetailResp GetHotfixBuildDetail(1: hotfix.GetHotfixBuildDetailReq req)
    hotfix.GetHotfixPipelineIDByPublishResp GetHotfixPipelineIDByPublish(1: hotfix.GetHotfixPipelineIDByPublishReq req)
    hotfix.GetMboxLinkResp GetMboxLink(1: hotfix.GetMboxLinkReq req)
    hotfix.GetVersionListByAppIDResp GetVersionListByAppID(1: hotfix.GetVersionListByAppIDReq req)
    base.EmptyResponse CloseHotfixPublish(1: hotfix.CloseHotfixQuery req)
    hotfix.CreateHotfixBuildResp CreateHotfixBuild(1: hotfix.CreateHotfixBuildReq req)
    hotfix.GetHotfixBuildListResp GetHotfixBuildList(1: hotfix.GetHotfixBuildListReq req)
    hotfix.GetHotfixBuildBranchInfoResp GetHotfixBuildBranchInfo(1: hotfix.GetHotfixBuildBranchInfoReq req)
    hotfix.CreatePublishResp CreateAndroidHotfixPublish(1: hotfix.CreatAndroidHotfixPublisReq req)
    hotfix.GetHotfixBuildInfoByIDResp GetHotfixBuildInfoByID(1: hotfix.GetHotfixBuildInfoByIDReq req)
    hotfix.GetHotfixPackageInfoResp GetHotfixPackageInfo(1: hotfix.GetHotfixPackageInfoReq req)
    base.EmptyResponse EditRepoModules(1: hotfix.EditRepoModulesReq req)
    base.EmptyResponse RetryHotfixBuild(1: hotfix.RetryHotfixBuildReq req)
    //new block error
    GetDevBlockErrorResponse GetDevBlockError (1: GetDevBlockErrorQuery req)
    // dev
    GetDevTimeLineEventDataByDevIDResponse GetDevTimelineDataByDevID(1: GetDevTimelineInfoByDevIDQuery req) // get Dev timeline
    graph.GetPopOverResp GetGraphStageInfo (1: graph.GetGraphStageInfoReq req) // get graph stage info
    GetSmallMrListResp  GetSmallMrList (1:GetSmallMrListReq req) // [bitscrl c push] get dev mr by branch
    // dev infra
    CreateDevTimelineEventResponse CreateDevTimelineEvent(1: CreateDevTimelineEventQuery req) // create a timeline event
    GetDevTimeLineEventsResponse GetDevTimelineEvents(1: GetDevTimelineEventsQuery req) // get timeline events by dev id
    CreateDevTagsResponse CreateDevTag(1: CreateDevTagsQuery req) // create tag
    DeleteDevTagResponse DeleteDevTag(1: DeleteDevTagsQuery req) // delete tag
    GetDevTagsResponse ListDevTags(1: ListDevTagsQuery req) //  list all type of tags
    ChangeDevTagResponse BindDevTag(1: ChangeDevTagQuery req) // bind tag
    ChangeDevTagResponse UnbindDevTag(1: ChangeDevTagQuery req) // unbound tag
    GetDevTagsResponse GetDevTags(1: GetDevTagsQuery req) // get tags by dev id
    base.EmptyResponse SendMrDiffCheckResult(1: SendMrDiffCheckResultReq req) // send mr diff check result
    GetMrDiffCheckResultResp GetMrDiffCheckResult(1: GetMrDiffCheckResultReq req) // get mr diff check result
    GetMrDiffCheckResultResp GetDiffCheckResultByProjectIdIID(1: GetDiffCheckResultByProjectIdIIDReq req) // get mr diff check result
    GetMrDiffCheckResultResp GetDevDiffCheckResult(1: GetDevDiffCheckResultReq req)
    GetAffectedMethodsMonitorResp GetAffectedMethodsMonitor(1: GetAffectedMethodsMonitorReq req) // get affected methods monitor
    DiffCheckCallbackResp DiffCheckCallback(1: DiffCheckCallbackReq req) // diff check callback
    InvokeDiffCheckManuallyResp InvokeDiffCheckManually(1: InvokeDiffCheckManuallyReq req) // invoke diff check manually
    InvokeDevTaskDiffCheckResp InvokeDevTaskDiffCheck(1: InvokeDevTaskDiffCheckReq req) // invoke from bits
    GetDiffCheckFunctionInfoResp GetDiffCheckFunctionInfo(1: GetDiffCheckFunctionInfoReq req) // get diff check function info
    base.EmptyResponse EnableCodeImpactAutoInvoke (1: EnableCodeImpactAutoInvokeReq req)
}
