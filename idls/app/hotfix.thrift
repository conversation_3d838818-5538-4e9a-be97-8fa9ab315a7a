include "../base.thrift"
namespace go bytedance.bits.hotfix

struct CreateHotfixDTO{
    1: required i64 taskID
    2: required string title
    3: required string author
    4: required string state
    5: required list<string> techApprovers
    6: required list<string> bizApprovers
    7: optional i64 isTestHotfix
    255: optional base.Base Base
}

struct CreateHotFixResp{
    1: required i64 devId
    2: required string ticketID
    255: optional base.BaseResp BaseResp
}

struct ResetHotfixApproveReq{
    1: required i64 taskID
    2: required list<string> techApprovers
    3: required list<string> bizApprovers
    255: optional base.Base Base
}

struct ResetHotfixApproveResp{
    1: required string ticketID
    255: optional base.BaseResp BaseResp
}

struct ApproveRejectReq{
    1: required i64 taskID
    2: required string user
    3: required string comment // 审批意见
    255: optional base.Base Base
}

struct CreateVersionDTO{
    1: required i64 taskID
    2: required string version
    3: required string versionLong
    4: required string author
    5: optional i64 bitsAppID
    255: optional base.Base Base
}

struct CreateVersionResp{
    1: required i64 versionID
    2: optional string branch
    255: optional base.BaseResp BaseResp
}

struct ApproveRejectResp{
    1: required bool isFinish // 通过判断是否终止，拒绝判断是否终止
    255: optional base.BaseResp BaseResp
}

struct createPublishRepoDTO{
    1: required i64 id
    2: required list<string> modules
}

struct CreatePublishReq{
    1: required i64 versionID
    2: required list<createPublishRepoDTO> repos
    3: required string author
    255: optional base.Base Base
}

struct CreatePublishResp{
    1: required i64 publishID
    255: optional base.BaseResp BaseResp
}

struct GetHotFixVersionListReq{
    1: required i64 taskID
    255: optional base.Base Base
}

struct HotFixVersion{
    1: required i64 id
    2: required string version
    3: required string versionLong
    4: required list<string> modules
    5: required string author
    6: required i64 createAt
    7: required string branch
    8: required string commit
    9: optional i64 appID
}

struct GetHotFixVersionListResp{
    1: required list<HotFixVersion> versions
    255: optional base.BaseResp BaseResp
}

struct DeleteHotFixVersionReq{
    1: required i64 versionID
    255: optional base.Base Base
}

struct GetPublishListReq{
    1: required i64 versionID
    255: optional base.Base Base
}

enum Stage{
    Integration,
    Build,
    SelfTest,
    CodeReview,
    QATest,
    Release,
    Close,
    Finished,
    AutomatedTest,
    Configure,
    Start,
}

enum Status{
    waiting,
    running,
    success,
    failed,
    cancelled,
    closed
}

struct Module{
    1: required string name
}

struct Publish{
    1: required i64 id
    2: required i64 versionID
    3: required string author
    4: required string dsym
    5: required string ipa
    6: required i64 createAt
    7: required Stage stage
    8: required Status status
    9: required list<Module> modules
    10: optional HotfixArtifact artifact //只返回saveu的包
}

struct GetPublishListResp{
    1: required list<Publish> publish
    255: optional base.BaseResp BaseResp
}

struct PublishModule{
    1: required i64 moduleID
    2: required i64 mrID
    3: required string projectID
    4: required i64 iID
    5: required string sourceBranch
    6: required string targetBranch
    7: required string name
    9: required i64 componentID
}

struct GetHotFixPublishBasicInfoReq{
    1: required i64 publishID
    255: optional base.Base Base
}

struct GetHotFixPublishBasicInfoResp{
    1: required string title
    2: required i64 taskID
    3: required string operator
    4: required i64 taskCreateTime
    5: required i64 publishCreateTime
    6: required string dsym
    7: required string ipa
    8: required i64 packageID
    9: required i64 appID
    10: required string groupName
    11: required string mainGroupCommit
    12: required string mainGroupRepoUrl
    13: required string mainGroupBranch
    14: optional i64 mrID
    15: optional i64 appCloudID
    16: optional string mainGroupTargetBranch
    255: optional base.BaseResp BaseResp
}

enum ApproveStatus{
    PASS,
    PENDING,
    TERMINATED
}

enum ApproverStatus{
    approved,
    rejected,
    pending
}

struct ApporverInfo{
    1: required string username
    2: required ApproverStatus status
    3: optional string comment
    4: optional i64 updateTime
}

struct GetHotFixTaskApprovalStatusReq{
    1: required i64 taskID
    255: optional base.Base Base
}

struct GetHotFixTaskApprovalStatusResp{
    1: required ApproveStatus status
    2: required list<ApporverInfo> techApprovers
    3: required list<ApporverInfo> bizApprovers
    255: optional base.BaseResp BaseResp
}

struct GetPublishInfoReq{
    1: required i64 publishID
    255: optional base.Base Base
}

struct GetPublishInfoResp{
    1: required i64 publishID
    2: required i64 versionID
    3: required string author
    4: required string dsym
    5: required string ipa
    6: required i64 createAt
    7: optional i64 packageID
    8: optional string status
    255: optional base.BaseResp BaseResp
}

struct GetPublishGraphReq{
    1: required i64 publishID
    255: optional base.Base Base
}

struct Node{
    1: required Stage stage
    2: required Status status
    3: required string name
    4: required i64 startAt
    5: required i64 endAt
}

struct GetPublishGraphResp{
    1: required map<Stage,Node> nodes
    255: optional base.BaseResp BaseResp
}

struct GetHotfixRepoListByAppVersionReq{
    1: required i64 versionID
    255: optional base.Base Base
}

struct moduleVersionInfo{
    1: required string name
    2: required string version
    3: required string commitID
    4: optional bool is_plugin
}

struct RepoModulesInfo{
    1: required string name
    2: required i64 projectID
    3: required string gitUrl
    4: required list<moduleVersionInfo> modules
}

struct GetHotfixRepoListByAppVersionResp{
    1: required list<RepoModulesInfo> repos
    255: optional base.BaseResp BaseResp
}

struct RepoDTO{
    1: required i64 projectID
    2: required string name
    3: required list<string> modules
    4: required string commitID
    5: required string branch
}

struct AddHotfixRepoReq{
    1: required i64 versionID
    2: required list<RepoDTO> repo
    3: required string operator
    255: optional base.Base Base
}

struct GetRepoListReq{
    1: required i64 versionID
    255: optional base.Base Base
}

struct HotfixRepo{
    1: required i64 projectID
    2: required string name
    3: required list<string> modules
    4: required string commitID
    5: required string branch
    6: required i64 id
    7: required bool canRemove
}

struct GetRepoListResp{
    1: required list<HotfixRepo> repos
    255: optional base.BaseResp BaseResp
}

struct DeleteRepoListReq{
    1: required i64 repoID
    255: optional base.Base Base
}

struct InviteUserJoinLarkGroupReq{
    1: required i64 taskID
    2: required string userName
    255: optional base.Base Base
}

struct InviteUserJoinLarkGroupResp{
    1: required string chatID
    255: optional base.BaseResp BaseResp
}

struct GetHotfixBranchInfoReq{
    1: required i64 publishID
    255: optional base.Base Base
}

struct RepoBranchInfo{
    1: required string name
    2: required i64 projectID
    3: required string commitID
    4: required string branch
    5: required i64 iid
    6: required list<string> modules
}

struct GetHotfixBranchInfoResp{
    1: required list<RepoBranchInfo> repos
    255: optional base.BaseResp BaseResp
}

struct GetHotfixBuildBranchInfoReq{
    1: required i64 buildID
    255: optional base.Base Base
}

struct BuildBranchInfo{
    1: required string name
    2: required string commitID
    3: required string branch
    4: required string gitUrl
    5: optional list<string> modules
    6: optional i64 projectID
}

struct GetHotfixBuildBranchInfoResp{
    1: required list<BuildBranchInfo> repos
    255: optional base.BaseResp BaseResp
}

struct GetHotfixPublishTimelineReq{
    1: required i64 publishID
    255: optional base.Base Base
}

struct PublishTimeline{
    1: required i64 id
    2: required string message
    3: required string operator
    4: required i64 createAt
}

struct GetHotfixPublishTimelineResp{
    1: required list<PublishTimeline> timeline
    255: optional base.BaseResp BaseResp
}

struct CreatePublishTimelineReq{
    1: required i64 publishID
    2: required string message
    3: required string operator
    255: optional base.Base Base
}

struct CreatePublishTimelineResp{
    1: required PublishTimeline timeline
    255: optional base.BaseResp BaseResp
}

struct SearchHotfixTaskReq{
    1: required i64 groupID
    2: optional ApproveStatus approveStatus
    3: optional string version
    4: optional string name
    5: optional string author
    6: optional string module
    7: optional string businessGroupName
    8: optional i64 lastID
    9: optional bool aboutMe
    10: optional string username
    11: optional i64 limit
    12: optional i64 isTestHotfix
    255: optional base.Base Base
}

struct HotfixListVersionTag{
    1: required string versionLong
    2: required i64 versionID
}

struct HotfixTaskItem{
    1: required i64 id
    2: required string title
    3: required string businessGroupName
    4: required string taskStatus
    5: required string author
    6: required i64 createAt
    7: required string approvalStatus
    8: required list<HotfixListVersionTag> versions
}

struct GetHotfixTaskListResp{
    1: required list<i64> ids
    2: required i64 total
    255: optional base.BaseResp BaseResp
}

struct GetHotfixTaskByIDsReq{
    1: required list<i64> ids
    255: optional base.Base Base
}

struct GetHotfixTaskByIDsResp{
    1: required list<HotfixTaskItem> List (go.tag = "json:\"list\"")
    255: optional base.BaseResp BaseResp
}

struct DynamicPkgTaskItem{
    1: required i64 id
    2: required string title
    3: required string businessGroupName
    4: required string taskStatus
    5: required string author
    6: required i64 createAt
    7: required string approvalStatus
    8: required list<DynamicPkgVersionTag> versions
}

struct GetDynamicPkgTaskByIDsResp{
    1: required list<DynamicPkgTaskItem> List (go.tag = "json:\"list\"")
    255: optional base.BaseResp BaseResp
}

struct DynamicPkgVersionTag{
    1: required string versionLong
    2: required i64 versionID
    3: required i64 appID
}

struct TerminatePublishReq{
    1: required i64 publishID
    2: required string operator
    255: optional base.Base Base
}

struct GetHotfixTaskIDByTicketIDReq{
    1: required string ticketID
    255: optional base.Base Base
}

struct GetHotfixTaskIDByTicketIDResp{
    1: required i64 taskID
    255: optional base.BaseResp BaseResp
}

struct GetHotfixIntegrationInfoReq{
    1: required i64 publishID
    255: optional base.Base Base
}

struct GetHotfixIntegrationInfoResp{
    1: required string taskID
    2: required string targetBranch
    3: required string status
    4: required string commitID
    5: required i64 projectID
    6: required string gitUrl
    7: required string message
    8: required string trace_id
    255: optional base.BaseResp BaseResp
}

enum hotfixBuildType{
    Package,
    AutomatedTest,
}

struct GetHotfixBuildDetailReq{
    1: required i64 publishID
    2: required hotfixBuildType type
    255: optional base.Base Base
}

enum ArtifactType {
    SaveU = 0
    Debug = 1
}

enum SaveuHotfixPkgStatus {
    ReleaseHotfixPkgNotEffect = 0
    ReleaseHotfixPkgInEffect = 1
    ReleaseHotfixPkgOffline = 2
}

struct HotfixArtifact {
    1: required string url,
    2: required string md5,
    3: required i64 size,
    4: optional i64 id=0,
    5: optional ArtifactType artifact_type,// 产物类型 （debug，上传saveu）
    6: optional string saveu_pkg_version,
    7: optional SaveuHotfixPkgStatus status,// saveu 上热修包的状态
    8: optional i64 publishID
}

struct GetHotfixBuildDetailResp{
    1: required i64 pipelineID
    2: required string status
    3: required i64 projectID
    4: required string gitUrl
    5: required string commitID
    6: required string branch
    7: required list<HotfixArtifact> artifacts
    8: required i64 createAt
    9: required i64 updateAt
    10: required string targetBranch
    255: optional base.BaseResp BaseResp (go.tag="json:\"-\"")
}

struct GetHotfixPipelineIDByPublishReq{
    1: required i64 publishID
    255: optional base.Base Base
}

struct GetHotfixPipelineIDByPublishResp{
    1: required i64 pipelineID
    255: optional base.BaseResp BaseResp
}

struct CloseHotfixQuery{
    1: required i64 publishID
    255: optional base.Base Base
}

struct GetMboxLinkReq{
    1: required i64 versionID
    255: optional base.Base Base
}

struct mboxRepoItem{
    1: required string name
    2: required string gitUrl
    3: required string sourceBranch
    4: required string targetBranch
    5: required list<string> modules
}

struct GetVersionListByAppIDReq{
    1: required i64 appID
    255: optional base.Base Base
}

struct GetVersionListByAppIDResp{
    1: required list<string> versions
    255: optional base.BaseResp BaseResp
}

struct GetMboxLinkResp{
    1: required list<mboxRepoItem> repos
    255: optional base.BaseResp BaseResp
}

struct HotfixBuildRepo{
    1: required i64 repoID
    2: required list<string> modules
}

enum HotfixFramework{
    Robust,
    Reparo,
    Unknown = 255,
}

enum HotfixPackageArchitecture{
    armhf,
    arm64,
    mixed,
    default,
    unknown = 255,
}

struct CreateHotfixBuildReq{
    1: required i64 versionID
    2: required list<HotfixBuildRepo> repos
    3: required bool javaNomi
    4: required string operator
    5: required map<HotfixFramework,list<HotfixPackageArchitecture>> variants
    6: optional string channel
    7: optional map<string, string> env_value,
    255: optional base.Base Base
}

struct CreateHotfixBuildResp{
    1: required i64 buildID
    255: optional base.BaseResp BaseResp
}

struct GetHotfixBuildListReq{
    1: required i64 versionID
    255: optional base.Base Base
}

struct HotfixPackage{
    1: required i64 id
    2: required i64 pipelineID
    3: required string status
    4: required bool javaOnly
    5: required HotfixPackageArchitecture arch
    6: required HotfixFramework framework
    7: required i64 created_at
    8: required i64 updated_at
}

struct HotfixBuild{
    1: required i64 id
    2: required string mainRepoCommit
    3: required string extra
    4: required list<HotfixPackage> pkgs
    5: required i64 createAt
    6: required bool javaOnly
    7: required HotfixFramework framework
    8: required string operator
    9: optional string channel
}

struct GetHotfixBuildListResp{
    1: required list<HotfixBuild> List (go.tag = "json:\"list\"")
    255: optional base.BaseResp BaseResp
}

struct CreatAndroidHotfixPublisReq{
    1: required i64 packageID
    2: required string operator
    255: optional base.Base Base
}

struct GetHotfixBuildInfoByIDReq{
    1: required i64 buildID
    255: optional base.Base Base
}

struct BaseInfo{
    1: required string mapping
    2: required string apk
    3: required string logs
}

struct GetHotfixBuildInfoByIDResp{
    1: required HotfixBuild Build
    2: required map<string,BaseInfo> baseInfo
    255: optional base.BaseResp BaseResp (go.tag = "json:\"-\"")
}

struct GetHotfixPackageInfoReq{
    1: required i64 packageID
    255: optional base.Base Base
}

struct GetHotfixPackageInfoResp{
    1: required i64 PipelineID
    2: required string status
    3: required HotfixPackageArchitecture architecture
    4: required HotfixFramework framework
    5: required i64 createdAt
    6: required i64 updated_at
    7: required list<HotfixArtifact> artifacts
    8: required string commitID
    9: required string branch
    10: required string gitUrl
    11: optional list<Publish> publishList
    255: optional base.BaseResp BaseResp (go.tag = "json:\"-\"")
}

struct EditRepoModulesReq{
    1: required i64 repoID
    2: required list<string> modules
    255: optional base.Base Base
}

struct RetryHotfixBuildReq{
    1: required i64 publishID
    2: required string operator
    255: optional base.Base Base
}