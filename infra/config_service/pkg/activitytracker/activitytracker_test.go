package activitytracker

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	diff "github.com/r3labs/diff/v3"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/mapper"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/gopkg/ctxvalues"
	logs "code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
)

func TestActivityTracker_RecordEditWorkflowConfigurationOperationAutoGen(t *testing.T) {
	// Verify the functionality of RecordEditWorkflowConfigurationOperation method.
	t.Run("testActivityTracker_RecordEditWorkflowConfigurationOperation", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var logIDDefaultRet1Mock string
			mockey.Mock(ctxvalues.LogIDDefault).Return(logIDDefaultRet1Mock).Build()

			var onesiteWorkflowMockPtrValue config_service.OnesiteWorkflow
			onesiteWorkflowMock := &onesiteWorkflowMockPtrValue
			mockey.Mock(mapper.TransformWorkflowRealtimePartToWorkflow).Return(onesiteWorkflowMock).Build()

			var v2MockPtrValue logs.ByteDLogger
			v2Mock := &v2MockPtrValue
			mockey.MockValue(&log.V2).To(v2Mock)

			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*events.ProducerWrapper).SendInfoRecord).Return(rMock).Build()

			var transformDiffChangesToColumnDiffsRet1MockItem0 ColumnDiff
			transformDiffChangesToColumnDiffsRet1Mock := []ColumnDiff{transformDiffChangesToColumnDiffsRet1MockItem0,}
			mockey.Mock(TransformDiffChangesToColumnDiffs).Return(transformDiffChangesToColumnDiffsRet1Mock).Build()

			// prepare parameters
			var spaceId int64
			var workflowId int64
			var beforePtrValue entity.WorkflowRealtimePart
			before := &beforePtrValue
			var afterPtrValue entity.WorkflowRealtimePart
			after := &afterPtrValue
			var receiverPtrValue ActivityTracker
			receiver := &receiverPtrValue
			var receiverJsonDifferPtrValue diff.Differ
			receiver.jsonDiffer = &receiverJsonDifferPtrValue
			ctx := context.Background()
			var updater string
			convey.So(func() { receiver.RecordEditWorkflowConfigurationOperation(ctx, updater, spaceId, workflowId, before, after) }, convey.ShouldPanic)
		})
	})

}

func TestNewColumnDiffAutoGen(t *testing.T) {
	// Verify the behavior of NewColumnDiff under default conditions.
	t.Run("testNewColumnDiff_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var diffColumn string
			var oldValue string
			var newValue string

			// run target function and assert
			got1 := NewColumnDiff(diffColumn, oldValue, newValue)
			convey.So(got1.NewValue, convey.ShouldBeBlank)
			convey.So(got1.DiffColumn, convey.ShouldBeBlank)
			convey.So(got1.OldValue, convey.ShouldBeBlank)
		})
	})

}

func TestNewActivityTrackerAutoGen(t *testing.T) {
	// github.com/r3labs/diff/v3:NewDiffer()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var newDifferRet1MockPtrValue diff.Differ
			newDifferRet1Mock := &newDifferRet1MockPtrValue
			newDifferRet2Mock := fmt.Errorf("error")
			mockey.Mock(diff.NewDiffer).Return(newDifferRet1Mock, newDifferRet2Mock).Build()

			// prepare parameters
			var producerPtrValue events.ProducerWrapper
			producer := &producerPtrValue
			convey.So(func() { _ = NewActivityTracker(producer) }, convey.ShouldPanic)
		})
	})

	// github.com/r3labs/diff/v3:NewDiffer()_ret-2 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var newDifferRet1MockPtrValue diff.Differ
			newDifferRet1Mock := &newDifferRet1MockPtrValue
			var newDifferRet2Mock error
			mockey.Mock(diff.NewDiffer).Return(newDifferRet1Mock, newDifferRet2Mock).Build()

			// prepare parameters
			var producerPtrValue events.ProducerWrapper
			producer := &producerPtrValue

			// run target function and assert
			got1 := NewActivityTracker(producer)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

