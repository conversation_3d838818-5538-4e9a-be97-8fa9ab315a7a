package activitytracker

import (
	"strings"
	"testing"

	"github.com/bytedance/mockey"
	diff "github.com/r3labs/diff/v3"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/bits/hephaestus/pkg/jsons"
)

func TestTransformDiffChangesToColumnDiffsAutoGen(t *testing.T) {
	// len(param1) > 0
	// strings:HasPrefix()_ret-1 == true
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var joinRet1Mock string
			mockey.Mock(strings.Join).Return(joinRet1Mock).Build()

			hasPrefixRet1Mock := true
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var columnDiffMock ColumnDiff
			mockey.Mock(NewColumnDiff).Return(columnDiffMock).Build()

			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			// prepare parameters
			var changesItem0 diff.Change
			changes := []diff.Change{changesItem0,}

			// run target function and assert
			got1 := TransformDiffChangesToColumnDiffs(changes)
			convey.So(len(got1), convey.ShouldEqual, 1)
		})
	})

}

