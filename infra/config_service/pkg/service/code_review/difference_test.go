package code_review

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
)

func TestCodeReview_GetConfigDetailDifferenceAutoGen(t *testing.T) {
	// Verify the result when receiver.OldDetail is nil.
	t.Run("testCodeReview_GetConfigDetailDifference_OldDetailNil", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.GetConfigDetailDifference(ctx)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestCodeReview_GetRulesDifferenceAutoGen(t *testing.T) {
	// Verify the function behavior when OldDetail is not null.
	t.Run("testCodeReview_GetRulesDifference_OldDetailNotNull", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			var receiverOldDetailPtrValue db.OptimusConfigCodeReviewDetail
			receiver.OldDetail = &receiverOldDetailPtrValue
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.GetRulesDifference(ctx)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).UpdateList), convey.ShouldEqual, 0)
			convey.So(len((*got1).OldList), convey.ShouldEqual, 0)
			convey.So(len((*got1).CreateList), convey.ShouldEqual, 0)
			convey.So(len((*got1).DeleteList), convey.ShouldEqual, 0)
		})
	})

}

