package code_review

import (
	"fmt"
	"regexp"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bits/devops/cachyper"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
)

/**
 * @Date: 2023/5/15
 * @Author: Todd "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

func TestMergeCodeReviewRules2(t *testing.T) {
	t.Run("hit independent rules", func(t *testing.T) {
		t.SkipNow()
	
		files := []*cachyper.CodeReviewRuleOwnersFile{
			{
				Filepath: ".bits/config",
				Config:   &cachyper.CodeReviewRulesFileConfig{Maintainers: make([]string, 0)},
				Rules: []*cachyper.CodeReviewRule{
					{
						Category: cachyper.CodeReviewRuleCategory_TARGET_BRANCH,
						Pattern:  "develop",
						Reviewers: []string{
							"<EMAIL>",
							"<EMAIL>",
						},
						RequiredApprovals: 1,
						Pattern2:          "develop",
					},
					{
						Category:          0,
						Pattern:           "app/src/main/kotlin/net/bytedance/bits/App.kt",
						Reviewers:         []string{"jiangyunong"},
						RequiredApprovals: 1,
						Pattern2:          "app/src/main/kotlin/net/bytedance/bits/App.kt",
					},
				},
			},
		}
	
		actual := MergeCodeReviewRules2(files)
	
		fmt.Println(jsons.Stringify(actual))
	})
}

func TestMergeCodeReviewRules2AutoGen(t *testing.T) {
	// Verify the function behavior when the input is empty.
	t.Run("testMergeCodeReviewRules2_EmptyInput", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			// prepare parameters
			files := []*cachyper.CodeReviewRuleOwnersFile{}
			var files0ItemPtrValue cachyper.CodeReviewRuleOwnersFile
			files0Item := &files0ItemPtrValue
			files = append(files, files0Item)

			// run target function and assert
			got1 := MergeCodeReviewRules2(files)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when there is a single rule in the input.
	t.Run("testMergeCodeReviewRules2_SingleRule", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			// prepare parameters
			files := []*cachyper.CodeReviewRuleOwnersFile{}
			var files0ItemPtrValue cachyper.CodeReviewRuleOwnersFile
			files0Item := &files0ItemPtrValue
			files = append(files, files0Item)
			files[0].Rules = []*cachyper.CodeReviewRule{}
			var files0Rules0ItemPtrValue cachyper.CodeReviewRule
			files0Rules0Item := &files0Rules0ItemPtrValue
			files[0].Rules = append(files[0].Rules, files0Rules0Item)

			// run target function and assert
			got1 := MergeCodeReviewRules2(files)
			convey.So(len(got1), convey.ShouldEqual, 1)
		})
	})

	// Verify the function behavior when there is a single rule with config in the input.
	t.Run("testMergeCodeReviewRules2_SingleRuleWithConfig", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			// prepare parameters
			files := []*cachyper.CodeReviewRuleOwnersFile{}
			var files0ItemPtrValue cachyper.CodeReviewRuleOwnersFile
			files0Item := &files0ItemPtrValue
			files = append(files, files0Item)
			files[0].Rules = []*cachyper.CodeReviewRule{}
			var files0Rules0ItemPtrValue cachyper.CodeReviewRule
			files0Rules0Item := &files0Rules0ItemPtrValue
			files[0].Rules = append(files[0].Rules, files0Rules0Item)
			var files0ConfigPtrValue cachyper.CodeReviewRulesFileConfig
			files[0].Config = &files0ConfigPtrValue

			// run target function and assert
			got1 := MergeCodeReviewRules2(files)
			convey.So(len(got1), convey.ShouldEqual, 1)
		})
	})

	// Verify the function behavior when there is a single rule with a specific category in the input.
	t.Run("testMergeCodeReviewRules2_SingleRuleWithCategory", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			// prepare parameters
			files := []*cachyper.CodeReviewRuleOwnersFile{}
			var files0ItemPtrValue cachyper.CodeReviewRuleOwnersFile
			files0Item := &files0ItemPtrValue
			files = append(files, files0Item)
			files[0].Rules = []*cachyper.CodeReviewRule{}
			var files0Rules0ItemPtrValue cachyper.CodeReviewRule
			files0Rules0Item := &files0Rules0ItemPtrValue
			files[0].Rules = append(files[0].Rules, files0Rules0Item)
			files0Rules0CategoryAlias := int64(1)
			files[0].Rules[0].Category = cachyper.CodeReviewRuleCategory(files0Rules0CategoryAlias)

			// run target function and assert
			got1 := MergeCodeReviewRules2(files)
			convey.So(len(got1), convey.ShouldEqual, 1)
		})
	})

	// Verify the function behavior under the default condition.
	t.Run("testMergeCodeReviewRules2_Default", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			// prepare parameters
			var files []*cachyper.CodeReviewRuleOwnersFile

			// run target function and assert
			got1 := MergeCodeReviewRules2(files)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func Test_findMatchedRuleSetAutoGen(t *testing.T) {
	// Verify the function behavior when the input parameters are not empty and the rule type is branch.
	t.Run("testFindMatchedRuleSet_NotEmptyAndBranchType", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var matchStringRet1Mock bool
			var matchStringRet2Mock error
			mockey.Mock(regexp.MatchString).Return(matchStringRet1Mock, matchStringRet2Mock).Build()

			// prepare parameters
			targetBranch := "not "
			bitsRules := []*db.OptimusConfigReviewRules{}
			var bitsRules0ItemPtrValue db.OptimusConfigReviewRules
			bitsRules0Item := &bitsRules0ItemPtrValue
			bitsRules = append(bitsRules, bitsRules0Item)
			bitsRules[0].Type = "branch"

			// run target function and assert
			got1 := findMatchedRuleSet(targetBranch, bitsRules)
			convey.So(got1, convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when the input parameters are not empty, the rule type is branch and the regex match is true.
	t.Run("testFindMatchedRuleSet_NotEmptyAndBranchType_MatchTrue", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			matchStringRet1Mock := true
			var matchStringRet2Mock error
			mockey.Mock(regexp.MatchString).Return(matchStringRet1Mock, matchStringRet2Mock).Build()

			// prepare parameters
			bitsRules := []*db.OptimusConfigReviewRules{}
			var bitsRules0ItemPtrValue db.OptimusConfigReviewRules
			bitsRules0Item := &bitsRules0ItemPtrValue
			bitsRules = append(bitsRules, bitsRules0Item)
			bitsRules[0].Type = "branch"
			targetBranch := "not "

			// run target function and assert
			got1 := findMatchedRuleSet(targetBranch, bitsRules)
			convey.So(got1, convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior under default conditions.
	t.Run("testFindMatchedRuleSet_Default", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var matchStringRet1Mock bool
			var matchStringRet2Mock error
			mockey.Mock(regexp.MatchString).Return(matchStringRet1Mock, matchStringRet2Mock).Build()

			// prepare parameters
			var targetBranch string
			var bitsRules []*db.OptimusConfigReviewRules

			// run target function and assert
			got1 := findMatchedRuleSet(targetBranch, bitsRules)
			convey.So(got1, convey.ShouldEqual, 0)
		})
	})

}

