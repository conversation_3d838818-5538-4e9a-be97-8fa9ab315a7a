package code_review

import (
	"context"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/operation_record"
	"code.byted.org/devinfra/hagrid/infra/config_service/service/tcc"
)

func TestCodeReview_StoreConfigOperationAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/infra/config_service/service/tcc:VerifyEnable()_ret-1 == true
	// len(param2) > 0
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			verifyEnableRet1Mock := true
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			var getConfigDetailDifferenceRet1Mock []*operation_record.ValueDifferenceItem
			mockey.Mock((*CodeReview).GetConfigDetailDifference).Return(getConfigDetailDifferenceRet1Mock).Build()

			mockey.Mock(operation_record.StoreProjectOperations).Return().Build()

			var reviewRulesDifferenceMockPtrValue ReviewRulesDifference
			reviewRulesDifferenceMock := &reviewRulesDifferenceMockPtrValue
			mockey.Mock((*CodeReview).GetRulesDifference).Return(reviewRulesDifferenceMock).Build()

			// prepare parameters
			ctx := context.Background()
			userName := "a"
			var configType consts.ProjectConfigType
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			convey.So(func() { receiver.StoreConfigOperation(ctx, userName, configType) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/service/tcc:VerifyEnable()_ret-1 == true
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:GetRulesDifference()_ret-1 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getConfigDetailDifferenceRet1Mock []*operation_record.ValueDifferenceItem
			mockey.Mock((*CodeReview).GetConfigDetailDifference).Return(getConfigDetailDifferenceRet1Mock).Build()

			mockey.Mock(operation_record.StoreProjectOperations).Return().Build()

			var reviewRulesDifferenceMock *ReviewRulesDifference
			mockey.Mock((*CodeReview).GetRulesDifference).Return(reviewRulesDifferenceMock).Build()

			verifyEnableRet1Mock := true
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var userName string
			var configType consts.ProjectConfigType
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			convey.So(func() { receiver.StoreConfigOperation(ctx, userName, configType) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/service/tcc:VerifyEnable()_ret-1 == true
	// len(code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:GetConfigDetailDifference()_ret-1) > 0
	// len(:make()_ret-1) != 0
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var getConfigDetailDifferenceRet1MockItem0PtrValue operation_record.ValueDifferenceItem
			getConfigDetailDifferenceRet1MockItem0 := &getConfigDetailDifferenceRet1MockItem0PtrValue
			getConfigDetailDifferenceRet1Mock := []*operation_record.ValueDifferenceItem{getConfigDetailDifferenceRet1MockItem0,}
			mockey.Mock((*CodeReview).GetConfigDetailDifference).Return(getConfigDetailDifferenceRet1Mock).Build()

			mockey.Mock(operation_record.StoreProjectOperations).Return().Build()

			var reviewRulesDifferenceMockPtrValue ReviewRulesDifference
			reviewRulesDifferenceMock := &reviewRulesDifferenceMockPtrValue
			mockey.Mock((*CodeReview).GetRulesDifference).Return(reviewRulesDifferenceMock).Build()

			verifyEnableRet1Mock := true
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var userName string
			var configType consts.ProjectConfigType
			convey.So(func() { receiver.StoreConfigOperation(ctx, userName, configType) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/service/tcc:VerifyEnable()_ret-1 == true
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:GetRulesDifference()_ret-1 != nil
	// len(code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:GetRulesDifference()_ret-1.CreateList) > 0
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var getConfigDetailDifferenceRet1Mock []*operation_record.ValueDifferenceItem
			mockey.Mock((*CodeReview).GetConfigDetailDifference).Return(getConfigDetailDifferenceRet1Mock).Build()

			mockey.Mock(operation_record.StoreProjectOperations).Return().Build()

			var reviewRulesDifferenceMockPtrValue ReviewRulesDifference
			reviewRulesDifferenceMock := &reviewRulesDifferenceMockPtrValue
			var reviewRulesDifferenceMockCreateListItem0PtrValue ReviewRuleDifferenceItem
			reviewRulesDifferenceMockCreateListItem0 := &reviewRulesDifferenceMockCreateListItem0PtrValue
			reviewRulesDifferenceMock.CreateList = []*ReviewRuleDifferenceItem{reviewRulesDifferenceMockCreateListItem0,}
			mockey.Mock((*CodeReview).GetRulesDifference).Return(reviewRulesDifferenceMock).Build()

			verifyEnableRet1Mock := true
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var userName string
			var configType consts.ProjectConfigType
			convey.So(func() { receiver.StoreConfigOperation(ctx, userName, configType) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/service/tcc:VerifyEnable()_ret-1 == true
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:GetRulesDifference()_ret-1 != nil
	// len(code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:GetRulesDifference()_ret-1.DeleteList) > 0
	t.Run("case_4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			verifyEnableRet1Mock := true
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			var getConfigDetailDifferenceRet1Mock []*operation_record.ValueDifferenceItem
			mockey.Mock((*CodeReview).GetConfigDetailDifference).Return(getConfigDetailDifferenceRet1Mock).Build()

			mockey.Mock(operation_record.StoreProjectOperations).Return().Build()

			var reviewRulesDifferenceMockPtrValue ReviewRulesDifference
			reviewRulesDifferenceMock := &reviewRulesDifferenceMockPtrValue
			var reviewRulesDifferenceMockDeleteListItem0PtrValue ReviewRuleDifferenceItem
			reviewRulesDifferenceMockDeleteListItem0 := &reviewRulesDifferenceMockDeleteListItem0PtrValue
			reviewRulesDifferenceMock.DeleteList = []*ReviewRuleDifferenceItem{reviewRulesDifferenceMockDeleteListItem0,}
			mockey.Mock((*CodeReview).GetRulesDifference).Return(reviewRulesDifferenceMock).Build()

			// prepare parameters
			ctx := context.Background()
			var userName string
			var configType consts.ProjectConfigType
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			convey.So(func() { receiver.StoreConfigOperation(ctx, userName, configType) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/service/tcc:VerifyEnable()_ret-1 == true
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:GetRulesDifference()_ret-1 != nil
	// len(code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:GetRulesDifference()_ret-1.UpdateList) > 0
	t.Run("case_5", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// mock function returns or global values
			mockey.Mock(operation_record.StoreProjectOperations).Return().Build()

			var reviewRulesDifferenceMockPtrValue ReviewRulesDifference
			reviewRulesDifferenceMock := &reviewRulesDifferenceMockPtrValue
			var reviewRulesDifferenceMockUpdateListItem0PtrValue UpdateReviewRuleDifferenceItem
			reviewRulesDifferenceMockUpdateListItem0 := &reviewRulesDifferenceMockUpdateListItem0PtrValue
			reviewRulesDifferenceMock.UpdateList = []*UpdateReviewRuleDifferenceItem{reviewRulesDifferenceMockUpdateListItem0,}
			mockey.Mock((*CodeReview).GetRulesDifference).Return(reviewRulesDifferenceMock).Build()

			verifyEnableRet1Mock := true
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			var getConfigDetailDifferenceRet1Mock []*operation_record.ValueDifferenceItem
			mockey.Mock((*CodeReview).GetConfigDetailDifference).Return(getConfigDetailDifferenceRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var userName string
			var configType consts.ProjectConfigType
			convey.So(func() { receiver.StoreConfigOperation(ctx, userName, configType) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// default condition
	t.Run("case_6", func(t *testing.T) {
		mockey.PatchConvey("case_6", t, func() {
			// mock function returns or global values
			var verifyEnableRet1Mock bool
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			var getConfigDetailDifferenceRet1Mock []*operation_record.ValueDifferenceItem
			mockey.Mock((*CodeReview).GetConfigDetailDifference).Return(getConfigDetailDifferenceRet1Mock).Build()

			mockey.Mock(operation_record.StoreProjectOperations).Return().Build()

			var reviewRulesDifferenceMockPtrValue ReviewRulesDifference
			reviewRulesDifferenceMock := &reviewRulesDifferenceMockPtrValue
			mockey.Mock((*CodeReview).GetRulesDifference).Return(reviewRulesDifferenceMock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var userName string
			var configType consts.ProjectConfigType
			convey.So(func() { receiver.StoreConfigOperation(ctx, userName, configType) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

}

