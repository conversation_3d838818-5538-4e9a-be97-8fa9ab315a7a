package code_review

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bits/devops/cachyper"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/code_review"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/data"
	"code.byted.org/gopkg/facility/log"
)

func TestCodeReview_GetReviewRuleItemAutoGen(t *testing.T) {
	// Verify the function behavior when Unmarshal is successful.
	t.Run("testCodeReview_GetReviewRuleItem_UnmarshalSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			// prepare parameters
			var operator string
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var rulePtrValue db.OptimusConfigReviewRules
			rule := &rulePtrValue

			// run target function and assert
			got1, got2 := receiver.GetReviewRuleItem(ctx, rule, operator)
			convey.So(len((*got1).Reviewers), convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).MinNumber, convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when Unmarshal fails.
	t.Run("testCodeReview_GetReviewRuleItem_UnmarshalFailure", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			unmarshalRet1Mock := fmt.Errorf("error")
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var rulePtrValue db.OptimusConfigReviewRules
			rule := &rulePtrValue
			var operator string
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue

			// run target function and assert
			got1, got2 := receiver.GetReviewRuleItem(ctx, rule, operator)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

func Test_adaptCatalogPrefixAndReviewRuleAutoGen(t *testing.T) {
	// strings:HasSuffix()_ret-1 == true
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			hasSuffixRet1Mock := true
			mockey.Mock(strings.HasSuffix, mockey.OptUnsafe).Return(hasSuffixRet1Mock).Build()

			var trimSuffixRet1Mock string
			mockey.Mock(strings.TrimSuffix, mockey.OptUnsafe).Return(trimSuffixRet1Mock).Build()

			// prepare parameters
			var rule string

			// run target function and assert
			got1 := adaptCatalogPrefixAndReviewRule(rule)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Rule, convey.ShouldEqual, ".*")
			convey.So((*got1).Prefix, convey.ShouldBeBlank)
		})
	})

	// default condition
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var hasSuffixRet1Mock bool
			mockey.Mock(strings.HasSuffix, mockey.OptUnsafe).Return(hasSuffixRet1Mock).Build()

			var trimSuffixRet1Mock string
			mockey.Mock(strings.TrimSuffix, mockey.OptUnsafe).Return(trimSuffixRet1Mock).Build()

			// prepare parameters
			var rule string

			// run target function and assert
			got1 := adaptCatalogPrefixAndReviewRule(rule)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Rule, convey.ShouldEqual, ".*")
			convey.So((*got1).Prefix, convey.ShouldBeBlank)
		})
	})

}

func TestCodeReview_GetReviewRuleItemByVersionAutoGen(t *testing.T) {
	// param2 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItem).Return(reviewRuleItemMock, getReviewRuleItemRet2Mock).Build()

			var getReviewRuleItemWithRegionMatchRet1MockPtrValue reviewRuleItem
			getReviewRuleItemWithRegionMatchRet1Mock := &getReviewRuleItemWithRegionMatchRet1MockPtrValue
			var getReviewRuleItemWithRegionMatchRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemWithRegionMatch).Return(getReviewRuleItemWithRegionMatchRet1Mock, getReviewRuleItemWithRegionMatchRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var rule *db.OptimusConfigReviewRules
			var operator string
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.GetReviewRuleItemByVersion(ctx, rule, operator, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// param2 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItem).Return(reviewRuleItemMock, getReviewRuleItemRet2Mock).Build()

			var getReviewRuleItemWithRegionMatchRet1MockPtrValue reviewRuleItem
			getReviewRuleItemWithRegionMatchRet1Mock := &getReviewRuleItemWithRegionMatchRet1MockPtrValue
			var getReviewRuleItemWithRegionMatchRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemWithRegionMatch).Return(getReviewRuleItemWithRegionMatchRet1Mock, getReviewRuleItemWithRegionMatchRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var rulePtrValue db.OptimusConfigReviewRules
			rule := &rulePtrValue
			var operator string
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.GetReviewRuleItemByVersion(ctx, rule, operator, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestCodeReview_AdaptCodeReviewDetailAndRulesDBToIDLAutoGen(t *testing.T) {
	// Verify the function behavior when append() return length is 0 and make() result does not contain a certain element.
	t.Run("testCodeReview_AdaptDBToIDL_EmptyAppendAndMake", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var catalogReviewDetailMockPtrValue CatalogReviewDetail
			catalogReviewDetailMock := &catalogReviewDetailMockPtrValue
			mockey.Mock(adaptCatalogPrefixAndReviewRule).Return(catalogReviewDetailMock).Build()

			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			var optimusConfigReviewRuleSetConfigMockPtrValue db.OptimusConfigReviewRuleSetConfig
			optimusConfigReviewRuleSetConfigMock := &optimusConfigReviewRuleSetConfigMockPtrValue
			var getReviewRuleSetConfigByIDRet2Mock error
			mockey.Mock(data.GetReviewRuleSetConfigByID).Return(optimusConfigReviewRuleSetConfigMock, getReviewRuleSetConfigByIDRet2Mock).Build()

			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemByVersionRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemByVersion).Return(reviewRuleItemMock, getReviewRuleItemByVersionRet2Mock).Build()

			mockey.Mock(log.CtxError, mockey.OptUnsafe).Return().Build()

			// prepare parameters
			var rules []*db.OptimusConfigReviewRules
			var regionMatch bool
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var cacConfigPtrValue cachyper.CodeReviewRulesRepositoryConfig
			cacConfig := &cacConfigPtrValue
			var crDetailPtrValue db.OptimusConfigCodeReviewDetail
			crDetail := &crDetailPtrValue

			// run target function and assert
			got1, got2 := receiver.AdaptCodeReviewDetailAndRulesDBToIDL(ctx, cacConfig, crDetail, rules, regionMatch)
			convey.So((*got1).AllowStartReviewWip, convey.ShouldEqual, false)
			convey.So(len((*got1).ReviewBizGroups), convey.ShouldEqual, 0)
			convey.So((*got1).ReviewerResetMode == nil, convey.ShouldBeFalse)
			convey.So((*got1).BizGroupReviewEnable == nil, convey.ShouldBeFalse)
			convey.So((*got1).ReviewMode == nil, convey.ShouldBeFalse)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).NotResetBranch), convey.ShouldEqual, 0)
			convey.So((*got1).RuleAcquisitionMode == nil, convey.ShouldBeFalse)
			convey.So((*got1).MinReviewersNumber == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).ReviewRuleSetList), convey.ShouldEqual, 1)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// Verify the function behavior when sonic.Unmarshal() returns an error.
	t.Run("testCodeReview_AdaptDBToIDL_SonicUnmarshalError", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var catalogReviewDetailMockPtrValue CatalogReviewDetail
			catalogReviewDetailMock := &catalogReviewDetailMockPtrValue
			mockey.Mock(adaptCatalogPrefixAndReviewRule).Return(catalogReviewDetailMock).Build()

			unmarshalRet1Mock := fmt.Errorf("error")
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			var optimusConfigReviewRuleSetConfigMockPtrValue db.OptimusConfigReviewRuleSetConfig
			optimusConfigReviewRuleSetConfigMock := &optimusConfigReviewRuleSetConfigMockPtrValue
			var getReviewRuleSetConfigByIDRet2Mock error
			mockey.Mock(data.GetReviewRuleSetConfigByID).Return(optimusConfigReviewRuleSetConfigMock, getReviewRuleSetConfigByIDRet2Mock).Build()

			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemByVersionRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemByVersion).Return(reviewRuleItemMock, getReviewRuleItemByVersionRet2Mock).Build()

			mockey.Mock(log.CtxError, mockey.OptUnsafe).Return().Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var cacConfigPtrValue cachyper.CodeReviewRulesRepositoryConfig
			cacConfig := &cacConfigPtrValue
			var crDetailPtrValue db.OptimusConfigCodeReviewDetail
			crDetail := &crDetailPtrValue
			var rules []*db.OptimusConfigReviewRules
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.AdaptCodeReviewDetailAndRulesDBToIDL(ctx, cacConfig, crDetail, rules, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// Verify the function behavior when sonic.Unmarshal() returns successfully.
	t.Run("testCodeReview_AdaptDBToIDL_SonicUnmarshalSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemByVersionRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemByVersion).Return(reviewRuleItemMock, getReviewRuleItemByVersionRet2Mock).Build()

			mockey.Mock(log.CtxError, mockey.OptUnsafe).Return().Build()

			var catalogReviewDetailMockPtrValue CatalogReviewDetail
			catalogReviewDetailMock := &catalogReviewDetailMockPtrValue
			mockey.Mock(adaptCatalogPrefixAndReviewRule).Return(catalogReviewDetailMock).Build()

			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			var optimusConfigReviewRuleSetConfigMockPtrValue db.OptimusConfigReviewRuleSetConfig
			optimusConfigReviewRuleSetConfigMock := &optimusConfigReviewRuleSetConfigMockPtrValue
			var getReviewRuleSetConfigByIDRet2Mock error
			mockey.Mock(data.GetReviewRuleSetConfigByID).Return(optimusConfigReviewRuleSetConfigMock, getReviewRuleSetConfigByIDRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var cacConfigPtrValue cachyper.CodeReviewRulesRepositoryConfig
			cacConfig := &cacConfigPtrValue
			var crDetailPtrValue db.OptimusConfigCodeReviewDetail
			crDetail := &crDetailPtrValue
			var rules []*db.OptimusConfigReviewRules
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.AdaptCodeReviewDetailAndRulesDBToIDL(ctx, cacConfig, crDetail, rules, regionMatch)
			convey.So((*got1).RuleAcquisitionMode == nil, convey.ShouldBeFalse)
			convey.So((*got1).MinReviewersNumber == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).ReviewBizGroups), convey.ShouldEqual, 0)
			convey.So((*got1).ReviewerResetMode == nil, convey.ShouldBeFalse)
			convey.So((*got1).BizGroupReviewEnable == nil, convey.ShouldBeFalse)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).NotResetBranch), convey.ShouldEqual, 0)
			convey.So((*got1).AllowStartReviewWip, convey.ShouldEqual, false)
			convey.So((*got1).ReviewMode == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).ReviewRuleSetList), convey.ShouldEqual, 1)
			convey.So(got2 == nil, convey.ShouldBeTrue)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// Verify the function behavior when sonic.Unmarshal() returns successfully and BizGroupReview is enabled with related conditions.
	t.Run("testCodeReview_AdaptDBToIDL_SonicUnmarshalSuccessAndBizGroupReview", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemByVersionRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemByVersion).Return(reviewRuleItemMock, getReviewRuleItemByVersionRet2Mock).Build()

			mockey.Mock(log.CtxError, mockey.OptUnsafe).Return().Build()

			var catalogReviewDetailMockPtrValue CatalogReviewDetail
			catalogReviewDetailMock := &catalogReviewDetailMockPtrValue
			mockey.Mock(adaptCatalogPrefixAndReviewRule).Return(catalogReviewDetailMock).Build()

			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			var optimusConfigReviewRuleSetConfigMockPtrValue db.OptimusConfigReviewRuleSetConfig
			optimusConfigReviewRuleSetConfigMock := &optimusConfigReviewRuleSetConfigMockPtrValue
			var getReviewRuleSetConfigByIDRet2Mock error
			mockey.Mock(data.GetReviewRuleSetConfigByID).Return(optimusConfigReviewRuleSetConfigMock, getReviewRuleSetConfigByIDRet2Mock).Build()

			unmarshalStringRet1Mock := fmt.Errorf("error")
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var cacConfigPtrValue cachyper.CodeReviewRulesRepositoryConfig
			cacConfig := &cacConfigPtrValue
			var crDetailPtrValue db.OptimusConfigCodeReviewDetail
			crDetail := &crDetailPtrValue
			crDetail.ReviewBizGroups = "a"
			crDetail.BizGroupReviewEnable = true
			var rules []*db.OptimusConfigReviewRules
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.AdaptCodeReviewDetailAndRulesDBToIDL(ctx, cacConfig, crDetail, rules, regionMatch)
			convey.So(got2 == nil, convey.ShouldBeFalse)
			convey.So(got1 == nil, convey.ShouldBeTrue)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

}

func TestGetRuleByVersionAutoGen(t *testing.T) {
	// param2 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var optimusConfigReviewRuleSetConfigMockPtrValue db.OptimusConfigReviewRuleSetConfig
			optimusConfigReviewRuleSetConfigMock := &optimusConfigReviewRuleSetConfigMockPtrValue
			var getReviewRuleSetConfigByIDRet2Mock error
			mockey.Mock(data.GetReviewRuleSetConfigByID).Return(optimusConfigReviewRuleSetConfigMock, getReviewRuleSetConfigByIDRet2Mock).Build()

			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItem).Return(reviewRuleItemMock, getReviewRuleItemRet2Mock).Build()

			var getReviewRuleItemWithRegionMatchRet1MockPtrValue reviewRuleItem
			getReviewRuleItemWithRegionMatchRet1Mock := &getReviewRuleItemWithRegionMatchRet1MockPtrValue
			var getReviewRuleItemWithRegionMatchRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemWithRegionMatch).Return(getReviewRuleItemWithRegionMatchRet1Mock, getReviewRuleItemWithRegionMatchRet2Mock).Build()

			var ruleTypeMock code_review.RuleType
			var ruleTypeFromStringRet2Mock error
			mockey.Mock(RuleTypeFromString).Return(ruleTypeMock, ruleTypeFromStringRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var rule *db.OptimusConfigReviewRules
			var operator string

			// run target function and assert
			got1, got2 := GetRuleByVersion(ctx, rule, operator)
			convey.So(got2 == nil, convey.ShouldBeFalse)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param2 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var optimusConfigReviewRuleSetConfigMockPtrValue db.OptimusConfigReviewRuleSetConfig
			optimusConfigReviewRuleSetConfigMock := &optimusConfigReviewRuleSetConfigMockPtrValue
			var getReviewRuleSetConfigByIDRet2Mock error
			mockey.Mock(data.GetReviewRuleSetConfigByID).Return(optimusConfigReviewRuleSetConfigMock, getReviewRuleSetConfigByIDRet2Mock).Build()

			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItem).Return(reviewRuleItemMock, getReviewRuleItemRet2Mock).Build()

			var getReviewRuleItemWithRegionMatchRet1MockPtrValue reviewRuleItem
			getReviewRuleItemWithRegionMatchRet1Mock := &getReviewRuleItemWithRegionMatchRet1MockPtrValue
			var getReviewRuleItemWithRegionMatchRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemWithRegionMatch).Return(getReviewRuleItemWithRegionMatchRet1Mock, getReviewRuleItemWithRegionMatchRet2Mock).Build()

			var ruleTypeMock code_review.RuleType
			var ruleTypeFromStringRet2Mock error
			mockey.Mock(RuleTypeFromString).Return(ruleTypeMock, ruleTypeFromStringRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var rulePtrValue db.OptimusConfigReviewRules
			rule := &rulePtrValue
			var operator string

			// run target function and assert
			got1, got2 := GetRuleByVersion(ctx, rule, operator)
			convey.So(got2 == nil, convey.ShouldBeFalse)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param2 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review:RuleTypeFromString()_ret-2 != nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItem).Return(reviewRuleItemMock, getReviewRuleItemRet2Mock).Build()

			var getReviewRuleItemWithRegionMatchRet1MockPtrValue reviewRuleItem
			getReviewRuleItemWithRegionMatchRet1Mock := &getReviewRuleItemWithRegionMatchRet1MockPtrValue
			var getReviewRuleItemWithRegionMatchRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemWithRegionMatch).Return(getReviewRuleItemWithRegionMatchRet1Mock, getReviewRuleItemWithRegionMatchRet2Mock).Build()

			var ruleTypeMock code_review.RuleType
			ruleTypeFromStringRet2Mock := fmt.Errorf("error")
			mockey.Mock(RuleTypeFromString).Return(ruleTypeMock, ruleTypeFromStringRet2Mock).Build()

			var optimusConfigReviewRuleSetConfigMockPtrValue db.OptimusConfigReviewRuleSetConfig
			optimusConfigReviewRuleSetConfigMock := &optimusConfigReviewRuleSetConfigMockPtrValue
			var getReviewRuleSetConfigByIDRet2Mock error
			mockey.Mock(data.GetReviewRuleSetConfigByID).Return(optimusConfigReviewRuleSetConfigMock, getReviewRuleSetConfigByIDRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var rulePtrValue db.OptimusConfigReviewRules
			rule := &rulePtrValue
			var operator string

			// run target function and assert
			got1, got2 := GetRuleByVersion(ctx, rule, operator)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// param2 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review:RuleTypeFromString()_ret-2 == nil
	// param2.Type == "file"
	// param2.IsComponent == true
	// param2.SetConfigId > 0
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetReviewRuleSetConfigByID()_ret-2 != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var optimusConfigReviewRuleSetConfigMockPtrValue db.OptimusConfigReviewRuleSetConfig
			optimusConfigReviewRuleSetConfigMock := &optimusConfigReviewRuleSetConfigMockPtrValue
			getReviewRuleSetConfigByIDRet2Mock := fmt.Errorf("error")
			mockey.Mock(data.GetReviewRuleSetConfigByID).Return(optimusConfigReviewRuleSetConfigMock, getReviewRuleSetConfigByIDRet2Mock).Build()

			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItem).Return(reviewRuleItemMock, getReviewRuleItemRet2Mock).Build()

			var getReviewRuleItemWithRegionMatchRet1MockPtrValue reviewRuleItem
			getReviewRuleItemWithRegionMatchRet1Mock := &getReviewRuleItemWithRegionMatchRet1MockPtrValue
			var getReviewRuleItemWithRegionMatchRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemWithRegionMatch).Return(getReviewRuleItemWithRegionMatchRet1Mock, getReviewRuleItemWithRegionMatchRet2Mock).Build()

			var ruleTypeMock code_review.RuleType
			var ruleTypeFromStringRet2Mock error
			mockey.Mock(RuleTypeFromString).Return(ruleTypeMock, ruleTypeFromStringRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var rulePtrValue db.OptimusConfigReviewRules
			rule := &rulePtrValue
			rule.Type = "file"
			rule.SetConfigId = int64(1)
			rule.IsComponent = true
			var operator string

			// run target function and assert
			got1, got2 := GetRuleByVersion(ctx, rule, operator)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// param2 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review:RuleTypeFromString()_ret-2 == nil
	// param2.Type == "file"
	// param2.IsComponent == true
	// param2.SetConfigId > 0
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetReviewRuleSetConfigByID()_ret-2 == nil
	t.Run("case_4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var getReviewRuleItemWithRegionMatchRet1MockPtrValue reviewRuleItem
			getReviewRuleItemWithRegionMatchRet1Mock := &getReviewRuleItemWithRegionMatchRet1MockPtrValue
			var getReviewRuleItemWithRegionMatchRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItemWithRegionMatch).Return(getReviewRuleItemWithRegionMatchRet1Mock, getReviewRuleItemWithRegionMatchRet2Mock).Build()

			var ruleTypeMock code_review.RuleType
			var ruleTypeFromStringRet2Mock error
			mockey.Mock(RuleTypeFromString).Return(ruleTypeMock, ruleTypeFromStringRet2Mock).Build()

			var optimusConfigReviewRuleSetConfigMockPtrValue db.OptimusConfigReviewRuleSetConfig
			optimusConfigReviewRuleSetConfigMock := &optimusConfigReviewRuleSetConfigMockPtrValue
			var getReviewRuleSetConfigByIDRet2Mock error
			mockey.Mock(data.GetReviewRuleSetConfigByID).Return(optimusConfigReviewRuleSetConfigMock, getReviewRuleSetConfigByIDRet2Mock).Build()

			var reviewRuleItemMockPtrValue reviewRuleItem
			reviewRuleItemMock := &reviewRuleItemMockPtrValue
			var getReviewRuleItemRet2Mock error
			mockey.Mock((*CodeReview).GetReviewRuleItem).Return(reviewRuleItemMock, getReviewRuleItemRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var rulePtrValue db.OptimusConfigReviewRules
			rule := &rulePtrValue
			rule.Type = "file"
			rule.SetConfigId = int64(1)
			rule.IsComponent = true
			var operator string

			// run target function and assert
			got1, got2 := GetRuleByVersion(ctx, rule, operator)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestRuleTypeFromStringAutoGen(t *testing.T) {
	// Verify the function returns correct value for input "file"
	t.Run("testRuleTypeFromString_File", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			s := "file"

			// run target function and assert
			got1, got2 := RuleTypeFromString(s)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(int64(got1), convey.ShouldEqual, 1)
		})
	})

	// Verify the function returns correct value for input "branch"
	t.Run("testRuleTypeFromString_Branch", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			s := "branch"

			// run target function and assert
			got1, got2 := RuleTypeFromString(s)
			convey.So(int64(got1), convey.ShouldEqual, 2)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the function returns correct value for input "role"
	t.Run("testRuleTypeFromString_Role", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// prepare parameters
			s := "role"

			// run target function and assert
			got1, got2 := RuleTypeFromString(s)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(int64(got1), convey.ShouldEqual, 3)
		})
	})

	// Verify the function returns correct value for default condition
	t.Run("testRuleTypeFromString_Default", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// prepare parameters
			var s string

			// run target function and assert
			got1, got2 := RuleTypeFromString(s)
			convey.So(int64(got1), convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

