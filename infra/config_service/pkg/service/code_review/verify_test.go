package code_review

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/dlclark/regexp2"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
)

func TestVerifyReviewModeAutoGen(t *testing.T) {
	// Verify the error handling when an invalid mode is provided.
	t.Run("testVerifyReviewMode_InvalidMode", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			mode := "not gitlab"

			// run target function and assert
			got1 := VerifyReviewMode(mode)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the function returns nil for a valid mode.
	t.Run("testVerifyReviewMode_ValidMode", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			mode := "gitlab"

			// run target function and assert
			got1 := VerifyReviewMode(mode)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestVerifyReviewRulesAutoGen(t *testing.T) {
	// Verify the panic situation when the rule is null.
	t.Run("testVerifyReviewRules_NullRule", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var compileRet1MockPtrValue regexp2.Regexp
			compileRet1Mock := &compileRet1MockPtrValue
			var compileRet2Mock error
			mockey.Mock(regexp2.Compile).Return(compileRet1Mock, compileRet2Mock).Build()

			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var rule *db.OptimusConfigReviewRules
			convey.So(func() { _ = VerifyReviewRules(ctx, rule) }, convey.ShouldPanic)
		})
	})

	// Verify the error handling when regexp2.Compile returns an error.
	t.Run("testVerifyReviewRules_CompileError", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var compileRet1MockPtrValue regexp2.Regexp
			compileRet1Mock := &compileRet1MockPtrValue
			compileRet2Mock := fmt.Errorf("error")
			mockey.Mock(regexp2.Compile).Return(compileRet1Mock, compileRet2Mock).Build()

			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var rulePtrValue db.OptimusConfigReviewRules
			rule := &rulePtrValue

			// run target function and assert
			got1 := VerifyReviewRules(ctx, rule)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the successful execution of VerifyReviewRules function.
	t.Run("testVerifyReviewRules_Success", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var compileRet1MockPtrValue regexp2.Regexp
			compileRet1Mock := &compileRet1MockPtrValue
			var compileRet2Mock error
			mockey.Mock(regexp2.Compile).Return(compileRet1Mock, compileRet2Mock).Build()

			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			// prepare parameters
			var rulePtrValue db.OptimusConfigReviewRules
			rule := &rulePtrValue
			ctx := context.Background()

			// run target function and assert
			got1 := VerifyReviewRules(ctx, rule)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestVerifyCodeReviewAllConfigAutoGen(t *testing.T) {
	// Verify the error handling when the input config is nil.
	t.Run("testVerifyCodeReviewAllConfig_NilInput", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getReviewerResetModeRet1Mock string
			mockey.Mock((*config_service.ProjectCodeReviewConfig).GetReviewerResetMode, mockey.OptUnsafe).Return(getReviewerResetModeRet1Mock).Build()

			// prepare parameters
			var config *config_service.ProjectCodeReviewConfig

			// run target function and assert
			got1 := VerifyCodeReviewAllConfig(config)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the function returns nil when the input config is not nil.
	t.Run("testVerifyCodeReviewAllConfig_ValidInput", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getReviewerResetModeRet1Mock string
			mockey.Mock((*config_service.ProjectCodeReviewConfig).GetReviewerResetMode, mockey.OptUnsafe).Return(getReviewerResetModeRet1Mock).Build()

			// prepare parameters
			var configPtrValue config_service.ProjectCodeReviewConfig
			config := &configPtrValue

			// run target function and assert
			got1 := VerifyCodeReviewAllConfig(config)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the error handling when the ReviewerResetMode is invalid.
	t.Run("testVerifyCodeReviewAllConfig_InvalidReviewerResetMode", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var getReviewerResetModeRet1Mock string
			mockey.Mock((*config_service.ProjectCodeReviewConfig).GetReviewerResetMode, mockey.OptUnsafe).Return(getReviewerResetModeRet1Mock).Build()

			// prepare parameters
			var configPtrValue config_service.ProjectCodeReviewConfig
			config := &configPtrValue
			var configReviewerResetModePtrValue string
			config.ReviewerResetMode = &configReviewerResetModePtrValue

			// run target function and assert
			got1 := VerifyCodeReviewAllConfig(config)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

