package code_review

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bits/devops/cachyper"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/data"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/common_config"
)

func TestCodeReview_GetCodeReviewRulesFromCACAutoGen(t *testing.T) {
	// Verify the function behavior when getting code review rules from CAC with specific return values.
	t.Run("testCodeReview_GetCodeReviewRulesFromCAC", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var project int64
			var iid int64

			// run target function and assert
			got1, got2, got3, got4 := receiver.GetCodeReviewRulesFromCAC(ctx, project, iid)
			convey.So(len(got2), convey.ShouldEqual, 0)
			convey.So(got3, convey.ShouldBeBlank)
			convey.So(got4, convey.ShouldEqual, false)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestCodeReview_GetCodeReviewAllConfigByDetailIDAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByID()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			getCodeReviewDetailByIDRet2Mock := fmt.Errorf("error")
			mockey.Mock(data.GetCodeReviewDetailByID).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByIDRet2Mock).Build()

			var getConfigReviewRulesByDetailIDNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByDetailIDNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByDetailIDNew).Return(getConfigReviewRulesByDetailIDNewRet1Mock, getConfigReviewRulesByDetailIDNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var id int64
			var regionMatch bool
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByDetailID(ctx, id, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByID()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByID()_ret-1 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getConfigReviewRulesByDetailIDNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByDetailIDNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByDetailIDNew).Return(getConfigReviewRulesByDetailIDNewRet1Mock, getConfigReviewRulesByDetailIDNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			var optimusConfigCodeReviewDetailMock *db.OptimusConfigCodeReviewDetail
			var getCodeReviewDetailByIDRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByID).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByIDRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var id int64
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByDetailID(ctx, id, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByID()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByID()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetConfigReviewRulesByDetailIDNew()_ret-2 != nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			var getCodeReviewDetailByIDRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByID).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByIDRet2Mock).Build()

			var getConfigReviewRulesByDetailIDNewRet1Mock []*db.OptimusConfigReviewRules
			getConfigReviewRulesByDetailIDNewRet2Mock := fmt.Errorf("error")
			mockey.Mock(data.GetConfigReviewRulesByDetailIDNew).Return(getConfigReviewRulesByDetailIDNewRet1Mock, getConfigReviewRulesByDetailIDNewRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var id int64
			var regionMatch bool
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByDetailID(ctx, id, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByID()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByID()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetConfigReviewRulesByDetailIDNew()_ret-2 == nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			var getCodeReviewDetailByIDRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByID).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByIDRet2Mock).Build()

			var getConfigReviewRulesByDetailIDNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByDetailIDNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByDetailIDNew).Return(getConfigReviewRulesByDetailIDNewRet1Mock, getConfigReviewRulesByDetailIDNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			// prepare parameters
			var regionMatch bool
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var id int64

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByDetailID(ctx, id, regionMatch)
			convey.So(len((*got1).ReviewBizGroups), convey.ShouldEqual, 0)
			convey.So((*got1).ReviewerResetMode == nil, convey.ShouldBeTrue)
			convey.So((*got1).AllowStartReviewWip, convey.ShouldEqual, false)
			convey.So((*got1).RuleAcquisitionMode == nil, convey.ShouldBeTrue)
			convey.So((*got1).MinReviewersNumber == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).Maintainers), convey.ShouldEqual, 0)
			convey.So(len((*got1).ReviewRuleSetList), convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).NotResetBranch), convey.ShouldEqual, 0)
			convey.So((*got1).MinMaintainersNumber == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByID()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByID()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetConfigReviewRulesByDetailIDNew()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:AdaptCodeReviewDetailAndRulesDBToIDL()_ret-2 != nil
	t.Run("case_4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var getConfigReviewRulesByDetailIDNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByDetailIDNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByDetailIDNew).Return(getConfigReviewRulesByDetailIDNewRet1Mock, getConfigReviewRulesByDetailIDNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			adaptCodeReviewDetailAndRulesDBToIDLRet2Mock := fmt.Errorf("error")
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			var getCodeReviewDetailByIDRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByID).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByIDRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var id int64
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByDetailID(ctx, id, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestCodeReview_GetCodeReviewAllConfigByProjectIDAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getConfigReviewRulesByProjectIDLatestNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByProjectIDLatestNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByProjectIDLatestNew).Return(getConfigReviewRulesByProjectIDLatestNewRet1Mock, getConfigReviewRulesByProjectIDLatestNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			getCodeReviewDetailByProjectIDLatestRet2Mock := fmt.Errorf("error")
			mockey.Mock(data.GetCodeReviewDetailByProjectIDLatest).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByProjectIDLatestRet2Mock).Build()

			var mergeCodeReviewRulesRet1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules).Return(mergeCodeReviewRulesRet1Mock).Build()

			var codeReviewRulesRepositoryConfigMockPtrValue cachyper.CodeReviewRulesRepositoryConfig
			codeReviewRulesRepositoryConfigMock := &codeReviewRulesRepositoryConfigMockPtrValue
			var getCodeReviewRulesFromCACRet2Mock []*cachyper.CodeReviewRuleOwnersFile
			var getCodeReviewRulesFromCACRet3Mock string
			var getCodeReviewRulesFromCACRet4Mock bool
			mockey.Mock((*CodeReview).GetCodeReviewRulesFromCAC).Return(codeReviewRulesRepositoryConfigMock, getCodeReviewRulesFromCACRet2Mock, getCodeReviewRulesFromCACRet3Mock, getCodeReviewRulesFromCACRet4Mock).Build()

			var mergeCodeReviewRules2Ret1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules2).Return(mergeCodeReviewRules2Ret1Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var projectID string
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByProjectID(ctx, projectID, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-1 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var mergeCodeReviewRules2Ret1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules2).Return(mergeCodeReviewRules2Ret1Mock).Build()

			var getConfigReviewRulesByProjectIDLatestNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByProjectIDLatestNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByProjectIDLatestNew).Return(getConfigReviewRulesByProjectIDLatestNewRet1Mock, getConfigReviewRulesByProjectIDLatestNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			var optimusConfigCodeReviewDetailMock *db.OptimusConfigCodeReviewDetail
			var getCodeReviewDetailByProjectIDLatestRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByProjectIDLatest).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByProjectIDLatestRet2Mock).Build()

			var mergeCodeReviewRulesRet1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules).Return(mergeCodeReviewRulesRet1Mock).Build()

			var codeReviewRulesRepositoryConfigMockPtrValue cachyper.CodeReviewRulesRepositoryConfig
			codeReviewRulesRepositoryConfigMock := &codeReviewRulesRepositoryConfigMockPtrValue
			var getCodeReviewRulesFromCACRet2Mock []*cachyper.CodeReviewRuleOwnersFile
			var getCodeReviewRulesFromCACRet3Mock string
			var getCodeReviewRulesFromCACRet4Mock bool
			mockey.Mock((*CodeReview).GetCodeReviewRulesFromCAC).Return(codeReviewRulesRepositoryConfigMock, getCodeReviewRulesFromCACRet2Mock, getCodeReviewRulesFromCACRet3Mock, getCodeReviewRulesFromCACRet4Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var projectID string
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByProjectID(ctx, projectID, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-1.RuleAcquisitionMode == "platform"
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			optimusConfigCodeReviewDetailMock.RuleAcquisitionMode = "platform"
			var getCodeReviewDetailByProjectIDLatestRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByProjectIDLatest).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByProjectIDLatestRet2Mock).Build()

			var mergeCodeReviewRulesRet1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules).Return(mergeCodeReviewRulesRet1Mock).Build()

			var codeReviewRulesRepositoryConfigMockPtrValue cachyper.CodeReviewRulesRepositoryConfig
			codeReviewRulesRepositoryConfigMock := &codeReviewRulesRepositoryConfigMockPtrValue
			var getCodeReviewRulesFromCACRet2Mock []*cachyper.CodeReviewRuleOwnersFile
			var getCodeReviewRulesFromCACRet3Mock string
			var getCodeReviewRulesFromCACRet4Mock bool
			mockey.Mock((*CodeReview).GetCodeReviewRulesFromCAC).Return(codeReviewRulesRepositoryConfigMock, getCodeReviewRulesFromCACRet2Mock, getCodeReviewRulesFromCACRet3Mock, getCodeReviewRulesFromCACRet4Mock).Build()

			var mergeCodeReviewRules2Ret1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules2).Return(mergeCodeReviewRules2Ret1Mock).Build()

			var getConfigReviewRulesByProjectIDLatestNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByProjectIDLatestNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByProjectIDLatestNew).Return(getConfigReviewRulesByProjectIDLatestNewRet1Mock, getConfigReviewRulesByProjectIDLatestNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var projectID string
			var regionMatch bool
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByProjectID(ctx, projectID, regionMatch)
			convey.So((*got1).MinMaintainersNumber == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).ReviewBizGroups), convey.ShouldEqual, 0)
			convey.So((*got1).ReviewerResetMode == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).NotResetBranch), convey.ShouldEqual, 0)
			convey.So((*got1).AllowStartReviewWip, convey.ShouldEqual, false)
			convey.So((*got1).ReviewMode == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).Maintainers), convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So((*got1).MinReviewersNumber == nil, convey.ShouldBeTrue)
			convey.So((*got1).RuleAcquisitionMode == nil, convey.ShouldBeTrue)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetConfigReviewRulesByProjectIDLatestNew()_ret-2 != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var mergeCodeReviewRulesRet1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules).Return(mergeCodeReviewRulesRet1Mock).Build()

			var codeReviewRulesRepositoryConfigMockPtrValue cachyper.CodeReviewRulesRepositoryConfig
			codeReviewRulesRepositoryConfigMock := &codeReviewRulesRepositoryConfigMockPtrValue
			var getCodeReviewRulesFromCACRet2Mock []*cachyper.CodeReviewRuleOwnersFile
			var getCodeReviewRulesFromCACRet3Mock string
			var getCodeReviewRulesFromCACRet4Mock bool
			mockey.Mock((*CodeReview).GetCodeReviewRulesFromCAC).Return(codeReviewRulesRepositoryConfigMock, getCodeReviewRulesFromCACRet2Mock, getCodeReviewRulesFromCACRet3Mock, getCodeReviewRulesFromCACRet4Mock).Build()

			var mergeCodeReviewRules2Ret1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules2).Return(mergeCodeReviewRules2Ret1Mock).Build()

			var getConfigReviewRulesByProjectIDLatestNewRet1Mock []*db.OptimusConfigReviewRules
			getConfigReviewRulesByProjectIDLatestNewRet2Mock := fmt.Errorf("error")
			mockey.Mock(data.GetConfigReviewRulesByProjectIDLatestNew).Return(getConfigReviewRulesByProjectIDLatestNewRet1Mock, getConfigReviewRulesByProjectIDLatestNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			var getCodeReviewDetailByProjectIDLatestRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByProjectIDLatest).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByProjectIDLatestRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var projectID string
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByProjectID(ctx, projectID, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-1.RuleAcquisitionMode != "platform"
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-1.RuleAcquisitionMode == "cac"
	t.Run("case_4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var mergeCodeReviewRulesRet1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules).Return(mergeCodeReviewRulesRet1Mock).Build()

			var codeReviewRulesRepositoryConfigMockPtrValue cachyper.CodeReviewRulesRepositoryConfig
			codeReviewRulesRepositoryConfigMock := &codeReviewRulesRepositoryConfigMockPtrValue
			var getCodeReviewRulesFromCACRet2Mock []*cachyper.CodeReviewRuleOwnersFile
			var getCodeReviewRulesFromCACRet3Mock string
			var getCodeReviewRulesFromCACRet4Mock bool
			mockey.Mock((*CodeReview).GetCodeReviewRulesFromCAC).Return(codeReviewRulesRepositoryConfigMock, getCodeReviewRulesFromCACRet2Mock, getCodeReviewRulesFromCACRet3Mock, getCodeReviewRulesFromCACRet4Mock).Build()

			var mergeCodeReviewRules2Ret1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules2).Return(mergeCodeReviewRules2Ret1Mock).Build()

			var getConfigReviewRulesByProjectIDLatestNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByProjectIDLatestNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByProjectIDLatestNew).Return(getConfigReviewRulesByProjectIDLatestNewRet1Mock, getConfigReviewRulesByProjectIDLatestNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			optimusConfigCodeReviewDetailMock.RuleAcquisitionMode = "cac"
			var getCodeReviewDetailByProjectIDLatestRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByProjectIDLatest).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByProjectIDLatestRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var projectID string
			var regionMatch bool
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByProjectID(ctx, projectID, regionMatch)
			convey.So((*got1).MinReviewersNumber == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).ReviewBizGroups), convey.ShouldEqual, 0)
			convey.So(len((*got1).NotResetBranch), convey.ShouldEqual, 0)
			convey.So((*got1).AllowStartReviewWip, convey.ShouldEqual, false)
			convey.So((*got1).MinMaintainersNumber == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).ReviewerResetMode == nil, convey.ShouldBeTrue)
			convey.So((*got1).RuleAcquisitionMode == nil, convey.ShouldBeTrue)
			convey.So((*got1).ReviewMode == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).Maintainers), convey.ShouldEqual, 0)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetConfigReviewRulesByProjectIDLatestNew()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:GetCodeReviewRulesFromCAC()_ret-4 == true
	t.Run("case_5", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// mock function returns or global values
			var codeReviewRulesRepositoryConfigMockPtrValue cachyper.CodeReviewRulesRepositoryConfig
			codeReviewRulesRepositoryConfigMock := &codeReviewRulesRepositoryConfigMockPtrValue
			var getCodeReviewRulesFromCACRet2Mock []*cachyper.CodeReviewRuleOwnersFile
			var getCodeReviewRulesFromCACRet3Mock string
			getCodeReviewRulesFromCACRet4Mock := true
			mockey.Mock((*CodeReview).GetCodeReviewRulesFromCAC).Return(codeReviewRulesRepositoryConfigMock, getCodeReviewRulesFromCACRet2Mock, getCodeReviewRulesFromCACRet3Mock, getCodeReviewRulesFromCACRet4Mock).Build()

			var mergeCodeReviewRules2Ret1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules2).Return(mergeCodeReviewRules2Ret1Mock).Build()

			var getConfigReviewRulesByProjectIDLatestNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByProjectIDLatestNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByProjectIDLatestNew).Return(getConfigReviewRulesByProjectIDLatestNewRet1Mock, getConfigReviewRulesByProjectIDLatestNewRet2Mock).Build()

			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			var adaptCodeReviewDetailAndRulesDBToIDLRet2Mock error
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			var getCodeReviewDetailByProjectIDLatestRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByProjectIDLatest).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByProjectIDLatestRet2Mock).Build()

			var mergeCodeReviewRulesRet1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules).Return(mergeCodeReviewRulesRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var projectID string
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByProjectID(ctx, projectID, regionMatch)
			convey.So(len((*got1).ReviewBizGroups), convey.ShouldEqual, 0)
			convey.So(len((*got1).Maintainers), convey.ShouldEqual, 0)
			convey.So((*got1).ReviewerResetMode == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).NotResetBranch), convey.ShouldEqual, 0)
			convey.So((*got1).AllowStartReviewWip, convey.ShouldEqual, false)
			convey.So((*got1).RuleAcquisitionMode == nil, convey.ShouldBeTrue)
			convey.So((*got1).ReviewMode == nil, convey.ShouldBeTrue)
			convey.So((*got1).MinMaintainersNumber == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).MinReviewersNumber == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetCodeReviewDetailByProjectIDLatest()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetConfigReviewRulesByProjectIDLatestNew()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/code_review.CodeReview:AdaptCodeReviewDetailAndRulesDBToIDL()_ret-2 != nil
	t.Run("case_6", func(t *testing.T) {
		mockey.PatchConvey("case_6", t, func() {
			// mock function returns or global values
			var projectCodeReviewConfigMockPtrValue config_service.ProjectCodeReviewConfig
			projectCodeReviewConfigMock := &projectCodeReviewConfigMockPtrValue
			adaptCodeReviewDetailAndRulesDBToIDLRet2Mock := fmt.Errorf("error")
			mockey.Mock((*CodeReview).AdaptCodeReviewDetailAndRulesDBToIDL).Return(projectCodeReviewConfigMock, adaptCodeReviewDetailAndRulesDBToIDLRet2Mock).Build()

			var optimusConfigCodeReviewDetailMockPtrValue db.OptimusConfigCodeReviewDetail
			optimusConfigCodeReviewDetailMock := &optimusConfigCodeReviewDetailMockPtrValue
			var getCodeReviewDetailByProjectIDLatestRet2Mock error
			mockey.Mock(data.GetCodeReviewDetailByProjectIDLatest).Return(optimusConfigCodeReviewDetailMock, getCodeReviewDetailByProjectIDLatestRet2Mock).Build()

			var mergeCodeReviewRulesRet1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules).Return(mergeCodeReviewRulesRet1Mock).Build()

			var codeReviewRulesRepositoryConfigMockPtrValue cachyper.CodeReviewRulesRepositoryConfig
			codeReviewRulesRepositoryConfigMock := &codeReviewRulesRepositoryConfigMockPtrValue
			var getCodeReviewRulesFromCACRet2Mock []*cachyper.CodeReviewRuleOwnersFile
			var getCodeReviewRulesFromCACRet3Mock string
			var getCodeReviewRulesFromCACRet4Mock bool
			mockey.Mock((*CodeReview).GetCodeReviewRulesFromCAC).Return(codeReviewRulesRepositoryConfigMock, getCodeReviewRulesFromCACRet2Mock, getCodeReviewRulesFromCACRet3Mock, getCodeReviewRulesFromCACRet4Mock).Build()

			var mergeCodeReviewRules2Ret1Mock []*db.OptimusConfigReviewRules
			mockey.Mock(MergeCodeReviewRules2).Return(mergeCodeReviewRules2Ret1Mock).Build()

			var getConfigReviewRulesByProjectIDLatestNewRet1Mock []*db.OptimusConfigReviewRules
			var getConfigReviewRulesByProjectIDLatestNewRet2Mock error
			mockey.Mock(data.GetConfigReviewRulesByProjectIDLatestNew).Return(getConfigReviewRulesByProjectIDLatestNewRet1Mock, getConfigReviewRulesByProjectIDLatestNewRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue CodeReview
			receiver := &receiverPtrValue
			ctx := context.Background()
			var projectID string
			var regionMatch bool

			// run target function and assert
			got1, got2 := receiver.GetCodeReviewAllConfigByProjectID(ctx, projectID, regionMatch)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

}

func TestGetCodeReviewGroupReviewConfigAutoGen(t *testing.T) {
	// Verify the behavior when getting common config fails.
	t.Run("testGetCodeReviewGroupReviewConfig_CommonConfigFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var versionDependencyReviewPatternMock config_service.VersionDependencyReviewPattern
			var versionDependencyReviewPatternFromStringRet2Mock error
			mockey.Mock(config_service.VersionDependencyReviewPatternFromString).Return(versionDependencyReviewPatternMock, versionDependencyReviewPatternFromStringRet2Mock).Build()

			var optimusConfigGroupCommonMockPtrValue db.OptimusConfigGroupCommon
			optimusConfigGroupCommonMock := &optimusConfigGroupCommonMockPtrValue
			getGroupCommonConfigRet2Mock := fmt.Errorf("error")
			mockey.Mock((*common_config.CommonConfig).GetGroupCommonConfig).Return(optimusConfigGroupCommonMock, getGroupCommonConfigRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var groupName string

			// run target function and assert
			got1, got2 := GetCodeReviewGroupReviewConfig(ctx, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the scenario when the config is not empty.
	t.Run("testGetCodeReviewGroupReviewConfig_ConfigNotEmpty", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var versionDependencyReviewPatternMock config_service.VersionDependencyReviewPattern
			var versionDependencyReviewPatternFromStringRet2Mock error
			mockey.Mock(config_service.VersionDependencyReviewPatternFromString).Return(versionDependencyReviewPatternMock, versionDependencyReviewPatternFromStringRet2Mock).Build()

			var optimusConfigGroupCommonMockPtrValue db.OptimusConfigGroupCommon
			optimusConfigGroupCommonMock := &optimusConfigGroupCommonMockPtrValue
			optimusConfigGroupCommonMock.Config = "a"
			var getGroupCommonConfigRet2Mock error
			mockey.Mock((*common_config.CommonConfig).GetGroupCommonConfig).Return(optimusConfigGroupCommonMock, getGroupCommonConfigRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var groupName string

			// run target function and assert
			got1, got2 := GetCodeReviewGroupReviewConfig(ctx, groupName)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).VersionDependencyReviewPattern == nil, convey.ShouldBeTrue)
			convey.So((*got1).VersionDependencyMinReviewersNumber == nil, convey.ShouldBeTrue)
			convey.So((*got1).VersionDependencyReviewEnable, convey.ShouldEqual, false)
		})
	})

	// Verify the behavior when unmarshalling fails.
	t.Run("testGetCodeReviewGroupReviewConfig_UnmarshalFailure", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			unmarshalStringRet1Mock := fmt.Errorf("error")
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var versionDependencyReviewPatternMock config_service.VersionDependencyReviewPattern
			var versionDependencyReviewPatternFromStringRet2Mock error
			mockey.Mock(config_service.VersionDependencyReviewPatternFromString).Return(versionDependencyReviewPatternMock, versionDependencyReviewPatternFromStringRet2Mock).Build()

			var optimusConfigGroupCommonMockPtrValue db.OptimusConfigGroupCommon
			optimusConfigGroupCommonMock := &optimusConfigGroupCommonMockPtrValue
			optimusConfigGroupCommonMock.Config = "a"
			var getGroupCommonConfigRet2Mock error
			mockey.Mock((*common_config.CommonConfig).GetGroupCommonConfig).Return(optimusConfigGroupCommonMock, getGroupCommonConfigRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var groupName string

			// run target function and assert
			got1, got2 := GetCodeReviewGroupReviewConfig(ctx, groupName)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).VersionDependencyReviewPattern == nil, convey.ShouldBeTrue)
			convey.So((*got1).VersionDependencyMinReviewersNumber == nil, convey.ShouldBeTrue)
			convey.So((*got1).VersionDependencyReviewEnable, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the default behavior of the function.
	t.Run("testGetCodeReviewGroupReviewConfig_Default", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var versionDependencyReviewPatternMock config_service.VersionDependencyReviewPattern
			var versionDependencyReviewPatternFromStringRet2Mock error
			mockey.Mock(config_service.VersionDependencyReviewPatternFromString).Return(versionDependencyReviewPatternMock, versionDependencyReviewPatternFromStringRet2Mock).Build()

			var optimusConfigGroupCommonMockPtrValue db.OptimusConfigGroupCommon
			optimusConfigGroupCommonMock := &optimusConfigGroupCommonMockPtrValue
			var getGroupCommonConfigRet2Mock error
			mockey.Mock((*common_config.CommonConfig).GetGroupCommonConfig).Return(optimusConfigGroupCommonMock, getGroupCommonConfigRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var groupName string

			// run target function and assert
			got1, got2 := GetCodeReviewGroupReviewConfig(ctx, groupName)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).VersionDependencyReviewPattern == nil, convey.ShouldBeTrue)
			convey.So((*got1).VersionDependencyMinReviewersNumber == nil, convey.ShouldBeTrue)
			convey.So((*got1).VersionDependencyReviewEnable, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

}

