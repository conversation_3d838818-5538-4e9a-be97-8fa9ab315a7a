package app_base_config

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
)

func Test_newDefaultAppBaseConfigAutoGen(t *testing.T) {
	// param1 == 2
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			// prepare parameters
			techTypeAlias := int64(2)
			techType := config_service.MetaTechnologyStack(techTypeAlias)

			// run target function and assert
			got1 := newDefaultAppBaseConfig(techType)
			convey.So(got1.FlutterProjectType, convey.ShouldEqual, 0)
			convey.So(got1.UiAutoTestId, convey.ShouldEqual, 0)
			convey.So(got1.BizReleasePattern, convey.ShouldEqual, "release/<lv>")
			convey.So(got1.HarmonyRelease, convey.ShouldBeBlank)
			convey.So(got1.AppId, convey.ShouldEqual, 0)
			convey.So(got1.ReleasePattern, convey.ShouldEqual, "release/<lv>")
			convey.So(got1.MonkeyAutoTestId, convey.ShouldEqual, 0)
			convey.So(got1.AndroidRelease, convey.ShouldEqual, "http://maven.byted.org/repository/bytedance_android/")
			convey.So(got1.IosBinary, convey.ShouldBeBlank)
		})
	})

	// param1 != 2
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			// prepare parameters
			techTypeAlias := int64(3)
			techType := config_service.MetaTechnologyStack(techTypeAlias)

			// run target function and assert
			got1 := newDefaultAppBaseConfig(techType)
			convey.So(got1.FlutterProjectType, convey.ShouldEqual, 0)
			convey.So(got1.ReleasePattern, convey.ShouldEqual, "release/<lv>")
			convey.So(got1.IosBinary, convey.ShouldBeBlank)
			convey.So(got1.HarmonyRelease, convey.ShouldBeBlank)
			convey.So(got1.AppId, convey.ShouldEqual, 0)
			convey.So(got1.MonkeyAutoTestId, convey.ShouldEqual, 0)
			convey.So(got1.UiAutoTestId, convey.ShouldEqual, 0)
			convey.So(got1.BizReleasePattern, convey.ShouldEqual, "release/<lv>")
			convey.So(got1.AndroidRelease, convey.ShouldBeBlank)
		})
	})

}

func Test_updateAppBaseConfigAutoGen(t *testing.T) {
	// param1 != nil
	// param3.IsApp != nil
	// param3 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsIsAppPtrValue bool
			options.IsApp = &optionsIsAppPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param3.UiAutoTestID != nil
	// param3 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsUiAutoTestIDPtrValue int64
			options.UiAutoTestID = &optionsUiAutoTestIDPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param3.MonkeyAutoTestID != nil
	// param3 != nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsMonkeyAutoTestIDPtrValue int64
			options.MonkeyAutoTestID = &optionsMonkeyAutoTestIDPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param3.AutoTestAppID != nil
	// param3 != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsAutoTestAppIDPtrValue int64
			options.AutoTestAppID = &optionsAutoTestAppIDPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param3.DevelopPattern != nil
	// param3 != nil
	t.Run("case_4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsDevelopPatternPtrValue string
			options.DevelopPattern = &optionsDevelopPatternPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param3.ReleasePattern != nil
	// param3 != nil
	t.Run("case_5", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsReleasePatternPtrValue string
			options.ReleasePattern = &optionsReleasePatternPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param3.MasterPattern != nil
	// param3 != nil
	t.Run("case_6", func(t *testing.T) {
		mockey.PatchConvey("case_6", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsMasterPatternPtrValue string
			options.MasterPattern = &optionsMasterPatternPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param3.BizDevelopPattern != nil
	// param3 != nil
	t.Run("case_7", func(t *testing.T) {
		mockey.PatchConvey("case_7", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsBizDevelopPatternPtrValue string
			options.BizDevelopPattern = &optionsBizDevelopPatternPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param3.BizReleasePattern != nil
	// param3 != nil
	t.Run("case_8", func(t *testing.T) {
		mockey.PatchConvey("case_8", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsBizReleasePatternPtrValue string
			options.BizReleasePattern = &optionsBizReleasePatternPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param3.BizMasterPattern != nil
	// param3 != nil
	t.Run("case_9", func(t *testing.T) {
		mockey.PatchConvey("case_9", t, func() {
			// mock function returns or global values
			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var techType config_service.MetaTechnologyStack
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsBizMasterPatternPtrValue string
			options.BizMasterPattern = &optionsBizMasterPatternPtrValue
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	// param2 == 1
	// param3.IosBinary != nil
	// param3 != nil
	t.Run("case_10", func(t *testing.T) {
		mockey.PatchConvey("case_10", t, func() {
			// mock function returns or global values
			var getIsAppRet1Mock bool
			mockey.Mock((*config_service.UpdateDevopsAppBaseConfigOptions).GetIsApp, mockey.OptUnsafe).Return(getIsAppRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var convertBool2Int8Ret1Mock int8
			mockey.Mock(utils.ConvertBool2Int8).Return(convertBool2Int8Ret1Mock).Build()

			// prepare parameters
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			techTypeAlias := int64(1)
			techType := config_service.MetaTechnologyStack(techTypeAlias)
			var optionsPtrValue config_service.UpdateDevopsAppBaseConfigOptions
			options := &optionsPtrValue
			var optionsIosBinaryPtrValue string
			options.IosBinary = &optionsIosBinaryPtrValue
			convey.So(func() { updateAppBaseConfig(config, techType, options) }, convey.ShouldNotPanic)
		})
	})

}

