package app_base_config

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/utils"
	utils2 "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
)

func TestAdaptAppBaseConfigToViewAutoGen(t *testing.T) {
	// Verify the function behavior when IsFlutterProjectType returns true.
	t.Run("testAdaptAppBaseConfigToView_FlutterProjectTypeTrue", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var convertInt82BoolRet1Mock bool
			mockey.Mock(utils2.ConvertInt82Bool).Return(convertInt82BoolRet1Mock).Build()

			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			isFlutterProjectTypeRet1Mock := true
			mockey.Mock(utils.IsFlutterProjectType).Return(isFlutterProjectTypeRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue

			// run target function and assert
			got1, got2 := AdaptAppBaseConfigToView(ctx, config)
			convey.So((*got1).IsApp, convey.ShouldEqual, false)
			convey.So((*got1).UiAutoTestID, convey.ShouldEqual, 0)
			convey.So((*got1).IosRootDirectory, convey.ShouldBeBlank)
			convey.So((*got1).BizReleasePattern, convey.ShouldBeBlank)
			convey.So((*got1).IosSource, convey.ShouldBeBlank)
			convey.So((*got1).DevelopPattern, convey.ShouldBeBlank)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).IosBundleID == nil, convey.ShouldBeFalse)
			convey.So((*got1).AndroidRelease, convey.ShouldBeBlank)
			convey.So((*got1).MonkeyAutoTestID, convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when AndroidRelatedId is greater than zero.
	t.Run("testAdaptAppBaseConfigToView_AndroidRelatedIdGreaterThanZero", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var isFlutterProjectTypeRet1Mock bool
			mockey.Mock(utils.IsFlutterProjectType).Return(isFlutterProjectTypeRet1Mock).Build()

			var convertInt82BoolRet1Mock bool
			mockey.Mock(utils2.ConvertInt82Bool).Return(convertInt82BoolRet1Mock).Build()

			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			config.AndroidRelatedId = int64(1)

			// run target function and assert
			got1, got2 := AdaptAppBaseConfigToView(ctx, config)
			convey.So((*got1).IosSource, convey.ShouldBeBlank)
			convey.So((*got1).MonkeyAutoTestID, convey.ShouldEqual, 0)
			convey.So((*got1).IsApp, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So((*got1).UiAutoTestID, convey.ShouldEqual, 0)
			convey.So((*got1).AndroidRelease, convey.ShouldBeBlank)
			convey.So((*got1).IosRootDirectory, convey.ShouldBeBlank)
			convey.So((*got1).BizReleasePattern, convey.ShouldBeBlank)
			convey.So((*got1).DevelopPattern, convey.ShouldBeBlank)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).IosBundleID == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the function behavior when IosRelatedId is greater than zero.
	t.Run("testAdaptAppBaseConfigToView_IosRelatedIdGreaterThanZero", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var unmarshalRet1Mock error
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			var isFlutterProjectTypeRet1Mock bool
			mockey.Mock(utils.IsFlutterProjectType).Return(isFlutterProjectTypeRet1Mock).Build()

			var convertInt82BoolRet1Mock bool
			mockey.Mock(utils2.ConvertInt82Bool).Return(convertInt82BoolRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			config.IosRelatedId = int64(1)

			// run target function and assert
			got1, got2 := AdaptAppBaseConfigToView(ctx, config)
			convey.So((*got1).UiAutoTestID, convey.ShouldEqual, 0)
			convey.So((*got1).BizReleasePattern, convey.ShouldBeBlank)
			convey.So((*got1).DevelopPattern, convey.ShouldBeBlank)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So((*got1).IsApp, convey.ShouldEqual, false)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).IosBundleID == nil, convey.ShouldBeFalse)
			convey.So((*got1).AndroidRelease, convey.ShouldBeBlank)
			convey.So((*got1).IosRootDirectory, convey.ShouldBeBlank)
			convey.So((*got1).IosSource, convey.ShouldBeBlank)
			convey.So((*got1).MonkeyAutoTestID, convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when DirectoryOwnerConfig is not empty and Unmarshal returns an error.
	t.Run("testAdaptAppBaseConfigToView_DirectoryOwnerConfigNotEmpty", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var isFlutterProjectTypeRet1Mock bool
			mockey.Mock(utils.IsFlutterProjectType).Return(isFlutterProjectTypeRet1Mock).Build()

			var convertInt82BoolRet1Mock bool
			mockey.Mock(utils2.ConvertInt82Bool).Return(convertInt82BoolRet1Mock).Build()

			unmarshalRet1Mock := fmt.Errorf("error")
			mockey.Mock(sonic.Unmarshal, mockey.OptUnsafe).Return(unmarshalRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var configPtrValue db.AppBaseConfig
			config := &configPtrValue
			config.DirectoryOwnerConfig = "not "

			// run target function and assert
			got1, got2 := AdaptAppBaseConfigToView(ctx, config)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

