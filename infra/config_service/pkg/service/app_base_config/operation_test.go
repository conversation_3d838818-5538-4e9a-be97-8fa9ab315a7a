package app_base_config

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/operation_record"
	"code.byted.org/devinfra/hagrid/infra/config_service/service/tcc"
)

func TestAppBaseUpdater_StoreAppBaseConfigOperationAutoGen(t *testing.T) {
	// Verify the behavior when tcc.VerifyEnable returns false.
	t.Run("testAppBaseUpdater_StoreAppBaseConfigOperation_VerifyEnableFalse", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			mockey.Mock(operation_record.ValueDifferenceList.LogInfo, mockey.OptUnsafe).Return().Build()

			mockey.Mock(operation_record.StoreGroupOperations).Return().Build()

			var toInterfaceListRet1Mock []interface{}
			mockey.Mock(operation_record.ValueDifferenceList.ToInterfaceList).Return(toInterfaceListRet1Mock).Build()

			verifyEnableRet1Mock := false
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			var valueDifferenceListMock operation_record.ValueDifferenceList
			mockey.Mock(operation_record.GetValueDifferenceNew).Return(valueDifferenceListMock).Build()

			// prepare parameters
			ctx := context.Background()
			var userName *string
			var receiverPtrValue AppBaseUpdater
			receiver := &receiverPtrValue
			convey.So(func() { receiver.StoreAppBaseConfigOperation(ctx, userName) }, convey.ShouldNotPanic)
		})
	})

	// Verify the behavior when tcc.VerifyEnable returns true.
	t.Run("testAppBaseUpdater_StoreAppBaseConfigOperation_VerifyEnableTrue", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var toInterfaceListRet1Mock []interface{}
			mockey.Mock(operation_record.ValueDifferenceList.ToInterfaceList).Return(toInterfaceListRet1Mock).Build()

			verifyEnableRet1Mock := true
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			var valueDifferenceListMock operation_record.ValueDifferenceList
			mockey.Mock(operation_record.GetValueDifferenceNew).Return(valueDifferenceListMock).Build()

			mockey.Mock(operation_record.ValueDifferenceList.LogInfo, mockey.OptUnsafe).Return().Build()

			mockey.Mock(operation_record.StoreGroupOperations).Return().Build()

			// prepare parameters
			var receiverPtrValue AppBaseUpdater
			receiver := &receiverPtrValue
			ctx := context.Background()
			var userName *string
			convey.So(func() { receiver.StoreAppBaseConfigOperation(ctx, userName) }, convey.ShouldNotPanic)
		})
	})

	// Verify the behavior when operation_record.GetValueDifferenceNew returns a non-empty list and tcc.VerifyEnable returns true.
	t.Run("testAppBaseUpdater_StoreAppBaseConfigOperation_ValueDifferenceNotEmpty", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			mockey.Mock(operation_record.ValueDifferenceList.LogInfo, mockey.OptUnsafe).Return().Build()

			mockey.Mock(operation_record.StoreGroupOperations).Return().Build()

			var toInterfaceListRet1Mock []interface{}
			mockey.Mock(operation_record.ValueDifferenceList.ToInterfaceList).Return(toInterfaceListRet1Mock).Build()

			verifyEnableRet1Mock := true
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			var valueDifferenceListMockItem0PtrValue operation_record.ValueDifferenceItem
			valueDifferenceListMockItem0 := &valueDifferenceListMockItem0PtrValue
			valueDifferenceListMock := []*operation_record.ValueDifferenceItem{valueDifferenceListMockItem0,}
			mockey.Mock(operation_record.GetValueDifferenceNew).Return(valueDifferenceListMock).Build()

			// prepare parameters
			var receiverPtrValue AppBaseUpdater
			receiver := &receiverPtrValue
			ctx := context.Background()
			var userName *string
			convey.So(func() { receiver.StoreAppBaseConfigOperation(ctx, userName) }, convey.ShouldNotPanic)
		})
	})

	// Verify the behavior when operation_record.GetValueDifferenceNew returns a non-empty list, tcc.VerifyEnable returns true and userName is not null.
	t.Run("testAppBaseUpdater_StoreAppBaseConfigOperation_ValueDifferenceNotEmptyAndUserNameNotNull", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var valueDifferenceListMockItem0PtrValue operation_record.ValueDifferenceItem
			valueDifferenceListMockItem0 := &valueDifferenceListMockItem0PtrValue
			valueDifferenceListMock := []*operation_record.ValueDifferenceItem{valueDifferenceListMockItem0,}
			mockey.Mock(operation_record.GetValueDifferenceNew).Return(valueDifferenceListMock).Build()

			mockey.Mock(operation_record.ValueDifferenceList.LogInfo, mockey.OptUnsafe).Return().Build()

			mockey.Mock(operation_record.StoreGroupOperations).Return().Build()

			var toInterfaceListRet1Mock []interface{}
			mockey.Mock(operation_record.ValueDifferenceList.ToInterfaceList).Return(toInterfaceListRet1Mock).Build()

			verifyEnableRet1Mock := true
			mockey.Mock(tcc.VerifyEnable).Return(verifyEnableRet1Mock).Build()

			// prepare parameters
			var userNamePtrValue string
			userName := &userNamePtrValue
			var receiverPtrValue AppBaseUpdater
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { receiver.StoreAppBaseConfigOperation(ctx, userName) }, convey.ShouldNotPanic)
		})
	})

}

