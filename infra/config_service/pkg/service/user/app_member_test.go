package user

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/gopkg/facility/set"
)

func TestGetMembersFromAppInfoAutoGen(t *testing.T) {
	// Verify the function behavior when the input appInfo is nil.
	t.Run("testGetMembersFromAppInfo_NullInput", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var appInfo *config_service.MetaAppInfo

			// run target function and assert
			got1 := GetMembersFromAppInfo(appInfo)
			convey.So(len(got1.MaterialOwner), convey.ShouldEqual, 0)
			convey.So(len(got1.ReviewOwner), convey.ShouldEqual, 0)
			convey.So(len(got1.BM), convey.ShouldEqual, 0)
			convey.So(len(got1.QA), convey.ShouldEqual, 0)
			convey.So(len(got1.CheckOwner), convey.ShouldEqual, 0)
			convey.So(len(got1.SubmitOwner), convey.ShouldEqual, 0)
			convey.So(len(got1.PM), convey.ShouldEqual, 0)
			convey.So(len(got1.Managers), convey.ShouldEqual, 0)
			convey.So(len(got1.RD), convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when the input appInfo is not nil.
	t.Run("testGetMembersFromAppInfo_NonNullInput", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			var appInfoPtrValue config_service.MetaAppInfo
			appInfo := &appInfoPtrValue

			// run target function and assert
			got1 := GetMembersFromAppInfo(appInfo)
			convey.So(len(got1.Managers), convey.ShouldEqual, 0)
			convey.So(len(got1.QA), convey.ShouldEqual, 0)
			convey.So(len(got1.BM), convey.ShouldEqual, 0)
			convey.So(len(got1.MaterialOwner), convey.ShouldEqual, 0)
			convey.So(len(got1.PM), convey.ShouldEqual, 0)
			convey.So(len(got1.RD), convey.ShouldEqual, 0)
			convey.So(len(got1.CheckOwner), convey.ShouldEqual, 0)
			convey.So(len(got1.ReviewOwner), convey.ShouldEqual, 0)
			convey.So(len(got1.SubmitOwner), convey.ShouldEqual, 0)
		})
	})

}

func TestGetLoseUserNamesBetweenOldAndNewAppInfoAutoGen(t *testing.T) {
	// Verify the function behavior under default conditions.
	t.Run("testGetLoseUserNamesBetweenOldAndNewAppInfo_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stringSetMock set.StringSet
			mockey.Mock(set.NewStringSet).Return(stringSetMock).Build()

			// prepare parameters
			var oldAppInfo config_service.MetaAppInfo
			var newAppInfo config_service.MetaAppInfo

			// run target function and assert
			got1, got2, got3, got4, got5 := GetLoseUserNamesBetweenOldAndNewAppInfo(oldAppInfo, newAppInfo)
			convey.So(len(got1), convey.ShouldEqual, 0)
			convey.So(len(got2), convey.ShouldEqual, 0)
			convey.So(len(got3), convey.ShouldEqual, 0)
			convey.So(len(got4), convey.ShouldEqual, 0)
			convey.So(len(got5), convey.ShouldEqual, 0)
		})
	})

}

func TestGetNewUserNamesBetweenOldAndNewAppInfoAutoGen(t *testing.T) {
	// Verify the function behavior under default conditions.
	t.Run("testGetNewUserNamesBetweenOldAndNewAppInfo", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stringSetMock set.StringSet
			mockey.Mock(set.NewStringSet).Return(stringSetMock).Build()

			// prepare parameters
			var oldAppInfo config_service.MetaAppInfo
			var newAppInfo config_service.MetaAppInfo

			// run target function and assert
			got1, got2, got3, got4, got5 := GetNewUserNamesBetweenOldAndNewAppInfo(oldAppInfo, newAppInfo)
			convey.So(len(got3), convey.ShouldEqual, 0)
			convey.So(len(got4), convey.ShouldEqual, 0)
			convey.So(len(got5), convey.ShouldEqual, 0)
			convey.So(len(got1), convey.ShouldEqual, 0)
			convey.So(len(got2), convey.ShouldEqual, 0)
		})
	})

}

