package user

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"
)

func TestAdaptToAppAdminsAutoGen(t *testing.T) {
	// len(param2.Managers) > 0
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var adaptUserNameToEmailRet1Mock string
			mockey.Mock(AdaptUserNameToEmail, mockey.OptUnsafe).Return(adaptUserNameToEmailRet1Mock).Build()

			// prepare parameters
			var appId int64
			var inMembers AppMembers
			var inMembersManagersItem0 string
			inMembers.Managers = []string{inMembersManagersItem0,}

			// run target function and assert
			got1 := AdaptToAppAdmins(appId, inMembers)
			convey.So(len(got1), convey.ShouldEqual, 1)
		})
	})

}

func TestAdaptUserNameToEmailAutoGen(t *testing.T) {
	// Verify the function behavior when email validation is true.
	t.Run("testAdaptUserNameToEmail_ValidateTrue", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			validateRet1Mock := true
			mockey.Mock(emails.Validate, mockey.OptUnsafe).Return(validateRet1Mock).Build()

			var withSuffixRet1Mock string
			mockey.Mock(emails.WithSuffix).Return(withSuffixRet1Mock).Build()

			// prepare parameters
			var userName string

			// run target function and assert
			got1 := AdaptUserNameToEmail(userName)
			convey.So(got1, convey.ShouldBeBlank)
		})
	})

	// Verify the function behavior when email validation is false.
	t.Run("testAdaptUserNameToEmail_ValidateFalse", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var withSuffixRet1Mock string
			mockey.Mock(emails.WithSuffix).Return(withSuffixRet1Mock).Build()

			validateRet1Mock := false
			mockey.Mock(emails.Validate, mockey.OptUnsafe).Return(validateRet1Mock).Build()

			// prepare parameters
			var userName string

			// run target function and assert
			got1 := AdaptUserNameToEmail(userName)
			convey.So(got1, convey.ShouldBeBlank)
		})
	})

}

func TestAdaptAppUserSimpleInfoDBToIdlAutoGen(t *testing.T) {
	// param1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var withSuffixRet1Mock string
			mockey.Mock(emails.WithSuffix).Return(withSuffixRet1Mock).Build()

			// prepare parameters
			var userPtrValue db.IosArchUsers
			user := &userPtrValue

			// run target function and assert
			got1 := AdaptAppUserSimpleInfoDBToIdl(user)
			convey.So((*got1).Avatar, convey.ShouldBeBlank)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Email, convey.ShouldBeBlank)
			convey.So((*got1).FullName, convey.ShouldBeBlank)
		})
	})

}

