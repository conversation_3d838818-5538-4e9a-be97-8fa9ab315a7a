package operation_record

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/utils"
)

func TestGetValueDifferenceAutoGen(t *testing.T) {
	// Verify the behavior of GetValueDifference function with given conditions.
	t.Run("testGetValueDifference", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var mustTransInterfaceToStringRet1Mock string
			mockey.Mock(utils.MustTransInterfaceToString, mockey.OptUnsafe).Return(mustTransInterfaceToStringRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			oldConfig := 1
			var newConfig interface{}
			var prefix string

			// run target function and assert
			got1 := GetValueDifference(ctx, oldConfig, newConfig, prefix)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestValueDifferenceList_ToInterfaceListAutoGen(t *testing.T) {
	// Verify the conversion of ValueDifferenceList to an interface list.
	t.Run("testValueDifferenceList_ToInterfaceList", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiverAlias := []*ValueDifferenceItem{}
			receiver := ValueDifferenceList(receiverAlias)

			// run target function and assert
			got1 := receiver.ToInterfaceList()
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestValueDifferenceList_LogInfoAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			receiverAlias := []*ValueDifferenceItem{}
			receiver := ValueDifferenceList(receiverAlias)
			ctx := context.Background()
			var userName string
			var configType string
			convey.So(func() { receiver.LogInfo(ctx, userName, configType) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetValueDifferenceNewAutoGen(t *testing.T) {
	// Verify the function behavior when the configs are nil.
	t.Run("testGetValueDifferenceNew_NilConfigs", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			isNilRet1Mock := true
			mockey.Mock(utils.IsNil).Return(isNilRet1Mock).Build()

			var filedStructMapMock map[string]interface{}
			mockey.MockValue(&FiledStructMap).To(filedStructMapMock)

			var valueDifferenceItemMockPtrValue ValueDifferenceItem
			valueDifferenceItemMock := &valueDifferenceItemMockPtrValue
			mockey.Mock(NewValueDifferenceItem).Return(valueDifferenceItemMock).Build()

			var valueDifferenceListMock ValueDifferenceList
			mockey.Mock(GetMapValueDifference).Return(valueDifferenceListMock).Build()

			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var getValueDifferenceRet1Mock ValueDifferenceList
			mockey.Mock(GetValueDifference).Return(getValueDifferenceRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var oldConfig interface{}
			var newConfig interface{}
			var prefix string
			var firstParse bool

			// run target function and assert
			got1 := GetValueDifferenceNew(ctx, oldConfig, newConfig, prefix, firstParse)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

	// Verify that the function panics under certain conditions.
	t.Run("testGetValueDifferenceNew_ShouldPanic", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var valueDifferenceListMock ValueDifferenceList
			mockey.Mock(GetMapValueDifference).Return(valueDifferenceListMock).Build()

			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var getValueDifferenceRet1Mock ValueDifferenceList
			mockey.Mock(GetValueDifference).Return(getValueDifferenceRet1Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var isNilRet1Mock bool
			mockey.Mock(utils.IsNil).Return(isNilRet1Mock).Build()

			var filedStructMapMock map[string]interface{}
			mockey.MockValue(&FiledStructMap).To(filedStructMapMock)

			var valueDifferenceItemMockPtrValue ValueDifferenceItem
			valueDifferenceItemMock := &valueDifferenceItemMockPtrValue
			mockey.Mock(NewValueDifferenceItem).Return(valueDifferenceItemMock).Build()

			// prepare parameters
			ctx := context.Background()
			var oldConfig interface{}
			var newConfig interface{}
			var prefix string
			var firstParse bool
			convey.So(func() { _ = GetValueDifferenceNew(ctx, oldConfig, newConfig, prefix, firstParse) }, convey.ShouldPanic)
		})
	})

}

func TestNewValueDifferenceItemAutoGen(t *testing.T) {
	// Verify the behavior of NewValueDifferenceItem under default conditions.
	t.Run("testNewValueDifferenceItem_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var name string
			var oldValue interface{}
			var newValue interface{}

			// run target function and assert
			got1 := NewValueDifferenceItem(name, oldValue, newValue)
			convey.So((*got1).Name, convey.ShouldBeBlank)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestGetMapValueDifferenceAutoGen(t *testing.T) {
	// param2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var valueDifferenceItemMockPtrValue ValueDifferenceItem
			valueDifferenceItemMock := &valueDifferenceItemMockPtrValue
			mockey.Mock(NewValueDifferenceItem).Return(valueDifferenceItemMock).Build()

			// prepare parameters
			ctx := context.Background()
			oldConfig := make(map[string]interface{})
			var newConfig map[string]interface{}
			var prefix string

			// run target function and assert
			got1 := GetMapValueDifference(ctx, oldConfig, newConfig, prefix)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

	// param2 != nil
	// len(param3) > 0
	// len(param4) != 0
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var valueDifferenceItemMockPtrValue ValueDifferenceItem
			valueDifferenceItemMock := &valueDifferenceItemMockPtrValue
			mockey.Mock(NewValueDifferenceItem).Return(valueDifferenceItemMock).Build()

			// prepare parameters
			ctx := context.Background()
			oldConfig := make(map[string]interface{})
			var newConfig0 interface{}
			newConfig := map[string]interface{}{"0": newConfig0}
			prefix := "a"

			// run target function and assert
			got1 := GetMapValueDifference(ctx, oldConfig, newConfig, prefix)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

	// param2 != nil
	// len(param3) > 0
	// len(param4) == 0
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var valueDifferenceItemMockPtrValue ValueDifferenceItem
			valueDifferenceItemMock := &valueDifferenceItemMockPtrValue
			mockey.Mock(NewValueDifferenceItem).Return(valueDifferenceItemMock).Build()

			// prepare parameters
			ctx := context.Background()
			oldConfig := make(map[string]interface{})
			var newConfig0 interface{}
			newConfig := map[string]interface{}{"0": newConfig0}
			prefix := ""

			// run target function and assert
			got1 := GetMapValueDifference(ctx, oldConfig, newConfig, prefix)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestDifferenceList_LogInfoAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var userName string
			var configType string
			var uniqueID string
			var receiverPtrValue DifferenceList
			receiver := &receiverPtrValue
			convey.So(func() { receiver.LogInfo(ctx, userName, configType, uniqueID) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetListDifferenceAutoGen(t *testing.T) {
	// Verify the function behavior when the new list is not empty.
	t.Run("testGetListDifference_NotEmptyNewList", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var oldListItem0PtrValue ConfigListItem
			oldListItem0 := &oldListItem0PtrValue
			oldList := []*ConfigListItem{oldListItem0,}
			var newList ConfigList
			var name string

			// run target function and assert
			got1 := GetListDifference(name, oldList, newList)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).UpdateList), convey.ShouldEqual, 0)
			convey.So(len((*got1).CreateList), convey.ShouldEqual, 0)
			convey.So(len((*got1).DeleteList), convey.ShouldEqual, 1)
		})
	})

	// Verify the function behavior when the new list is not empty and a new item is created.
	t.Run("testGetListDifference_NotEmptyNewListAndCreateItem", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			var name string
			var oldList ConfigList
			var newListItem0PtrValue ConfigListItem
			newListItem0 := &newListItem0PtrValue
			newList := []*ConfigListItem{newListItem0,}

			// run target function and assert
			got1 := GetListDifference(name, oldList, newList)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).UpdateList), convey.ShouldEqual, 0)
			convey.So(len((*got1).CreateList), convey.ShouldEqual, 1)
			convey.So(len((*got1).DeleteList), convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior under default condition.
	t.Run("testGetListDifference_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// prepare parameters
			var name string
			var oldList ConfigList
			var newList ConfigList

			// run target function and assert
			got1 := GetListDifference(name, oldList, newList)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).UpdateList), convey.ShouldEqual, 0)
			convey.So(len((*got1).CreateList), convey.ShouldEqual, 0)
			convey.So(len((*got1).DeleteList), convey.ShouldEqual, 0)
		})
	})

}

