package operation_record

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
)

func TestAdaptOptimusGroupConfigToViewAutoGen(t *testing.T) {
	// Verify the function behavior when given valid input.
	t.Run("testAdaptOptimusGroupConfigToView", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			var configDBPtrValue db.OptimusConfigGroupOperationRecord
			configDB := &configDBPtrValue

			// run target function and assert
			got1, got2 := AdaptOptimusGroupConfigToView(ctx, configDB)
			convey.So((*got1).CreateTime, convey.ShouldEqual, "0001-01-01 00:00:00")
			convey.So((*got1).Operator, convey.ShouldBeBlank)
			convey.So((*got1).OperationDetail, convey.ShouldBeBlank)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).EventType, convey.ShouldBeBlank)
			convey.So((*got1).ConfigType, convey.ShouldEqual, 0)
			convey.So((*got1).Id, convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So((*got1).GroupName, convey.ShouldBeBlank)
		})
	})

}

func TestAdaptOptimusProjectConfigToViewAutoGen(t *testing.T) {
	// param2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			var configDBPtrValue db.OptimusConfigProjectOperationRecord
			configDB := &configDBPtrValue

			// run target function and assert
			got1, got2 := AdaptOptimusProjectConfigToView(ctx, configDB)
			convey.So((*got1).OperationDetail, convey.ShouldBeBlank)
			convey.So((*got1).Id, convey.ShouldEqual, 0)
			convey.So((*got1).ProjectID, convey.ShouldBeBlank)
			convey.So((*got1).CreateTime, convey.ShouldEqual, "0001-01-01 00:00:00")
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So((*got1).ConfigType, convey.ShouldEqual, 0)
			convey.So((*got1).Operator, convey.ShouldBeBlank)
			convey.So((*got1).EventType, convey.ShouldBeBlank)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

