package operation_record

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/consts"
)

func TestStoreGroupListOperationsAutoGen(t *testing.T) {
	// param5 != nil
	// len(param5.CreateList) > 0
	// len(:make()_ret-1) != 0
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			mockey.Mock(StoreGroupOperations).Return().Build()

			// prepare parameters
			var listPtrValue DifferenceList
			list := &listPtrValue
			var listCreateListItem0PtrValue DifferenceItem
			listCreateListItem0 := &listCreateListItem0PtrValue
			list.CreateList = []*DifferenceItem{listCreateListItem0,}
			ctx := context.Background()
			var userName string
			var configType consts.GroupConfigType
			var groupName string
			convey.So(func() { StoreGroupListOperations(ctx, userName, configType, groupName, list) }, convey.ShouldNotPanic)
		})
	})

	// param5 != nil
	// len(param5.DeleteList) > 0
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			mockey.Mock(StoreGroupOperations).Return().Build()

			// prepare parameters
			ctx := context.Background()
			var userName string
			var configType consts.GroupConfigType
			var groupName string
			var listPtrValue DifferenceList
			list := &listPtrValue
			var listDeleteListItem0PtrValue DifferenceItem
			listDeleteListItem0 := &listDeleteListItem0PtrValue
			list.DeleteList = []*DifferenceItem{listDeleteListItem0,}
			convey.So(func() { StoreGroupListOperations(ctx, userName, configType, groupName, list) }, convey.ShouldNotPanic)
		})
	})

	// param5 != nil
	// len(param5.UpdateList) > 0
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			mockey.Mock(StoreGroupOperations).Return().Build()

			// prepare parameters
			ctx := context.Background()
			var userName string
			var configType consts.GroupConfigType
			var groupName string
			var listPtrValue DifferenceList
			list := &listPtrValue
			var listUpdateListItem0PtrValue UpdateDifferenceItem
			listUpdateListItem0 := &listUpdateListItem0PtrValue
			list.UpdateList = []*UpdateDifferenceItem{listUpdateListItem0,}
			convey.So(func() { StoreGroupListOperations(ctx, userName, configType, groupName, list) }, convey.ShouldNotPanic)
		})
	})

	// default condition
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			mockey.Mock(StoreGroupOperations).Return().Build()

			// prepare parameters
			ctx := context.Background()
			var userName string
			var configType consts.GroupConfigType
			var groupName string
			var listPtrValue DifferenceList
			list := &listPtrValue
			convey.So(func() { StoreGroupListOperations(ctx, userName, configType, groupName, list) }, convey.ShouldNotPanic)
		})
	})

}

