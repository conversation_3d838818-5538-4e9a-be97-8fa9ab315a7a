package config_schema

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
)

func TestAdaptConfigSchemaDBListToIDLAutoGen(t *testing.T) {
	// Verify the function behavior when the input list is empty.
	t.Run("testAdaptConfigSchemaDBListToIDL_EmptyList", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			list := []*db.OptimusConfigSchema{}

			// run target function and assert
			got1 := AdaptConfigSchemaDBListToIDL(list)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when the input list is not empty.
	t.Run("testAdaptConfigSchemaDBListToIDL_NonEmptyList", func(t *testing.T) {
		mockey.Patch<PERSON>onvey("case_1", t, func() {
			// prepare parameters
			var listItem0PtrValue db.OptimusConfigSchema
			listItem0 := &listItem0PtrValue
			list := []*db.OptimusConfigSchema{listItem0,}

			// run target function and assert
			got1 := AdaptConfigSchemaDBListToIDL(list)
			convey.So(len(got1), convey.ShouldEqual, 1)
		})
	})

}

