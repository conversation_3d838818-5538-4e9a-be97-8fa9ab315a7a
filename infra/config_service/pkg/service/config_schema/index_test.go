package config_schema

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/data"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/common_config"
)

func Test_getConfigItemsBySchemaAutoGen(t *testing.T) {
	// Verify the behavior when UnmarshalString fails.
	t.Run("testGetConfigItemsBySchema_UnmarshalStringFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			unmarshalStringRet1Mock := fmt.Errorf("error")
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var scanFormItemsRet1Mock map[string]interface{}
			var scanFormItemsRet2Mock error
			mockey.Mock(scanFormItems).Return(scanFormItemsRet1Mock, scanFormItemsRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var schema string

			// run target function and assert
			got1, got2 := getConfigItemsBySchema(ctx, schema)
			convey.So(len(got1), convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the behavior when UnmarshalString succeeds.
	t.Run("testGetConfigItemsBySchema_UnmarshalStringSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var scanFormItemsRet1Mock map[string]interface{}
			var scanFormItemsRet2Mock error
			mockey.Mock(scanFormItems).Return(scanFormItemsRet1Mock, scanFormItemsRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var schema string

			// run target function and assert
			got1, got2 := getConfigItemsBySchema(ctx, schema)
			convey.So(got2 == nil, convey.ShouldBeFalse)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func Test_scanFormItemsAutoGen(t *testing.T) {
	// len(param2) > 0
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			var formItemsItem0 interface{}
			formItems := []interface{}{formItemsItem0,}

			// run target function and assert
			got1, got2 := scanFormItems(ctx, formItems)
			convey.So(len(got1), convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// default condition
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			ctx := context.Background()
			var formItems []interface{}

			// run target function and assert
			got1, got2 := scanFormItems(ctx, formItems)
			convey.So(len(got1), convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestDeleteConfigSchemaAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:DeleteConfigSchema()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			deleteConfigSchemaRet1Mock := fmt.Errorf("error")
			mockey.Mock(data.DeleteConfigSchema).Return(deleteConfigSchemaRet1Mock).Build()

			// prepare parameters
			var category string
			ctx := context.Background()
			var formName string

			// run target function and assert
			got1 := DeleteConfigSchema(ctx, formName, category)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:DeleteConfigSchema()_ret-1 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var deleteConfigSchemaRet1Mock error
			mockey.Mock(data.DeleteConfigSchema).Return(deleteConfigSchemaRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var formName string
			var category string

			// run target function and assert
			got1 := DeleteConfigSchema(ctx, formName, category)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestGetConfigSchemaByFormNameAndCategoryAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetConfigSchemaByFormNameAndCategory()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var optimusConfigSchemaMockPtrValue db.OptimusConfigSchema
			optimusConfigSchemaMock := &optimusConfigSchemaMockPtrValue
			getConfigSchemaByFormNameAndCategoryRet2Mock := fmt.Errorf("error")
			mockey.Mock(data.GetConfigSchemaByFormNameAndCategory).Return(optimusConfigSchemaMock, getConfigSchemaByFormNameAndCategoryRet2Mock).Build()

			// prepare parameters
			var formName string
			var category string
			ctx := context.Background()

			// run target function and assert
			got1, got2 := GetConfigSchemaByFormNameAndCategory(ctx, formName, category)
			convey.So(got1, convey.ShouldBeBlank)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetConfigSchemaByFormNameAndCategory()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/data:GetConfigSchemaByFormNameAndCategory()_ret-1 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var optimusConfigSchemaMock *db.OptimusConfigSchema
			var getConfigSchemaByFormNameAndCategoryRet2Mock error
			mockey.Mock(data.GetConfigSchemaByFormNameAndCategory).Return(optimusConfigSchemaMock, getConfigSchemaByFormNameAndCategoryRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var formName string
			var category string

			// run target function and assert
			got1, got2 := GetConfigSchemaByFormNameAndCategory(ctx, formName, category)
			convey.So(got1, convey.ShouldBeBlank)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

	// default condition
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var optimusConfigSchemaMockPtrValue db.OptimusConfigSchema
			optimusConfigSchemaMock := &optimusConfigSchemaMockPtrValue
			var getConfigSchemaByFormNameAndCategoryRet2Mock error
			mockey.Mock(data.GetConfigSchemaByFormNameAndCategory).Return(optimusConfigSchemaMock, getConfigSchemaByFormNameAndCategoryRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var formName string
			var category string

			// run target function and assert
			got1, got2 := GetConfigSchemaByFormNameAndCategory(ctx, formName, category)
			convey.So(got1, convey.ShouldBeBlank)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestUpdateConfigSchemaByNameAutoGen(t *testing.T) {
	// param3 == "rd_process_configs_dynamic"
	// len(code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/config_schema:getConfigItemsBySchema()_ret-1) > 0
	// github.com/bytedance/sonic:Marshal()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var updateConfigSchemaRet1Mock error
			mockey.Mock(data.UpdateConfigSchema).Return(updateConfigSchemaRet1Mock).Build()

			var getConfigItemsBySchemaRet1Mock0 interface{}
			getConfigItemsBySchemaRet1Mock := map[string]interface{}{"0": getConfigItemsBySchemaRet1Mock0}
			var getConfigItemsBySchemaRet2Mock error
			mockey.Mock(getConfigItemsBySchema).Return(getConfigItemsBySchemaRet1Mock, getConfigItemsBySchemaRet2Mock).Build()

			var marshalRet1Mock []byte
			marshalRet2Mock := fmt.Errorf("error")
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var updateGroupCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateGroupCommonConfig).Return(updateGroupCommonConfigRet1Mock).Build()

			var updateProjectCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateProjectCommonConfig).Return(updateProjectCommonConfigRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var formName string
			category := "rd_process_configs_dynamic"
			var schema string
			var operator string

			// run target function and assert
			got1 := UpdateConfigSchemaByName(ctx, formName, category, schema, operator)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// param3 == "rd_process_configs_dynamic"
	// len(code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/config_schema:getConfigItemsBySchema()_ret-1) > 0
	// github.com/bytedance/sonic:Marshal()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/common_config.CommonConfig:UpdateGroupCommonConfig()_ret-1 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			updateGroupCommonConfigRet1Mock := fmt.Errorf("error")
			mockey.Mock((*common_config.CommonConfig).UpdateGroupCommonConfig).Return(updateGroupCommonConfigRet1Mock).Build()

			var updateProjectCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateProjectCommonConfig).Return(updateProjectCommonConfigRet1Mock).Build()

			var updateConfigSchemaRet1Mock error
			mockey.Mock(data.UpdateConfigSchema).Return(updateConfigSchemaRet1Mock).Build()

			var getConfigItemsBySchemaRet1Mock0 interface{}
			getConfigItemsBySchemaRet1Mock := map[string]interface{}{"0": getConfigItemsBySchemaRet1Mock0}
			var getConfigItemsBySchemaRet2Mock error
			mockey.Mock(getConfigItemsBySchema).Return(getConfigItemsBySchemaRet1Mock, getConfigItemsBySchemaRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var formName string
			category := "rd_process_configs_dynamic"
			var schema string
			var operator string

			// run target function and assert
			got1 := UpdateConfigSchemaByName(ctx, formName, category, schema, operator)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// param3 == "rd_process_configs_dynamic"
	// len(code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/config_schema:getConfigItemsBySchema()_ret-1) > 0
	// github.com/bytedance/sonic:Marshal()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/common_config.CommonConfig:UpdateGroupCommonConfig()_ret-1 == nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var updateConfigSchemaRet1Mock error
			mockey.Mock(data.UpdateConfigSchema).Return(updateConfigSchemaRet1Mock).Build()

			var getConfigItemsBySchemaRet1Mock0 interface{}
			getConfigItemsBySchemaRet1Mock := map[string]interface{}{"0": getConfigItemsBySchemaRet1Mock0}
			var getConfigItemsBySchemaRet2Mock error
			mockey.Mock(getConfigItemsBySchema).Return(getConfigItemsBySchemaRet1Mock, getConfigItemsBySchemaRet2Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var updateGroupCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateGroupCommonConfig).Return(updateGroupCommonConfigRet1Mock).Build()

			var updateProjectCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateProjectCommonConfig).Return(updateProjectCommonConfigRet1Mock).Build()

			// prepare parameters
			var formName string
			category := "rd_process_configs_dynamic"
			var schema string
			var operator string
			ctx := context.Background()

			// run target function and assert
			got1 := UpdateConfigSchemaByName(ctx, formName, category, schema, operator)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3 != "rd_process_configs_dynamic"
	// param3 == "repo_configs_dynamic"
	// len(code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/config_schema:getConfigItemsBySchema()_ret-1) > 0
	// github.com/bytedance/sonic:Marshal()_ret-2 != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var updateConfigSchemaRet1Mock error
			mockey.Mock(data.UpdateConfigSchema).Return(updateConfigSchemaRet1Mock).Build()

			var getConfigItemsBySchemaRet1Mock0 interface{}
			getConfigItemsBySchemaRet1Mock := map[string]interface{}{"0": getConfigItemsBySchemaRet1Mock0}
			var getConfigItemsBySchemaRet2Mock error
			mockey.Mock(getConfigItemsBySchema).Return(getConfigItemsBySchemaRet1Mock, getConfigItemsBySchemaRet2Mock).Build()

			var marshalRet1Mock []byte
			marshalRet2Mock := fmt.Errorf("error")
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var updateGroupCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateGroupCommonConfig).Return(updateGroupCommonConfigRet1Mock).Build()

			var updateProjectCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateProjectCommonConfig).Return(updateProjectCommonConfigRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var formName string
			category := "repo_configs_dynamic"
			var schema string
			var operator string

			// run target function and assert
			got1 := UpdateConfigSchemaByName(ctx, formName, category, schema, operator)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// param3 != "rd_process_configs_dynamic"
	// param3 == "repo_configs_dynamic"
	// len(code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/config_schema:getConfigItemsBySchema()_ret-1) > 0
	// github.com/bytedance/sonic:Marshal()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/common_config.CommonConfig:UpdateProjectCommonConfig()_ret-1 != nil
	t.Run("case_4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var getConfigItemsBySchemaRet1Mock0 interface{}
			getConfigItemsBySchemaRet1Mock := map[string]interface{}{"0": getConfigItemsBySchemaRet1Mock0}
			var getConfigItemsBySchemaRet2Mock error
			mockey.Mock(getConfigItemsBySchema).Return(getConfigItemsBySchemaRet1Mock, getConfigItemsBySchemaRet2Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var updateGroupCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateGroupCommonConfig).Return(updateGroupCommonConfigRet1Mock).Build()

			updateProjectCommonConfigRet1Mock := fmt.Errorf("error")
			mockey.Mock((*common_config.CommonConfig).UpdateProjectCommonConfig).Return(updateProjectCommonConfigRet1Mock).Build()

			var updateConfigSchemaRet1Mock error
			mockey.Mock(data.UpdateConfigSchema).Return(updateConfigSchemaRet1Mock).Build()

			// prepare parameters
			var operator string
			ctx := context.Background()
			var formName string
			category := "repo_configs_dynamic"
			var schema string

			// run target function and assert
			got1 := UpdateConfigSchemaByName(ctx, formName, category, schema, operator)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// default condition
	t.Run("case_5", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// mock function returns or global values
			var getConfigItemsBySchemaRet1Mock map[string]interface{}
			var getConfigItemsBySchemaRet2Mock error
			mockey.Mock(getConfigItemsBySchema).Return(getConfigItemsBySchemaRet1Mock, getConfigItemsBySchemaRet2Mock).Build()

			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var updateGroupCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateGroupCommonConfig).Return(updateGroupCommonConfigRet1Mock).Build()

			var updateProjectCommonConfigRet1Mock error
			mockey.Mock((*common_config.CommonConfig).UpdateProjectCommonConfig).Return(updateProjectCommonConfigRet1Mock).Build()

			var updateConfigSchemaRet1Mock error
			mockey.Mock(data.UpdateConfigSchema).Return(updateConfigSchemaRet1Mock).Build()

			// prepare parameters
			var category string
			var schema string
			var operator string
			ctx := context.Background()
			var formName string

			// run target function and assert
			got1 := UpdateConfigSchemaByName(ctx, formName, category, schema, operator)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

