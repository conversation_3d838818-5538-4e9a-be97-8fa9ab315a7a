/**
 * @Date: 2024/3/14
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package errors

import (
	"errors"

	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/bytedancegitlab"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/codebaseapi"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/internal/middlewares"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
)

var Is = errors.Is

func FromCodebaseApiError(err error) error {
	switch {
	case errors.Is(err, middlewares.ErrApiBlocked):
		return bits_err.GITSERVER.ErrSystemRecovering

	case errors.Is(err, codebaseapi.ErrPathNotFound):
		return bits_err.GITSERVER.ErrPathNotFound

	case errors.Is(err, codebaseapi.ErrTooManyRequests):
		return bits_err.GITSERVER.ErrCodebaseApiTooManyRequests

	case errors.Is(err, codebaseapi.ErrMalformedChangeId):
		return bits_err.GITSERVER.ErrMalformedChangeId

	case errors.Is(err, codebaseapi.ErrChangeNotFound):
		return bits_err.GITSERVER.ErrChangeNotFound

	case errors.Is(err, codebaseapi.ErrBlameContentNotFound):
		return bits_err.GITSERVER.ErrBlameContentNotFound

	case errors.Is(err, codebaseapi.ErrRepoNotFound):
		return bits_err.GITSERVER.ErrRepoNotFound

	case errors.Is(err, codebaseapi.ErrRepoHasInstalledApp):
		return bits_err.GITSERVER.ErrRepoHasInstalledApp

	case errors.Is(err, codebaseapi.ErrForbidden):
		return bits_err.GITSERVER.ErrForbidden

	case errors.Is(err, codebaseapi.ErrPatchSetsNotFound):
		return bits_err.GITSERVER.ErrPatchSetsNotFound

	case errors.Is(err, codebaseapi.ErrBranchNotFound):
		return bits_err.GITSERVER.ErrBranchNotFound

	case errors.Is(err, codebaseapi.ErrAmbiguousArgument):
		return bits_err.GITSERVER.ErrAmbiguousArgument

	case errors.Is(err, codebaseapi.ErrChangeIsCompleted):
		return bits_err.GITSERVER.ErrChangeIsCompleted

	case errors.Is(err, codebaseapi.ErrSamePatternAlreadyExists):
		return bits_err.GITSERVER.ErrSamePatternAlreadyExists

	case errors.Is(err, codebaseapi.ErrMergeRequestAlreadyExists):
		return bits_err.GITSERVER.ErrGitlabMergeRequestAlreadyExists

	case errors.Is(err, codebaseapi.ErrSourceBranchNotFound):
		return bits_err.GITSERVER.ErrGitlabSourceBranchNotExists

	case errors.Is(err, codebaseapi.ErrTargetBranchNotFound):
		return bits_err.GITSERVER.ErrGitlabTargetBranchNotExists

	default:
		return err
	}
}

func FromGitlabApiError(err error) error {
	switch {
	case errors.Is(err, middlewares.ErrApiBlocked):
		return bits_err.GITSERVER.ErrSystemRecovering

	case errors.Is(err, bytedancegitlab.ErrBranchNotFound):
		return bits_err.GITSERVER.ErrGitlabBranchNotFound

	case errors.Is(err, bytedancegitlab.ErrBranchRefNameIsInvalid):
		return bits_err.GITSERVER.ErrGitlabBranchRefNameIsInvalid

	case errors.Is(err, bytedancegitlab.ErrTagNotFound):
		return bits_err.GITSERVER.ErrGitlabTagNotFound

	case errors.Is(err, bytedancegitlab.ErrBranchAlreadyExistsOrInvalidReferenceName):
		return bits_err.GITSERVER.ErrGitlabBranchAlreadyExistsOrInvalidReferenceName

	case errors.Is(err, bytedancegitlab.ErrFileNotFound):
		return bits_err.GITSERVER.ErrGitlabFileNotFound

	case errors.Is(err, bytedancegitlab.ErrProjectNotFound):
		return bits_err.GITSERVER.ErrGitlabProjectNotFound

	case errors.Is(err, bytedancegitlab.ErrMemberAlreadyExists):
		return bits_err.GITSERVER.ErrGitlabMemberAlreadyExists

	case errors.Is(err, bytedancegitlab.ErrCommitNotFound):
		return bits_err.GITSERVER.ErrGitlabCommitNotFound

	case errors.Is(err, bytedancegitlab.ErrProjectedBranchNotFound):
		return bits_err.GITSERVER.ErrGitlabProjectedBranchNotFound

	case errors.Is(err, bytedancegitlab.ErrMergeRequestAlreadyExists):
		return bits_err.GITSERVER.ErrGitlabMergeRequestAlreadyExists

	case errors.Is(err, bytedancegitlab.ErrSourceBranchOrTargetBranchNotExists):
		return bits_err.GITSERVER.ErrGitlabSourceBranchOrTargetBranchNotExists

	case errors.Is(err, bytedancegitlab.ErrSourceBranchNotExists):
		return bits_err.GITSERVER.ErrGitlabSourceBranchNotExists

	case errors.Is(err, bytedancegitlab.ErrTargetBranchNotExists):
		return bits_err.GITSERVER.ErrGitlabTargetBranchNotExists

	case errors.Is(err, bytedancegitlab.ErrUserUnauthorized):
		return bits_err.GITSERVER.ErrGitlabUserUnauthorized

	case errors.Is(err, bytedancegitlab.ErrMergeRequestAlreadyCherryPicked):
		return bits_err.GITSERVER.ErrGitlabMergeRequestAlreadyCherryPicked

	case errors.Is(err, bytedancegitlab.ErrMergeRequestNotFound):
		return bits_err.GITSERVER.ErrGitlabMergeRequestNotFound

	case errors.Is(err, bytedancegitlab.ErrAcceptMergeRequestDenied):
		return bits_err.GITSERVER.ErrGitlabAcceptMergeRequestDenied

	case errors.Is(err, bytedancegitlab.ErrTooManyRequests):
		return bits_err.GITSERVER.ErrGitlabApiTooManyRequests

	case errors.Is(err, bytedancegitlab.ErrIntervalServer):
		return bits_err.GITSERVER.ErrGitlabInternalServer

	case errors.Is(err, bytedancegitlab.ErrForbidden):
		return bits_err.GITSERVER.ErrForbidden

	case errors.Is(err, bytedancegitlab.ErrNotAllowOperateBranch):
		return bits_err.GITSERVER.ErrNotAllowOperateBranch

	default:
		return err
	}
}
