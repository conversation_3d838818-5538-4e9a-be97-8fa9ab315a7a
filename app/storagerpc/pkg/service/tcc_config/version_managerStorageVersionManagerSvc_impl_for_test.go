package tcc_config
import (
	"context"

	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/version_manager"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/storage_versionpb"
)

type version_managerStorageVersionManagerSvcImplForTestAutoGen struct{}

func (*version_managerStorageVersionManagerSvcImplForTestAutoGen) IsStoragePsmVersionOutdated(param1 context.Context, param2 version_manager.CheckPsmVersionOutdatedPayload)(bool, *entity.StoragePsmVersion, error) {
	var ret1 bool
	var ret2PtrValue entity.StoragePsmVersion
	ret2 := &ret2PtrValue
	var ret3 error
	return ret1, ret2, ret3
}

func (*version_managerStorageVersionManagerSvcImplForTestAutoGen) IsStoragePsmVersionOutdatedByChangeItemId(param1 context.Context, param2 uint64, param3 int64)(bool, *entity.StoragePsmVersion, error) {
	var ret1 bool
	var ret2PtrValue entity.StoragePsmVersion
	ret2 := &ret2PtrValue
	var ret3 error
	return ret1, ret2, ret3
}

func (*version_managerStorageVersionManagerSvcImplForTestAutoGen) IsStorageLocked(param1 context.Context, param2 sharedpb.WorkflowType, param3 uint64)(bool, error) {
	var ret1 bool
	var ret2 error
	return ret1, ret2
}

func (*version_managerStorageVersionManagerSvcImplForTestAutoGen) CheckStoragePsmVersion(param1 context.Context, param2 *storage_versionpb.CheckStoragePsmVersionReq)(*storage_versionpb.CheckStoragePsmVersionResp, error) {
	var ret1PtrValue storage_versionpb.CheckStoragePsmVersionResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*version_managerStorageVersionManagerSvcImplForTestAutoGen) GetAndInsertTccNsId(param1 context.Context, param2 version_manager.GetAndInsertTccNsIdPayload)(int64, *entity.StoragePsmVersion, error) {
	var ret1 int64
	var ret2PtrValue entity.StoragePsmVersion
	ret2 := &ret2PtrValue
	var ret3 error
	return ret1, ret2, ret3
}

func (*version_managerStorageVersionManagerSvcImplForTestAutoGen) UpdateTccPsmVersionByPsm(param1 context.Context, param2 sharedpb.WorkflowType, param3 uint64, param4 string)error {
	var ret1 error
	return ret1
}

func (*version_managerStorageVersionManagerSvcImplForTestAutoGen) GetNsIdMap(param1 context.Context, param2 version_manager.NsIdMapPayload)(entity.DBTCCNamespaceIDMap, error) {
	var ret1 entity.DBTCCNamespaceIDMap
	var ret2 error
	return ret1, ret2
}
