package tcc_config
import (
	"context"

	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql/entity"
)

type mysqlTccChangeDaoSvcImplForTestAutoGen struct{}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) Upsert(param1 context.Context, param2 *entity.TccChange)error {
	var ret1 error
	return ret1
}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) GetByWorkflowUniqueId(param1 context.Context, param2 string, param3 uint64)([]*entity.TccChange, error) {
	var ret1 []*entity.TccChange
	var ret2 error
	return ret1, ret2
}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) GetChangeByChangeItemId(param1 context.Context, param2 uint64)(*entity.TccChange, error) {
	var ret1PtrValue entity.TccChange
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) Delete(param1 context.Context, param2 *entity.TccChange)error {
	var ret1 error
	return ret1
}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) GetChangeByChangeItemUniqueKey(param1 context.Context, param2 string)(*entity.TccChange, error) {
	var ret1PtrValue entity.TccChange
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) GetChangeListByChangeItemUniqueKey(param1 context.Context, param2 []string)([]*entity.TccChange, error) {
	var ret1 []*entity.TccChange
	var ret2 error
	return ret1, ret2
}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) GetListByWorkflowUniqueIdAndPsmList(param1 context.Context, param2 string, param3 uint64, param4 []string)([]*entity.TccChange, error) {
	var ret1 []*entity.TccChange
	var ret2 error
	return ret1, ret2
}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) BatchUpsert(param1 context.Context, param2 []*entity.TccChange)error {
	var ret1 error
	return ret1
}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) DeleteByChangeItemId(param1 context.Context, param2 int64)error {
	var ret1 error
	return ret1
}

func (*mysqlTccChangeDaoSvcImplForTestAutoGen) GetByWorkflowUniqueIdAndPsm(param1 context.Context, param2 string, param3 uint64, param4 string)([]*entity.TccChange, error) {
	var ret1 []*entity.TccChange
	var ret2 error
	return ret1, ret2
}
