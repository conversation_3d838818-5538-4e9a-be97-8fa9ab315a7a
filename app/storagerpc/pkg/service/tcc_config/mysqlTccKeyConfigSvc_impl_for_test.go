package tcc_config
import (
	"context"

	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql/entity"
)

type mysqlTccKeyConfigSvcImplForTestAutoGen struct{}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) Upsert(param1 context.Context, param2 *entity.TccKeyConfig)error {
	var ret1 error
	return ret1
}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) Upserts(param1 context.Context, param2 []*entity.TccKeyConfig)error {
	var ret1 error
	return ret1
}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) ListTccKeys(param1 context.Context, param2 []uint64)([]*entity.TccKeyConfig, error) {
	var ret1 []*entity.TccKeyConfig
	var ret2 error
	return ret1, ret2
}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) DeleteConfigKey(param1 context.Context, param2 int64, param3 int64)error {
	var ret1 error
	return ret1
}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) UpdateConfigIsDeleted(param1 context.Context, param2 int64, param3 int64, param4 bool)error {
	var ret1 error
	return ret1
}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) UpdateConfigColumns(param1 context.Context, param2 int64, param3 int64, param4 int64)error {
	var ret1 error
	return ret1
}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) DeleteConfigKeysByChangeId(param1 context.Context, param2 int64)error {
	var ret1 error
	return ret1
}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) GetListByChangeId(param1 context.Context, param2 uint64)([]*entity.TccKeyConfig, error) {
	var ret1 []*entity.TccKeyConfig
	var ret2 error
	return ret1, ret2
}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) GetByDraftConfigId(param1 context.Context, param2 int64)(*entity.TccKeyConfig, error) {
	var ret1PtrValue entity.TccKeyConfig
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*mysqlTccKeyConfigSvcImplForTestAutoGen) GetByChangeIdAndConfigName(param1 context.Context, param2 int64, param3 string)(*entity.TccKeyConfig, error) {
	var ret1PtrValue entity.TccKeyConfig
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}
