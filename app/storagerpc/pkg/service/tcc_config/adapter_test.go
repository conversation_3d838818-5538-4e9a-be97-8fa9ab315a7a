package tcc_config

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/pkg/tcc/apis"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/sharedpb"
)

func Test_generateCreateTargetConfigReqAutoGen(t *testing.T) {
	// Verify the result of the function under default conditions.
	t.Run("testGenerateCreateTargetConfigReq_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var draft apis.ConfigInfo
			var dirID int64
			var env string
			var regions []string

			// run target function and assert
			got1 := generateCreateTargetConfigReq(regions, draft, dirID, env)
			convey.So(len(got1.Regions), convey.ShouldEqual, 0)
			convey.So(got1.EnvNewCreate, convey.ShouldEqual, false)
			convey.So(got1.DirId, convey.ShouldEqual, 0)
			convey.So(got1.CdnSupported, convey.ShouldEqual, false)
			convey.So(got1.WithL4Data, convey.ShouldEqual, false)
			convey.So(got1.DisableForceRegionsConsistencyCheck, convey.ShouldEqual, true)
			convey.So(got1.NsName, convey.ShouldBeBlank)
			convey.So(got1.Description, convey.ShouldBeBlank)
			convey.So(got1.Env, convey.ShouldBeBlank)
		})
	})

}

func Test_generateUpdateStatusTargetConfigAutoGen(t *testing.T) {
	// Verify the function behavior under default conditions.
	t.Run("testGenerateUpdateStatusTargetConfig_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var targetConfig apis.ConfigInfo

			// run target function and assert
			got1 := generateUpdateStatusTargetConfig(targetConfig)
			convey.So(got1.Id, convey.ShouldEqual, 0)
			convey.So(got1.Status, convey.ShouldEqual, "disable")
			convey.So(got1.Env, convey.ShouldBeBlank)
		})
	})

}

func Test_mapControlPlaneAutoGen(t *testing.T) {
	// Verify the function output when the parameter is 5.
	t.Run("testMapControlPlane_Param5", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			cpAlias := int32(5)
			cp := sharedpb.TccControlPlane(cpAlias)

			// run target function and assert
			got1 := mapControlPlane(cp)
			convey.So(got1, convey.ShouldEqual, "BOE")
		})
	})

	// Verify the function output when the parameter is 1.
	t.Run("testMapControlPlane_Param1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			cpAlias := int32(1)
			cp := sharedpb.TccControlPlane(cpAlias)

			// run target function and assert
			got1 := mapControlPlane(cp)
			convey.So(got1, convey.ShouldEqual, "CN")
		})
	})

	// Verify the function output when the parameter is 2.
	t.Run("testMapControlPlane_Param2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// prepare parameters
			cpAlias := int32(2)
			cp := sharedpb.TccControlPlane(cpAlias)

			// run target function and assert
			got1 := mapControlPlane(cp)
			convey.So(got1, convey.ShouldEqual, "I18N")
		})
	})

	// Verify the function output when the parameter is 3.
	t.Run("testMapControlPlane_Param3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// prepare parameters
			cpAlias := int32(3)
			cp := sharedpb.TccControlPlane(cpAlias)

			// run target function and assert
			got1 := mapControlPlane(cp)
			convey.So(got1, convey.ShouldEqual, "EU-TTP")
		})
	})

	// Verify the function output when the parameter is 4.
	t.Run("testMapControlPlane_Param4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// prepare parameters
			cpAlias := int32(4)
			cp := sharedpb.TccControlPlane(cpAlias)

			// run target function and assert
			got1 := mapControlPlane(cp)
			convey.So(got1, convey.ShouldEqual, "US-TTP")
		})
	})

	// Verify the function output when the parameter is 6.
	t.Run("testMapControlPlane_Param6", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// prepare parameters
			cpAlias := int32(6)
			cp := sharedpb.TccControlPlane(cpAlias)

			// run target function and assert
			got1 := mapControlPlane(cp)
			convey.So(got1, convey.ShouldEqual, "Unspecified")
		})
	})

}

