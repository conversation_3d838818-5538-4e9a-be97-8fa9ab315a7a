package tcc_config

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/sharedpb"
)

func Test_tccConfigSvc_ImportMultiTccConfigsAutoGen(t *testing.T) {
	// Verify the behavior when the version is outdated.
	t.Run("testTccConfigSvc_ImportMultiTccConfigs_VersionOutdated", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var upsertRet1Mock error
			mockey.Mock((*mysqlStoragePsmVersionDaoSvcImplForTestAutoGen).Upsert, mockey.OptUnsafe).Return(upsertRet1Mock).Build()

			var importTccConfigRespMockPtrValue platform_configpb.ImportTccConfigResp
			importTccConfigRespMock := &importTccConfigRespMockPtrValue
			var importTccConfigRet2Mock error
			mockey.Mock(tccConfigSvc.ImportTccConfig).Return(importTccConfigRespMock, importTccConfigRet2Mock).Build()

			var stringRet1Mock string
			mockey.Mock(sharedpb.WorkflowType.String, mockey.OptUnsafe).Return(stringRet1Mock).Build()

			var codeRet1Mock int64
			mockey.Mock((*bits_err.BitsErr).Code, mockey.OptUnsafe).Return(codeRet1Mock).Build()

			var dBTCCNamespaceIDMapMock entity.DBTCCNamespaceIDMap
			var getNsIdMapRet2Mock error
			mockey.Mock((*version_managerStorageVersionManagerSvcImplForTestAutoGen).GetNsIdMap, mockey.OptUnsafe).Return(dBTCCNamespaceIDMapMock, getNsIdMapRet2Mock).Build()

			var usernameRet1Mock string
			mockey.Mock(cdauth.Username).Return(usernameRet1Mock).Build()

			var workflowTypeMock sharedpb.WorkflowType
			mockey.Mock((*platform_configpb.ImportMultiTccConfigsReq).GetWorkflowType, mockey.OptUnsafe).Return(workflowTypeMock).Build()

			var bitsErrMockPtrValue bits_err.BitsErr
			bitsErrMock := &bitsErrMockPtrValue
			mockey.Mock(bits_err.ToBizError).Return(bitsErrMock).Build()

			isStoragePsmVersionOutdatedRet1Mock := true
			var storagePsmVersionMockPtrValue entity.StoragePsmVersion
			storagePsmVersionMock := &storagePsmVersionMockPtrValue
			var isStoragePsmVersionOutdatedRet3Mock error
			mockey.Mock((*version_managerStorageVersionManagerSvcImplForTestAutoGen).IsStoragePsmVersionOutdated, mockey.OptUnsafe).Return(isStoragePsmVersionOutdatedRet1Mock, storagePsmVersionMock, isStoragePsmVersionOutdatedRet3Mock).Build()

			var toJsonRet1Mock string
			mockey.Mock(utils.ToJson).Return(toJsonRet1Mock).Build()

			// prepare parameters
			receiver := tccConfigSvc{}
			var receiverStorageVersionManagerSvcImpl version_managerStorageVersionManagerSvcImplForTestAutoGen
			receiver.storageVersionManagerSvc = &receiverStorageVersionManagerSvcImpl
			ctx := context.Background()
			var reqPtrValue platform_configpb.ImportMultiTccConfigsReq
			req := &reqPtrValue

			// run target function and assert
			got1, got2 := receiver.ImportMultiTccConfigs(ctx, req)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the scenario when the Upsert operation encounters an error.
	t.Run("testTccConfigSvc_ImportMultiTccConfigs_UpsertError", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var usernameRet1Mock string
			mockey.Mock(cdauth.Username).Return(usernameRet1Mock).Build()

			upsertRet1Mock := fmt.Errorf("error")
			mockey.Mock((*mysqlStoragePsmVersionDaoSvcImplForTestAutoGen).Upsert, mockey.OptUnsafe).Return(upsertRet1Mock).Build()

			var importTccConfigRespMockPtrValue platform_configpb.ImportTccConfigResp
			importTccConfigRespMock := &importTccConfigRespMockPtrValue
			var importTccConfigRet2Mock error
			mockey.Mock(tccConfigSvc.ImportTccConfig).Return(importTccConfigRespMock, importTccConfigRet2Mock).Build()

			var workflowTypeMock sharedpb.WorkflowType
			mockey.Mock((*platform_configpb.ImportMultiTccConfigsReq).GetWorkflowType, mockey.OptUnsafe).Return(workflowTypeMock).Build()

			var bitsErrMockPtrValue bits_err.BitsErr
			bitsErrMock := &bitsErrMockPtrValue
			mockey.Mock(bits_err.ToBizError).Return(bitsErrMock).Build()

			var isStoragePsmVersionOutdatedRet1Mock bool
			var storagePsmVersionMockPtrValue entity.StoragePsmVersion
			storagePsmVersionMock := &storagePsmVersionMockPtrValue
			isStoragePsmVersionOutdatedRet3Mock := fmt.Errorf("error")
			mockey.Mock((*version_managerStorageVersionManagerSvcImplForTestAutoGen).IsStoragePsmVersionOutdated, mockey.OptUnsafe).Return(isStoragePsmVersionOutdatedRet1Mock, storagePsmVersionMock, isStoragePsmVersionOutdatedRet3Mock).Build()

			var stringRet1Mock string
			mockey.Mock(sharedpb.WorkflowType.String, mockey.OptUnsafe).Return(stringRet1Mock).Build()

			var toJsonRet1Mock string
			mockey.Mock(utils.ToJson).Return(toJsonRet1Mock).Build()

			var codeRet1Mock int64
			mockey.Mock((*bits_err.BitsErr).Code, mockey.OptUnsafe).Return(codeRet1Mock).Build()

			var dBTCCNamespaceIDMapMock entity.DBTCCNamespaceIDMap
			var getNsIdMapRet2Mock error
			mockey.Mock((*version_managerStorageVersionManagerSvcImplForTestAutoGen).GetNsIdMap, mockey.OptUnsafe).Return(dBTCCNamespaceIDMapMock, getNsIdMapRet2Mock).Build()

			// prepare parameters
			var reqPtrValue platform_configpb.ImportMultiTccConfigsReq
			req := &reqPtrValue
			receiver := tccConfigSvc{}
			var receiverStorageVersionManagerSvcImpl version_managerStorageVersionManagerSvcImplForTestAutoGen
			receiver.storageVersionManagerSvc = &receiverStorageVersionManagerSvcImpl
			var receiverStoragePsmVersionDaoSvcImpl mysqlStoragePsmVersionDaoSvcImplForTestAutoGen
			receiver.storagePsmVersionDaoSvc = &receiverStoragePsmVersionDaoSvcImpl
			ctx := context.Background()

			// run target function and assert
			got1, got2 := receiver.ImportMultiTccConfigs(ctx, req)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the case when ImportTccConfig has no error.
	t.Run("testTccConfigSvc_ImportMultiTccConfigs_ImportTccConfigNoError", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var stringRet1Mock string
			mockey.Mock(sharedpb.WorkflowType.String, mockey.OptUnsafe).Return(stringRet1Mock).Build()

			var codeRet1Mock int64
			mockey.Mock((*bits_err.BitsErr).Code, mockey.OptUnsafe).Return(codeRet1Mock).Build()

			var bitsErrMockPtrValue bits_err.BitsErr
			bitsErrMock := &bitsErrMockPtrValue
			mockey.Mock(bits_err.ToBizError).Return(bitsErrMock).Build()

			var toJsonRet1Mock string
			mockey.Mock(utils.ToJson).Return(toJsonRet1Mock).Build()

			var workflowTypeMock sharedpb.WorkflowType
			mockey.Mock((*platform_configpb.ImportMultiTccConfigsReq).GetWorkflowType, mockey.OptUnsafe).Return(workflowTypeMock).Build()

			var dBTCCNamespaceIDMapMock entity.DBTCCNamespaceIDMap
			getNsIdMapRet2Mock := fmt.Errorf("error")
			mockey.Mock((*version_managerStorageVersionManagerSvcImplForTestAutoGen).GetNsIdMap, mockey.OptUnsafe).Return(dBTCCNamespaceIDMapMock, getNsIdMapRet2Mock).Build()

			var usernameRet1Mock string
			mockey.Mock(cdauth.Username).Return(usernameRet1Mock).Build()

			var upsertRet1Mock error
			mockey.Mock((*mysqlStoragePsmVersionDaoSvcImplForTestAutoGen).Upsert, mockey.OptUnsafe).Return(upsertRet1Mock).Build()

			var importTccConfigRespMockPtrValue platform_configpb.ImportTccConfigResp
			importTccConfigRespMock := &importTccConfigRespMockPtrValue
			var importTccConfigRet2Mock error
			mockey.Mock(tccConfigSvc.ImportTccConfig).Return(importTccConfigRespMock, importTccConfigRet2Mock).Build()

			var isStoragePsmVersionOutdatedRet1Mock bool
			var storagePsmVersionMockPtrValue entity.StoragePsmVersion
			storagePsmVersionMock := &storagePsmVersionMockPtrValue
			var isStoragePsmVersionOutdatedRet3Mock error
			mockey.Mock((*version_managerStorageVersionManagerSvcImplForTestAutoGen).IsStoragePsmVersionOutdated, mockey.OptUnsafe).Return(isStoragePsmVersionOutdatedRet1Mock, storagePsmVersionMock, isStoragePsmVersionOutdatedRet3Mock).Build()

			// prepare parameters
			receiver := tccConfigSvc{}
			var receiverStorageVersionManagerSvcImpl version_managerStorageVersionManagerSvcImplForTestAutoGen
			receiver.storageVersionManagerSvc = &receiverStorageVersionManagerSvcImpl
			ctx := context.Background()
			var reqPtrValue platform_configpb.ImportMultiTccConfigsReq
			req := &reqPtrValue

			// run target function and assert
			got1, got2 := receiver.ImportMultiTccConfigs(ctx, req)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the case when ImportTccConfig has no error but Upsert has an error.
	t.Run("testTccConfigSvc_ImportMultiTccConfigs_ImportTccConfigNoError_UpsertError", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var codeRet1Mock int64
			mockey.Mock((*bits_err.BitsErr).Code, mockey.OptUnsafe).Return(codeRet1Mock).Build()

			upsertRet1Mock := fmt.Errorf("error")
			mockey.Mock((*mysqlStoragePsmVersionDaoSvcImplForTestAutoGen).Upsert, mockey.OptUnsafe).Return(upsertRet1Mock).Build()

			var stringRet1Mock string
			mockey.Mock(sharedpb.WorkflowType.String, mockey.OptUnsafe).Return(stringRet1Mock).Build()

			var usernameRet1Mock string
			mockey.Mock(cdauth.Username).Return(usernameRet1Mock).Build()

			var workflowTypeMock sharedpb.WorkflowType
			mockey.Mock((*platform_configpb.ImportMultiTccConfigsReq).GetWorkflowType, mockey.OptUnsafe).Return(workflowTypeMock).Build()

			var dBTCCNamespaceIDMapMock entity.DBTCCNamespaceIDMap
			var getNsIdMapRet2Mock error
			mockey.Mock((*version_managerStorageVersionManagerSvcImplForTestAutoGen).GetNsIdMap, mockey.OptUnsafe).Return(dBTCCNamespaceIDMapMock, getNsIdMapRet2Mock).Build()

			var bitsErrMockPtrValue bits_err.BitsErr
			bitsErrMock := &bitsErrMockPtrValue
			mockey.Mock(bits_err.ToBizError).Return(bitsErrMock).Build()

			var importTccConfigRespMockPtrValue platform_configpb.ImportTccConfigResp
			importTccConfigRespMock := &importTccConfigRespMockPtrValue
			var importTccConfigRet2Mock error
			mockey.Mock(tccConfigSvc.ImportTccConfig).Return(importTccConfigRespMock, importTccConfigRet2Mock).Build()

			var toJsonRet1Mock string
			mockey.Mock(utils.ToJson).Return(toJsonRet1Mock).Build()

			var isStoragePsmVersionOutdatedRet1Mock bool
			var storagePsmVersionMockPtrValue entity.StoragePsmVersion
			storagePsmVersionMock := &storagePsmVersionMockPtrValue
			var isStoragePsmVersionOutdatedRet3Mock error
			mockey.Mock((*version_managerStorageVersionManagerSvcImplForTestAutoGen).IsStoragePsmVersionOutdated, mockey.OptUnsafe).Return(isStoragePsmVersionOutdatedRet1Mock, storagePsmVersionMock, isStoragePsmVersionOutdatedRet3Mock).Build()

			// prepare parameters
			receiver := tccConfigSvc{}
			var receiverStorageVersionManagerSvcImpl version_managerStorageVersionManagerSvcImplForTestAutoGen
			receiver.storageVersionManagerSvc = &receiverStorageVersionManagerSvcImpl
			var receiverStoragePsmVersionDaoSvcImpl mysqlStoragePsmVersionDaoSvcImplForTestAutoGen
			receiver.storagePsmVersionDaoSvc = &receiverStoragePsmVersionDaoSvcImpl
			ctx := context.Background()
			var reqPtrValue platform_configpb.ImportMultiTccConfigsReq
			req := &reqPtrValue

			// run target function and assert
			got1, got2 := receiver.ImportMultiTccConfigs(ctx, req)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

