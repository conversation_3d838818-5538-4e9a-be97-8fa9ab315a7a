package tcc_config
import (
	"context"

	"code.byted.org/devinfra/hagrid/pkg/tcc/apis"
)

type apisApiImplForTestAutoGen struct{}

func (*apisApiImplForTestAutoGen) GetConfigById(param1 context.Context, param2 int64, param3 string, param4 ...apis.Option)(apis.ConfigResp, error) {
	var ret1 apis.ConfigResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) GetDirListByNameSpace(param1 context.Context, param2 string, param3 string, param4 ...apis.Option)(apis.DirListResp, error) {
	var ret1 apis.DirListResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) GetConfigVersionById(param1 context.Context, param2 int64, param3 int64, param4 string, param5 ...apis.Option)(apis.ConfigVersionResp, error) {
	var ret1 apis.ConfigVersionResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) GetConfigList(param1 context.Context, param2 apis.SearchConfigReq, param3 string, param4 ...apis.Option)(apis.SearchConfigResp, error) {
	var ret1 apis.SearchConfigResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) GetNameSpace(param1 context.Context, param2 string, param3 string, param4 ...apis.Option)(apis.NameSpaceResp, error) {
	var ret1 apis.NameSpaceResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) GetDeploymentById(param1 context.Context, param2 int64, param3 string, param4 ...apis.Option)(apis.DeploymentResp, error) {
	var ret1 apis.DeploymentResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) DeploymentCreateCheck(param1 context.Context, param2 apis.DeployCreateCheckReq, param3 string, param4 ...apis.Option)(apis.DeployCreateCheckResp, int, error) {
	var ret1 apis.DeployCreateCheckResp
	var ret2 int
	var ret3 error
	return ret1, ret2, ret3
}

func (*apisApiImplForTestAutoGen) DeploymentRollbackCheck(param1 context.Context, param2 apis.DeployRollbackCheckReq, param3 string, param4 ...apis.Option)(apis.DeployRollbackCheckResp, int, error) {
	var ret1 apis.DeployRollbackCheckResp
	var ret2 int
	var ret3 error
	return ret1, ret2, ret3
}

func (*apisApiImplForTestAutoGen) OperateCustomStep(param1 context.Context, param2 apis.OperateCustomStepReq, param3 string, param4 ...apis.Option)(apis.OperateCustomStepResp, error) {
	var ret1 apis.OperateCustomStepResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) UpdateConfig(param1 context.Context, param2 apis.UpdateConfigReq, param3 string, param4 ...apis.Option)(apis.UpsertConfigResp, error) {
	var ret1 apis.UpsertConfigResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) UpdateConfigV2(param1 context.Context, param2 apis.UpdateConfigReqV2, param3 string, param4 ...apis.Option)(apis.UpsertConfigResp, error) {
	var ret1 apis.UpsertConfigResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) CreateConfig(param1 context.Context, param2 apis.CreateConfigReq, param3 string, param4 ...apis.Option)(apis.UpsertConfigResp, error) {
	var ret1 apis.UpsertConfigResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) CreateConfigV2(param1 context.Context, param2 apis.CreateConfigReqV2, param3 string, param4 ...apis.Option)(apis.UpsertConfigResp, error) {
	var ret1 apis.UpsertConfigResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) UpdateConfigStatus(param1 context.Context, param2 apis.UpdateConfigStatusReq, param3 string, param4 ...apis.Option)(apis.UpdateConfigStatusResp, error) {
	var ret1 apis.UpdateConfigStatusResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) ImportConfig(param1 context.Context, param2 apis.ImportConfigReq, param3 string, param4 ...apis.Option)(apis.ImportConfigResp, error) {
	var ret1 apis.ImportConfigResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) CreateDraftEnv(param1 context.Context, param2 apis.CreateDraftReq, param3 string, param4 ...apis.Option)(apis.BaseResp, error) {
	var ret1 apis.BaseResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) OperateDeployment(param1 context.Context, param2 apis.OperateDeploymentReq, param3 string, param4 ...apis.Option)(apis.OperateDeploymentResp, error) {
	var ret1 apis.OperateDeploymentResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) GetDeployStrategy(param1 context.Context, param2 apis.GetDeployStrategyReq, param3 string, param4 ...apis.Option)(apis.GetDeployStrategyResp, error) {
	var ret1 apis.GetDeployStrategyResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) GetDeployStrategyList(param1 context.Context, param2 apis.GetDeployStrategyListReq, param3 string, param4 ...apis.Option)(apis.GetDeployStrategyListResp, error) {
	var ret1 apis.GetDeployStrategyListResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) CreateEnv(param1 context.Context, param2 apis.CreateEnvReq, param3 string, param4 ...apis.Option)(apis.CreateEnvResp, error) {
	var ret1 apis.CreateEnvResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) ExtendRegion(param1 context.Context, param2 apis.ExtendRegionReq, param3 string, param4 ...apis.Option)(apis.ExtendRegionResp, error) {
	var ret1 apis.ExtendRegionResp
	var ret2 error
	return ret1, ret2
}

func (*apisApiImplForTestAutoGen) ViewClearValue(param1 context.Context, param2 apis.ViewClearValueReq, param3 string, param4 ...apis.Option)(apis.ViewClearValueResp, error) {
	var ret1 apis.ViewClearValueResp
	var ret2 error
	return ret1, ret2
}
