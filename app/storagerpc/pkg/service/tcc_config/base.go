package tcc_config

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/tcc_region"
	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/version_manager"
	"code.byted.org/devinfra/hagrid/pkg/tcc/apis"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
)

//go:generate mockgen -destination mock/service_mock.go -package mock code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/tcc_config TccConfigSvc

// 直接操作 tcc 配置项
type TccConfigSvc interface {
	GetTccConfigList(ctx context.Context, req *platform_configpb.GetTccConfigListReq) (resp *platform_configpb.GetTccConfigListResp, err error)
	GetTccOnlineConfigInfo(ctx context.Context, req *platform_configpb.GetTccOnlineConfigInfoReq) (resp *platform_configpb.GetTccOnlineConfigInfoResp, err error)
	GetTccConfigDiffByVersion(ctx context.Context, req *platform_configpb.GetTccConfigDiffByVersionReq) (resp *platform_configpb.GetTccConfigDiffByVersionResp, err error)
	GetTccConfigDiffByDeployTarget(ctx context.Context, req *platform_configpb.GetTccConfigDiffByDeployTargetReq, withEnv bool) (resp *platform_configpb.GetTccConfigDiffByDeployTargetResp, err error)
	GetTccConfigDiffByControlPlaneDeployTarget(ctx context.Context, req *platform_configpb.GetTccConfigDiffByControlPlaneDeployTargetReq) (resp *platform_configpb.GetTccConfigDiffByControlPlaneDeployTargetResp, err error)

	CreateTccConfig(ctx context.Context, req *platform_configpb.CreateTccConfigReq) (resp *platform_configpb.CreateTccConfigResp, err error)
	ImportTccConfig(ctx context.Context, req *platform_configpb.ImportTccConfigReq, isMultiImport bool) (resp *platform_configpb.ImportTccConfigResp, err error)
	UpdateTccConfigStatus(ctx context.Context, req *change_itempb.UpdateTccConfigStatusReq) (resp *change_itempb.UpdateTccConfigStatusResp, err error)
	UpdateTccConfig(ctx context.Context, req *platform_configpb.UpdateTccConfigReq) (resp *platform_configpb.UpdateTccConfigResp, err error)
	ImportTccConfigToTargetRegion(ctx context.Context, req *change_itempb.ImportTccConfigToTargetRegionReq) (resp *change_itempb.ImportTccConfigToTargetRegionResp, err error)
	ImportMultiTccConfigs(ctx context.Context, req *platform_configpb.ImportMultiTccConfigsReq) (resp *platform_configpb.ImportMultiTccConfigsResp, err error)
	GetTccConfigDiffByTicketId(ctx context.Context, req *platform_configpb.GetTccConfigDiffByTicketIdReq) (resp *platform_configpb.GetTccConfigDiffByTicketIdResp, err error)
}

type tccConfigSvc struct {
	TccClient                *apis.TccClientWrapper
	TccChangeDb              mysql.TccChangeDaoSvc
	TccKeyConfigDb           mysql.TccKeyConfigSvc
	storageVersionManagerSvc version_manager.StorageVersionManagerSvc
	storagePsmVersionDaoSvc  mysql.StoragePsmVersionDaoSvc
	tccRegionSvc             tcc_region.TccRegionSvc
}

func NewTccConfigSvc() TccConfigSvc {
	return &tccConfigSvc{
		TccClient:                apis.NewTCCClientWrapper(),
		TccKeyConfigDb:           mysql.NewTccKeyConfigSvc(),
		TccChangeDb:              mysql.NewTccChangeDaoSvc(),
		storageVersionManagerSvc: version_manager.NewStorageVersionManagerSvc(),
		storagePsmVersionDaoSvc:  mysql.NewStoragePsmVersionDaoSvc(),
		tccRegionSvc:             tcc_region.NewTccRegionSvc(),
	}
}
