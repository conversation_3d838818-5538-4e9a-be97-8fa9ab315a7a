package tcc_config
import (
	"context"

	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql/entity"
)

type mysqlStoragePsmVersionDaoSvcImplForTestAutoGen struct{}

func (*mysqlStoragePsmVersionDaoSvcImplForTestAutoGen) GetStoragePsmVersionByWorkflowUniqueId(param1 context.Context, param2 string, param3 uint64, param4 string)(*entity.StoragePsmVersion, error) {
	var ret1PtrValue entity.StoragePsmVersion
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*mysqlStoragePsmVersionDaoSvcImplForTestAutoGen) BatchUpsert(param1 context.Context, param2 []*entity.StoragePsmVersion)error {
	var ret1 error
	return ret1
}

func (*mysqlStoragePsmVersionDaoSvcImplForTestAutoGen) Upsert(param1 context.Context, param2 *entity.StoragePsmVersion, param3 bool)error {
	var ret1 error
	return ret1
}

func (*mysqlStoragePsmVersionDaoSvcImplForTestAutoGen) GetStoragePsmVersionListByWorkflowUniqueId(param1 context.Context, param2 string, param3 uint64)([]*entity.StoragePsmVersion, error) {
	var ret1 []*entity.StoragePsmVersion
	var ret2 error
	return ret1, ret2
}

func (*mysqlStoragePsmVersionDaoSvcImplForTestAutoGen) GetStoragePsmVersionListByWorkflowUniqueIdAndPsmList(param1 context.Context, param2 string, param3 uint64, param4 []string)([]*entity.StoragePsmVersion, error) {
	var ret1 []*entity.StoragePsmVersion
	var ret2 error
	return ret1, ret2
}
