package tcc_config
import (
	"context"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/authpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
)

type tcc_regionTccRegionSvcImplForTestAutoGen struct{}

func (*tcc_regionTccRegionSvcImplForTestAutoGen) GetTccRegionList(param1 context.Context, param2 *platform_configpb.GetTccRegionListReq)(*platform_configpb.GetTccRegionListResp, error) {
	var ret1PtrValue platform_configpb.GetTccRegionListResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*tcc_regionTccRegionSvcImplForTestAutoGen) GetTccDirList(param1 context.Context, param2 *platform_configpb.GetTccDirListReq)(*platform_configpb.GetTccDirListResp, error) {
	var ret1PtrValue platform_configpb.GetTccDirListResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*tcc_regionTccRegionSvcImplForTestAutoGen) CheckTccDeveloperPermission(param1 context.Context, param2 *authpb.CheckTccDeveloperPermissionReq)(*authpb.CheckTccDeveloperPermissionResp, error) {
	var ret1PtrValue authpb.CheckTccDeveloperPermissionResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}
