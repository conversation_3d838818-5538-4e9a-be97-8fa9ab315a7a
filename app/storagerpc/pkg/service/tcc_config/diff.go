package tcc_config

import (
	"context"
	"sort"
	"sync"

	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/model"
	ctxutils "code.byted.org/devinfra/hagrid/app/storagerpc/utils"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pkg/panicguard"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
	storage_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/sharedpb"
	"code.byted.org/gopkg/lang/v2/operatorx"
	"code.byted.org/gopkg/logs/v2"
	"golang.org/x/sync/errgroup"
)

func (t tccConfigSvc) ValidateRollbackTccConfigDiff(ctx context.Context, req *platform_configpb.GetTccConfigDiffByTicketIdReq) error {
	if req.GetRollbackType() == storage_sharedpb.TCCRollbackType_ROLLBACK_TYPE_UNSPECIFIED {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid rollback type")
	}

	//	1. 原单回滚
	if req.RollbackType == storage_sharedpb.TCCRollbackType_ROLLBACK_TYPE_ORIGIN {
		if req.GetTicketId() == 0 {
			return bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid ticket id")
		}
		return nil
	}

	// 2. 新单回滚
	// 2.1 发布总览回滚 diff
	if req.GetIsDeployOverviewDiff() {
		if req.GetRollbackTicketId() == 0 {
			return bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid rollback ticket id")
		}
		return nil
	}

	// 2.2 回滚前预览
	if req.GetTicketId() == 0 {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid ticket id")
	}
	return nil
}

// 回滚前预览：
//
//	 原单回滚： diff 左侧是发布工单的 to version，右侧是发布工单的 from version
//		新单回滚： diff 左侧是线上最新版本，右侧是发布工单的 from version
//
// 发布总览回滚 diff：
//
//	 原单回滚： diff 左侧是发布工单的 to version，右侧是发布工单的 from version
//		新单回滚： diff 左侧是回滚工单 from version，右侧是回滚工单 to version
func (t tccConfigSvc) GetTccConfigDiffByTicketId(ctx context.Context, req *platform_configpb.GetTccConfigDiffByTicketIdReq) (resp *platform_configpb.GetTccConfigDiffByTicketIdResp, err error) {
	if err := t.ValidateRollbackTccConfigDiff(ctx, req); err != nil {
		return nil, err
	}
	resp = &platform_configpb.GetTccConfigDiffByTicketIdResp{}

	// 只有 发布总览回滚 diff & 新单回滚时才会读回滚工单
	ticketID := req.GetTicketId()
	isOverviewByNewTicket := req.GetIsDeployOverviewDiff() && req.GetRollbackType() == storage_sharedpb.TCCRollbackType_ROLLBACK_TYPE_NEW
	if isOverviewByNewTicket {
		ticketID = req.GetRollbackTicketId()
	}
	// deployment ticket info
	token, err := ctxutils.UserJwt(ctx)
	if err != nil {
		return nil, err
	}
	tccClient := t.TccClient.Get(req.GetControlPlane())
	ticketInfo, err := tccClient.GetDeploymentById(ctx, int64(ticketID), token)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[GetTccConfigRollbackDiff] ticketInfo %+v", ticketInfo)

	// get changes
	confChanges := ticketInfo.Data.ConfigChanges
	if confChanges == nil || len(confChanges) == 0 {
		logs.CtxInfo(ctx, "[GetTccConfigRollbackDiff] no config changes")
		return resp, nil
	}

	g := errgroup.Group{}
	g.SetLimit(10)
	mutex := sync.Mutex{}
	configDiffItems := make([]*platform_configpb.TccConfigDiffPairItem, 0)
	for _, c := range confChanges {
		confID := *c.ConfId
		rawToVersion := *c.ToVersion
		rawFromVersion := *c.FromVersion
		// 除新单回滚完成是直接读回滚工单 ToVersion, 回滚的 ToVersion 一定是正向工单的 FromVersion
		ToVersion := operatorx.IfThen(isOverviewByNewTicket, rawToVersion, rawFromVersion)

		g.Go(func() error {
			defer panicguard.Recover(ctx)
			var (
				fromConf *platform_configpb.TccConfigDiffInfo
				toConf   *platform_configpb.TccConfigDiffInfo
			)

			// from config
			if req.GetRollbackType() == storage_sharedpb.TCCRollbackType_ROLLBACK_TYPE_ORIGIN {
				//  原单回滚。发布工单的 to version
				if fromRes, err := tccClient.GetConfigVersionById(ctx, confID, rawToVersion, token); err != nil {
					return err
				} else {
					fromConf = model.MapConfigVersion2TccConfigDiffInfo(fromRes, req.GetControlPlane())
				}
			} else if req.GetIsDeployOverviewDiff() {
				// 新单回滚，发布总览回滚diff。回滚工单 from version
				if fromRes, err := tccClient.GetConfigVersionById(ctx, confID, rawFromVersion, token); err != nil {
					return err
				} else {
					fromConf = model.MapConfigVersion2TccConfigDiffInfo(fromRes, req.GetControlPlane())
				}
			} else {
				// 新单回滚，回滚前 diff。线上最新版本
				if fromRes, err := tccClient.GetConfigById(ctx, confID, token); err != nil {
					return err
				} else {
					fromConf = model.MapConfigLatestTccConfigInfo(fromRes, req.GetControlPlane(), model.TccVersionTypeOnline)
				}
			}

			//	to config
			if toRes, err := tccClient.GetConfigVersionById(ctx, confID, ToVersion, token); err != nil {
				return err
			} else {
				toConf = model.MapConfigVersion2TccConfigDiffInfo(toRes, req.GetControlPlane())
			}

			mutex.Lock()
			defer mutex.Unlock()
			configDiffItems = append(configDiffItems, &platform_configpb.TccConfigDiffPairItem{
				FromVersionConfigDiffInfo: fromConf,
				ToVersionConfigDiffInfo:   toConf,
			})

			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, bits_err.STORAGE.CallTccErr.AddError(err)
	}

	sort.Sort(model.DiffItems(configDiffItems))
	return &platform_configpb.GetTccConfigDiffByTicketIdResp{
		ConfigDiffItems: configDiffItems,
	}, nil
}
