package tcc_config

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/tcc_region"
	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/version_manager"
	"code.byted.org/devinfra/hagrid/pkg/tcc/apis"
)

func TestNewTccConfigSvcAutoGen(t *testing.T) {
	// Verify the normal initialization of NewTccConfigSvc.
	t.Run("testNewTccConfigSvc", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var tccClientWrapperMockPtrValue apis.TccClientWrapper
			tccClientWrapperMock := &tccClientWrapperMockPtrValue
			mockey.Mock(apis.NewTCCClientWrapper).Return(tccClientWrapperMock).Build()

			var tccKeyConfigSvcMockValueImpl mysqlTccKeyConfigSvcImplForTestAutoGen
			tccKeyConfigSvcMockValue := &tccKeyConfigSvcMockValueImpl
			tccKeyConfigSvcMock := tccKeyConfigSvcMockValue
			mockey.Mock(mysql.NewTccKeyConfigSvc, mockey.OptUnsafe).Return(tccKeyConfigSvcMock).Build()

			var tccChangeDaoSvcMockValueImpl mysqlTccChangeDaoSvcImplForTestAutoGen
			tccChangeDaoSvcMockValue := &tccChangeDaoSvcMockValueImpl
			tccChangeDaoSvcMock := tccChangeDaoSvcMockValue
			mockey.Mock(mysql.NewTccChangeDaoSvc, mockey.OptUnsafe).Return(tccChangeDaoSvcMock).Build()

			var storageVersionManagerSvcMockValueImpl version_managerStorageVersionManagerSvcImplForTestAutoGen
			storageVersionManagerSvcMockValue := &storageVersionManagerSvcMockValueImpl
			storageVersionManagerSvcMock := storageVersionManagerSvcMockValue
			mockey.Mock(version_manager.NewStorageVersionManagerSvc).Return(storageVersionManagerSvcMock).Build()

			var storagePsmVersionDaoSvcMockValueImpl mysqlStoragePsmVersionDaoSvcImplForTestAutoGen
			storagePsmVersionDaoSvcMockValue := &storagePsmVersionDaoSvcMockValueImpl
			storagePsmVersionDaoSvcMock := storagePsmVersionDaoSvcMockValue
			mockey.Mock(mysql.NewStoragePsmVersionDaoSvc, mockey.OptUnsafe).Return(storagePsmVersionDaoSvcMock).Build()

			var tccRegionSvcMockValueImpl tcc_regionTccRegionSvcImplForTestAutoGen
			tccRegionSvcMockValue := &tccRegionSvcMockValueImpl
			tccRegionSvcMock := tccRegionSvcMockValue
			mockey.Mock(tcc_region.NewTccRegionSvc).Return(tccRegionSvcMock).Build()


			// run target function and assert
			got1 := NewTccConfigSvc()
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

