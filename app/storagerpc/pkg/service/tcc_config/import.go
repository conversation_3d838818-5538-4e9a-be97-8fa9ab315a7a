package tcc_config

import (
	"context"

	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/version_manager"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	common_utils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pkg/tcc/apis"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/sharedpb"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
	"gorm.io/datatypes"
)

type MultiImportResp struct {
	ControlPlane sharedpb.TccControlPlane
	Res          apis.ImportConfigResp
}

type ChangeSource struct {
	SourceControlPlane sharedpb.TccControlPlane
	SourceRegion       string
	SourceDir          string
}

func (s tccConfigSvc) ImportMultiTccConfigs(ctx context.Context, req *platform_configpb.ImportMultiTccConfigsReq) (resp *platform_configpb.ImportMultiTccConfigsResp, err error) {
	// 0. 版本是否过期
	resp = &platform_configpb.ImportMultiTccConfigsResp{}
	isOutdated, versionRecord, err := s.storageVersionManagerSvc.IsStoragePsmVersionOutdated(ctx, version_manager.CheckPsmVersionOutdatedPayload{
		WorkflowType:     req.GetWorkflowType(),
		WorkflowUniqueId: int64(req.GetWorkflowUniqueId()),
		Psm:              req.GetPsm(),
		Version:          req.GetStoragePsmVersion(),
	})
	if err != nil {
		if bits_err.ToBizError(err).Code() != bits_err.COMMON.ErrRecordNotFound.Code() {
			return nil, err
		}
		// 兜底处理，如果psm版本记录没创建，导入时补齐
		versionRecord = &entity.StoragePsmVersion{
			WorkflowType:      req.GetWorkflowType().String(),
			WorkflowUniqueId:  int64(req.GetWorkflowUniqueId()),
			Psm:               req.GetPsm(),
			Creator:           cdauth.Username(ctx),
			Version:           1,
			TCCNamespaceIDMap: datatypes.NewJSONType(entity.DBTCCNamespaceIDMap{}),
		}
		if err := s.storagePsmVersionDaoSvc.Upsert(ctx, versionRecord, false); err != nil {
			return nil, err
		}
	} else if isOutdated {
		return nil, bits_err.STORAGE.PsmVersionOutdated.AppendExtra("psm", versionRecord.Psm)
	}

	// 1. 聚合请求参数，并发调用单来源导入方法 ImportTccConfig
	groupBySourceReq := map[ChangeSource][]*platform_configpb.ImportTccConfigItem{}
	for _, item := range req.GetItems() {
		curKey := ChangeSource{
			SourceControlPlane: item.GetSourceControlPlane(),
			SourceRegion:       item.GetSourceRegion(),
			SourceDir:          item.GetSourceDir(),
		}
		curConfig := &platform_configpb.ImportTccConfigItem{
			ConfName:        item.GetConfigItem().GetConfName(),
			SourceConfigId:  item.GetConfigItem().GetSourceConfigId(),
			BaselineVersion: item.GetConfigItem().GetBaselineVersion(),
		}
		if _, ok := groupBySourceReq[curKey]; ok {
			groupBySourceReq[curKey] = append(groupBySourceReq[curKey], curConfig)
		} else {
			groupBySourceReq[curKey] = []*platform_configpb.ImportTccConfigItem{
				curConfig,
			}
		}
	}
	singleImportReqList := gmap.ToSlice(groupBySourceReq, func(k ChangeSource, v []*platform_configpb.ImportTccConfigItem) *platform_configpb.ImportTccConfigReq {
		return &platform_configpb.ImportTccConfigReq{
			WorkflowType:       req.GetWorkflowType(),
			WorkflowUniqueId:   req.GetWorkflowUniqueId(),
			Psm:                req.GetPsm(),
			SourceControlPlane: k.SourceControlPlane,
			SourceRegion:       k.SourceRegion,
			SourceDir:          k.SourceDir,
			ConfigItems:        v,
		}
	})

	outputs, err := common_utils.MCall(ctx, singleImportReqList, func(ctx context.Context, input *platform_configpb.ImportTccConfigReq) (outputs []*platform_configpb.ImportTccChangeItem, err error) {
		var r *platform_configpb.ImportTccConfigResp
		if r, err = s.ImportTccConfig(ctx, input, true); err != nil || r == nil {
			logs.CtxError(ctx, "[ImportMultiTccConfigs] ImportTccConfig error: %+v", err)
			return outputs, err
		}
		logs.CtxInfo(ctx, "[ImportMultiTccConfigs] ImportTccConfig result:%s", common_utils.ToJson(r))
		outputs = gslice.Map(r.GetFailedConfigItems(), func(item *platform_configpb.ImportTccConfigItem) *platform_configpb.ImportTccChangeItem {
			return &platform_configpb.ImportTccChangeItem{
				SourceControlPlane: input.GetSourceControlPlane(),
				SourceRegion:       input.GetSourceRegion(),
				SourceDir:          input.GetSourceDir(),
				ConfigItem: &platform_configpb.ImportTccConfigItem{
					ConfName:        item.GetConfName(),
					SourceConfigId:  item.GetSourceConfigId(),
					BaselineVersion: item.GetBaselineVersion(),
				},
			}
		})
		return outputs, nil
	})
	if err != nil {
		return nil, err
	}
	resp.FailedItems = gslice.Flatten(outputs)

	// 查各个控制面的 ns_id，写入 DB。会并发调各控制面接口，观测成功率！！
	cpMap := gslice.GroupBy(req.GetItems(), func(r *platform_configpb.ImportTccChangeItem) sharedpb.TccControlPlane {
		return r.GetSourceControlPlane()
	})
	nsMap, err := s.storageVersionManagerSvc.GetNsIdMap(ctx, version_manager.NsIdMapPayload{
		Psm:           req.GetPsm(),
		ControlPlanes: gmap.Keys(cpMap),
		DBNsMap:       versionRecord.TCCNamespaceIDMap.Data(),
	})
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[ImportMultiTccConfigs] nsMap: %+v", nsMap)
	versionRecord.TCCNamespaceIDMap = datatypes.NewJSONType(nsMap)

	if err := s.storagePsmVersionDaoSvc.Upsert(ctx, versionRecord, true); err != nil {
		logs.CtxError(ctx, "[ImportMultiTccConfigs] Upsert ns_id_map error: %+v", err)
		return nil, err
	}

	return &platform_configpb.ImportMultiTccConfigsResp{}, nil
}
