package model

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/pkg/tcc/apis"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/sharedpb"
)

func TestDiffItems_SwapAutoGen(t *testing.T) {
	// Verify that Swap method panics when the receiver is empty.
	t.Run("testDiffItems_Swap_PanicOnEmptyReceiver", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiverAlias := []*platform_configpb.TccConfigDiffPairItem{}
			receiver := DiffItems(receiverAlias)
			var i int
			var j int
			convey.So(func() { receiver.Swap(i, j) }, convey.ShouldPanic)
		})
	})

}

func TestMapConfigVersion2TccConfigDiffInfoAutoGen(t *testing.T) {
	// Verify the function behavior under default conditions.
	t.Run("testMapConfigVersion2TccConfigDiffInfo_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getRegionNameRet1Mock string
			mockey.Mock(apis.GetRegionName).Return(getRegionNameRet1Mock).Build()

			// prepare parameters
			var configInfo apis.ConfigVersionResp
			var controlPlane sharedpb.TccControlPlane

			// run target function and assert
			got1 := MapConfigVersion2TccConfigDiffInfo(configInfo, controlPlane)
			convey.So((*got1).Version, convey.ShouldEqual, 0)
			convey.So((*got1).Psm, convey.ShouldBeBlank)
			convey.So((*got1).ConfigName, convey.ShouldBeBlank)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).ConfigId, convey.ShouldEqual, 0)
			convey.So((*got1).Detail == nil, convey.ShouldBeFalse)
			convey.So((*got1).RegionInfo == nil, convey.ShouldBeFalse)
			convey.So(int32((*got1).ControlPlane), convey.ShouldEqual, 0)
			convey.So((*got1).DirInfo == nil, convey.ShouldBeFalse)
			convey.So((*got1).Content, convey.ShouldBeBlank)
		})
	})

}

func TestMapConfigLatestTccConfigInfoAutoGen(t *testing.T) {
	// Verify the function behavior when versionType is not online.
	t.Run("testMapConfigLatestTccConfigInfo_VersionTypeNotOnline", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getRegionNameRet1Mock string
			mockey.Mock(apis.GetRegionName).Return(getRegionNameRet1Mock).Build()

			// prepare parameters
			var configInfo apis.ConfigResp
			var controlPlane sharedpb.TccControlPlane
			versionTypeAlias := "not online"
			versionType := TccVersionType(versionTypeAlias)

			// run target function and assert
			got1 := MapConfigLatestTccConfigInfo(configInfo, controlPlane, versionType)
			convey.So((*got1).Version, convey.ShouldEqual, 0)
			convey.So((*got1).DirInfo == nil, convey.ShouldBeFalse)
			convey.So((*got1).Content, convey.ShouldBeBlank)
			convey.So((*got1).Detail == nil, convey.ShouldBeFalse)
			convey.So((*got1).ConfigName, convey.ShouldBeBlank)
			convey.So((*got1).RegionInfo == nil, convey.ShouldBeFalse)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).ConfigId, convey.ShouldEqual, 0)
			convey.So((*got1).Psm, convey.ShouldBeBlank)
			convey.So(int32((*got1).ControlPlane), convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when versionType is online.
	t.Run("testMapConfigLatestTccConfigInfo_VersionTypeOnline", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getRegionNameRet1Mock string
			mockey.Mock(apis.GetRegionName).Return(getRegionNameRet1Mock).Build()

			// prepare parameters
			var controlPlane sharedpb.TccControlPlane
			versionTypeAlias := "online"
			versionType := TccVersionType(versionTypeAlias)
			var configInfo apis.ConfigResp

			// run target function and assert
			got1 := MapConfigLatestTccConfigInfo(configInfo, controlPlane, versionType)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).DirInfo == nil, convey.ShouldBeFalse)
			convey.So((*got1).Psm, convey.ShouldBeBlank)
			convey.So((*got1).Detail == nil, convey.ShouldBeFalse)
			convey.So((*got1).ConfigName, convey.ShouldBeBlank)
			convey.So((*got1).RegionInfo == nil, convey.ShouldBeFalse)
			convey.So(int32((*got1).ControlPlane), convey.ShouldEqual, 0)
			convey.So((*got1).ConfigId, convey.ShouldEqual, 0)
			convey.So((*got1).Content, convey.ShouldBeBlank)
			convey.So((*got1).Version, convey.ShouldEqual, 0)
		})
	})

}

func TestMapTccConfigInfoAutoGen(t *testing.T) {
	// Verify the function when version type is not online.
	t.Run("testMapTccConfigInfo_VersionTypeNotOnline", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getRegionNameRet1Mock string
			mockey.Mock(apis.GetRegionName).Return(getRegionNameRet1Mock).Build()

			// prepare parameters
			var controlPlane sharedpb.TccControlPlane
			versionTypeAlias := "not online"
			versionType := TccVersionType(versionTypeAlias)
			var config apis.ConfigInfo

			// run target function and assert
			got1 := MapTccConfigInfo(config, controlPlane, versionType)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).VersionInfo == nil, convey.ShouldBeFalse)
			convey.So((*got1).BaseInfo == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the function when version type is online.
	t.Run("testMapTccConfigInfo_VersionTypeOnline", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getRegionNameRet1Mock string
			mockey.Mock(apis.GetRegionName).Return(getRegionNameRet1Mock).Build()

			// prepare parameters
			var config apis.ConfigInfo
			var controlPlane sharedpb.TccControlPlane
			versionTypeAlias := "online"
			versionType := TccVersionType(versionTypeAlias)

			// run target function and assert
			got1 := MapTccConfigInfo(config, controlPlane, versionType)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).VersionInfo == nil, convey.ShouldBeFalse)
			convey.So((*got1).BaseInfo == nil, convey.ShouldBeFalse)
		})
	})

}

func TestMapTccConfigDiffBasicInfoAutoGen(t *testing.T) {
	// Verify the function behavior under default conditions.
	t.Run("testMapTccConfigDiffBasicInfo_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getRegionNameRet1Mock string
			mockey.Mock(apis.GetRegionName).Return(getRegionNameRet1Mock).Build()

			// prepare parameters
			var controlPlane sharedpb.TccControlPlane
			var region string
			var dir string
			var confName string

			// run target function and assert
			got1 := MapTccConfigDiffBasicInfo(controlPlane, region, dir, confName)
			convey.So((*got1).Version, convey.ShouldEqual, 0)
			convey.So((*got1).Content, convey.ShouldBeBlank)
			convey.So((*got1).Psm, convey.ShouldBeBlank)
			convey.So((*got1).Detail == nil, convey.ShouldBeTrue)
			convey.So((*got1).DirInfo == nil, convey.ShouldBeFalse)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).ConfigName, convey.ShouldBeBlank)
			convey.So((*got1).RegionInfo == nil, convey.ShouldBeFalse)
			convey.So((*got1).ConfigId, convey.ShouldEqual, 0)
			convey.So(int32((*got1).ControlPlane), convey.ShouldEqual, 0)
		})
	})

}

func TestDiffItems_LenAutoGen(t *testing.T) {
	// Verify the Len function under default condition.
	t.Run("testDiffItems_Len_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiverAlias := []*platform_configpb.TccConfigDiffPairItem{}
			receiver := DiffItems(receiverAlias)

			// run target function and assert
			got1 := receiver.Len()
			convey.So(got1, convey.ShouldEqual, 0)
		})
	})

}

func TestDiffItems_LessAutoGen(t *testing.T) {
	// Verify the Less function behavior under default condition.
	t.Run("testDiffItems_LessAutoGen", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var i int
			var j int
			receiverAlias := []*platform_configpb.TccConfigDiffPairItem{}
			receiver := DiffItems(receiverAlias)
			convey.So(func() { _ = receiver.Less(i, j) }, convey.ShouldPanic)
		})
	})

}

