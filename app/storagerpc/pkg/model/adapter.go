package model

import (
	"code.byted.org/devinfra/hagrid/pkg/tcc/apis"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/sharedpb"
	"code.byted.org/gopkg/lang/v2/operatorx"
	"code.byted.org/lang/gg/gslice"
)

type TccVersionType string

const (
	// 线上版本
	TccVersionTypeOnline TccVersionType = "online"
	// 最新版本
	TccVersionTypeLatest TccVersionType = "latest"
)

// 指定版本
func MapConfigVersion2TccConfigDiffInfo(configInfo apis.ConfigVersionResp, controlPlane sharedpb.TccControlPlane) *platform_configpb.TccConfigDiffInfo {
	return &platform_configpb.TccConfigDiffInfo{
		ConfigId:     configInfo.Meta.Id,
		ConfigName:   configInfo.Meta.ConfName,
		ControlPlane: controlPlane,
		RegionInfo: &sharedpb.RegionInfo{
			Region:           configInfo.Meta.Region,
			RegionName:       apis.GetRegionName(configInfo.Meta.Region),
			MigrateRegionTag: operatorx.IfThen(apis.IsSinfRegion(configInfo.Meta.Region), sharedpb.MigrateRegionTag_MIGRATE_REGION_TAG_SINF, sharedpb.MigrateRegionTag_MIGRATE_REGION_TAG_UNSPECIFIED),
		},
		DirInfo: &sharedpb.DirInfo{
			Dir:     configInfo.Meta.DirInfo.DirPath,
			DirName: configInfo.Meta.DirInfo.DirName,
			DirId:   configInfo.Meta.DirInfo.DirId,
		},
		Version: configInfo.Data.Version,
		Content: configInfo.Data.Base,
		Psm:     configInfo.Meta.Namespace.Name,
		Detail: &platform_configpb.CreateTccConfigInfo{
			ConfigType: configInfo.Data.ConfigType,
			DataType:   configInfo.Data.DataType,
			Content:    configInfo.Data.Base,
			SchemaInfo: &platform_configpb.TccSchemaInfo{
				Id:   int64(configInfo.Data.SchemaInfo.Id),
				Name: configInfo.Data.SchemaInfo.Name,
			},
			ValidatorInfos: gslice.Map(configInfo.Data.ValidatorInfos, func(v apis.ValidatorInfo) *platform_configpb.TccValidatorItem {
				return &platform_configpb.TccValidatorItem{
					Id:   v.Id,
					Name: v.Name,
				}
			}),
			CreatedBy:        configInfo.Data.CreatedBy,
			EnableEncryption: configInfo.Data.EnableEncryption,
			ClearValue:       configInfo.Data.ClearValue,
			Tags:             configInfo.Data.Tags,
		},
	}
}

// 最新版本
func MapConfigLatestTccConfigInfo(configInfo apis.ConfigResp, controlPlane sharedpb.TccControlPlane, versionType TccVersionType) *platform_configpb.TccConfigDiffInfo {
	var data apis.CoreConfigInfo
	if versionType == TccVersionTypeOnline {
		data = configInfo.Data.OnlineVersionData
	} else {
		data = configInfo.Data.LatestVersionData
	}

	return &platform_configpb.TccConfigDiffInfo{
		ConfigId:     configInfo.Data.Id,
		ConfigName:   configInfo.Data.ConfName,
		ControlPlane: controlPlane,
		RegionInfo: &sharedpb.RegionInfo{
			Region:           configInfo.Data.Region,
			RegionName:       apis.GetRegionName(configInfo.Data.Region),
			MigrateRegionTag: operatorx.IfThen(apis.IsSinfRegion(configInfo.Data.Region), sharedpb.MigrateRegionTag_MIGRATE_REGION_TAG_SINF, sharedpb.MigrateRegionTag_MIGRATE_REGION_TAG_UNSPECIFIED),
		},
		DirInfo: &sharedpb.DirInfo{
			Dir:     configInfo.Data.DirInfo.DirPath,
			DirName: configInfo.Data.DirInfo.DirName,
			DirId:   configInfo.Data.DirInfo.DirId,
		},
		Version: data.Version,
		Content: data.Base,
		Psm:     configInfo.Data.Namespace.Name,
		Detail: &platform_configpb.CreateTccConfigInfo{
			ConfigType: data.ConfigType,
			DataType:   data.DataType,
			Content:    data.Base,
			SchemaInfo: &platform_configpb.TccSchemaInfo{
				Id:   int64(data.SchemaInfo.Id),
				Name: data.SchemaInfo.Name,
			},
			ValidatorInfos: gslice.Map(data.ValidatorInfos, func(v apis.ValidatorInfo) *platform_configpb.TccValidatorItem {
				return &platform_configpb.TccValidatorItem{
					Id:   v.Id,
					Name: v.Name,
				}
			}),
			CreatedBy:        data.CreatedBy,
			EnableEncryption: data.EnableEncryption,
			ClearValue:       data.ClearValue,
			Tags:             data.Tags,
		},
	}
}

// 获取配置详情的最新/线上版本
func MapTccConfigInfo(config apis.ConfigInfo, controlPlane sharedpb.TccControlPlane, versionType TccVersionType) *platform_configpb.TccConfigInfo {
	var version int64
	var versionInfo *platform_configpb.TccVersionInfo
	if versionType == TccVersionTypeOnline {
		version = config.OnlineVersion
		versionInfo = &platform_configpb.TccVersionInfo{
			ConfigType: config.OnlineVersionData.DataType,
			DataType:   config.OnlineVersionData.DataType,
			Version:    config.OnlineVersionData.Version,
			Content:    config.OnlineVersionData.Base,
			Schema: &platform_configpb.TccSchemaInfo{
				Id:   config.BindSchema.Id,
				Name: config.BindSchema.Name,
			},
		}
	} else {
		version = config.LatestVersion
		versionInfo = &platform_configpb.TccVersionInfo{
			ConfigType: config.LatestVersionData.ConfigType,
			DataType:   config.LatestVersionData.DataType,
			Version:    config.LatestVersionData.Version,
			Content:    config.LatestVersionData.Base,
			Schema: &platform_configpb.TccSchemaInfo{
				Id:   config.BindSchema.Id,
				Name: config.BindSchema.Name,
			},
		}
	}

	return &platform_configpb.TccConfigInfo{
		BaseInfo: &platform_configpb.TccConfigItem{
			ConfigId:     config.Id,
			ConfName:     config.ConfName,
			Description:  config.Description,
			ControlPlane: controlPlane,
			RegionInfo: &sharedpb.RegionInfo{
				Region:           config.Region,
				RegionName:       apis.GetRegionName(config.Region),
				MigrateRegionTag: operatorx.IfThen(apis.IsSinfRegion(config.Region), sharedpb.MigrateRegionTag_MIGRATE_REGION_TAG_SINF, sharedpb.MigrateRegionTag_MIGRATE_REGION_TAG_UNSPECIFIED),
			},
			DirInfo: &sharedpb.DirInfo{
				Dir:     config.DirInfo.DirPath,
				DirName: config.DirInfo.DirName,
				DirId:   config.DirInfo.DirId,
			},
			Tags:         config.Tags,
			WithL4Data:   config.WithL4Data,
			CdnSupported: config.CdnSupported,
			Version:      version,
		},
		VersionInfo: versionInfo,
	}
}

func MapTccConfigDiffBasicInfo(controlPlane sharedpb.TccControlPlane, region string, dir string, confName string) *platform_configpb.TccConfigDiffInfo {
	return &platform_configpb.TccConfigDiffInfo{
		ControlPlane: controlPlane,
		RegionInfo: &sharedpb.RegionInfo{
			Region:           region,
			RegionName:       apis.GetRegionName(region),
			MigrateRegionTag: operatorx.IfThen(apis.IsSinfRegion(region), sharedpb.MigrateRegionTag_MIGRATE_REGION_TAG_SINF, sharedpb.MigrateRegionTag_MIGRATE_REGION_TAG_UNSPECIFIED),
		},
		DirInfo:    &sharedpb.DirInfo{Dir: dir, DirName: dir},
		ConfigName: confName,
	}
}

type DiffItems []*platform_configpb.TccConfigDiffPairItem

func (p DiffItems) Len() int { return len(p) }
func (p DiffItems) Less(i, j int) bool {
	if p[i].FromVersionConfigDiffInfo == nil || p[j].FromVersionConfigDiffInfo == nil {
		return true
	}
	return p[i].FromVersionConfigDiffInfo.ConfigName < p[j].FromVersionConfigDiffInfo.ConfigName
}
func (p DiffItems) Swap(i, j int) { p[i], p[j] = p[j], p[i] }
