package recall_log

import (
	"context"
	"testing"

	"code.byted.org/gopkg/logs"
	jsoniter "github.com/json-iterator/go"

	"code.byted.org/devinfra/hagrid/pkg/net/httpclient"
)

// import (
//	"context"
//	"testing"
// )
//
// func Test_client_GetEnhanceLog(t *testing.T) {
//	newClient := NewClient()
//	logNums, err := newClient.GetEnhanceLog(context.Background(), 1090946203, 1, "test_execution_and_reporting",
//		[]uint64{7, 9, 14, 15, 32, 36, 48, 121, 122, 123, 124, 125, 126, 127, 140, 141, 142, 143, 144, 145, 146, 150, 151, 152, 153, 154, 155, 156, 178, 179, 180, 181, 182, 183, 184, 272, 273, 274, 275, 276, 277, 278, 360, 361, 378, 401, 402, 404, 436, 455, 457, 464, 467, 486, 489, 508, 522, 525, 532, 533, 540, 565, 584, 586, 593, 596, 615, 618, 637, 651, 654, 661, 662, 665, 666, 667, 668, 669, 1110, 1188, 1368, 1622, 2001, 2963, 2964, 3136, 3137, 3191, 3192, 3289, 3290, 3315, 3320, 3321, 3322, 3323, 3325, 3326, 3328, 3329, 3351, 3355, 3359, 3361, 3385, 3387})
//	if err != nil {
//		t.Errorf("GetEnhanceLog() error = %v", err)
//		return
//	}
//	t.Logf("log nums: %v", logNums)
// }

func Test_client_GetRecallTool(t *testing.T) {
	ctx := context.Background()
	type fields struct {
		recallClient *httpclient.BytedHttpClient
	}
	type args struct {
		name string
		req  *RecallToolRequest
	}
	tests := []args{
		// TODO: Add test cases.
		{
			name:    "缓存case",
			req: &RecallToolRequest{
				RootCause:   []string{
					"[可能性较大] `bytedagi/client/llmserver.py`文件代码格式不符合`black`规范，导致代码规范检查失败。 ",
					"使用`black`格式化代码\n根据错误日志，`black`检测到`bytedagi/client/llmserver.py`文件代码格式不符合规范，需要进行格式化。可以使用以下命令格式化代码：\n```bash\nblack bytedagi/client/llmserver.py\n```\n建议在本地进行代码格式化，并提交修改后的代码。你可以通过文档 [为什么我的流水线运行失败，错误日志显示代码库中存在未提交的修改，这是怎么回事？如何解决这个问题？](urls) 了解更多在这方面的信息。",
					"2. 配置`black`忽略该文件\n如果确认`bytedagi/client/llmserver.py`文件不需要进行格式化，可以配置`black`忽略该文件。你可以在`pyproject.toml`文件中添加如下配置：\n```toml\n[tool.black]\nexclude = '''\n/(\n    .*migrations.*\n  | bytedagi/client/llmserver.py\n)/\n'''\n```\n建议仔细评估忽略文件的风险，确保忽略的文件不会影响代码质量。你可以通过文档 [ci如何配置c++代码风格检查](urls) 了解更多在这方面的信息。",
					"3. 禁用`black`代码规范检查\n如果希望暂时禁用`black`代码规范检查，可以修改CI配置文件，注释掉或删除相关的代码规范检查步骤。\n```yaml\n# steps:\n#  - name: 代码规范检查\n#    uses: actions/black@v3\n```\n建议不要长期禁用代码规范检查，代码规范检查有助于提高代码质量。你可以通过文档 [CI失败](urls) 了解更多在这方面的信息。",
				},
				StepCommand: []string{
					"export PATH=/root/.local/bin:$PATH",
					"make lint",
				},
				Action:      nil,
				Solution:    "\n#### 问题：`bytedagi/client/llmserver.py`文件代码格式不符合`black`规范，导致代码规范检查失败。\n##### 1. 使用`black`格式化代码\n根据错误日志，`black`检测到`bytedagi/client/llmserver.py`文件代码格式不符合规范，需要进行格式化。可以使用以下命令格式化代码：\n```bash\nblack bytedagi/client/llmserver.py\n```\n建议在本地进行代码格式化，并提交修改后的代码。你可以通过文档 [为什么我的流水线运行失败，错误日志显示代码库中存在未提交的修改，这是怎么回事？如何解决这个问题？](https://oncall.bytedance.net/chats/user/userCase?id=54758357&picked_detail=54758357) 了解更多在这方面的信息。 \n##### 2. 配置`black`忽略该文件\n如果确认`bytedagi/client/llmserver.py`文件不需要进行格式化，可以配置`black`忽略该文件。你可以在`pyproject.toml`文件中添加如下配置：\n```toml\n[tool.black]\nexclude = '''\n/(\n    .*migrations.*\n  | bytedagi/client/llmserver.py\n)/\n'''\n```\n建议仔细评估忽略文件的风险，确保忽略的文件不会影响代码质量。你可以通过文档 [ci如何配置c++代码风格检查](https://oncall.bytedance.net/chats/user/userCase?id=50099799&picked_detail=50099799) 了解更多在这方面的信息。 \n##### 3. 禁用`black`代码规范检查\n如果希望暂时禁用`black`代码规范检查，可以修改CI配置文件，注释掉或删除相关的代码规范检查步骤。\n```yaml\n# steps:\n#  - name: 代码规范检查\n#    uses: actions/black@v3\n```\n建议不要长期禁用代码规范检查，代码规范检查有助于提高代码质量。你可以通过文档 [CI失败](https://cloud.bytedance.net/developer/vocoders/detail/16a424b4-0ef5-4a55-9265-15d902f2332d) 了解更多在这方面的信息。 ",
			},
		},
	}
	defer logs.Flush()
	simpleJson := func(v interface{}) string {
		toString, _ := jsoniter.MarshalToString(&v)
		return toString
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewClient()
			logs.CtxInfo(ctx, "[Test_client_GetRecallTool] name: %v", tt.name)
			got, err := c.GetRecallTool(ctx, tt.req)
			if err != nil {
				logs.CtxError(ctx, "[Test_client_GetRecallTool] name: %v error: %+v", tt.name, err)
			}
			logs.CtxInfo(ctx, "[Test_client_GetRecallTool] got: %v", simpleJson(&got))
		})
	}
}

func Test_client_ExtractLintFixParams(t *testing.T) {
	ctx := context.Background()
	type args struct {
		name string
		req  *ExtractLintFixParamsReq
	}
	tests := []args{
		{
			name: "test1",
			req: &ExtractLintFixParamsReq{
				RootCause:    "### 👀 诊断过程\n#### 任务类型：\n执行代码扫描\n#### 错误日志分析：\n1. 日志行 18-22 说明在`/home/<USER>/packages/studio/social-scene/src/pages/detail-page/components/develop/edit-host-modal/index.tsx`文件中，`useCallback`定义了但是从未使用，导致代码扫描失败。\n\n### 🎯 错误原因\n[可能性较大] 代码扫描阶段发现`/home/<USER>/packages/studio/social-scene/src/pages/detail-page/components/develop/edit-host-modal/index.tsx`文件中存在`useCallback`定义了但是从未使用的错误，导致代码扫描失败。\n\n",
				SeqID:        0,
				JobRunID:     1111764577,
				OrcaStepName: "run_all-step_9",
			},
		},
	}
	defer logs.Flush()
	simpleJson := func(v interface{}) string {
		toString, _ := jsoniter.MarshalToString(&v)
		return toString
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewClient()
			got, err := c.ExtractLintFixParams(ctx, tt.req)
			if err != nil {
				logs.CtxError(ctx, "[Test_client_GetRecallTool] name: %v error: %+v", tt.name, err)
			}
			logs.CtxInfo(ctx, "[Test_client_GetRecallTool] got: %v", simpleJson(&got))
		})
	}
}
