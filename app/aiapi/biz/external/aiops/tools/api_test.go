package tools

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/devinfra/hagrid/pkg/net/httpclient"
)

func Test_client_RunLintFixTool(t *testing.T) {
	type fields struct {
		httpclient *httpclient.BytedHttpClient
	}
	type args struct {
		ctx context.Context
		req *LintFixRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *LintFixResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &client{
				httpclient: tt.fields.httpclient,
			}
			got, err := c.RunLintFixTool(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RunLintFixTool() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Printf("got: %+v", got)
		})
	}
}