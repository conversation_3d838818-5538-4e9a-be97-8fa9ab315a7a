package devgpt

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"code.byted.org/middleware/hertz/byted/middlewares/client/bytedtrace"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/paas/cloud-sdk-go/jwt"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/model"
	"code.byted.org/devinfra/hagrid/libs/hertz"
	"code.byted.org/devinfra/hagrid/libs/stream"
	"code.byted.org/devinfra/hagrid/pkg/net/httpclient"
)
type ChatMessageRecordStream = *stream.RecvChannel[*ChatMessageRecordEvent]
// reference:https://bytedance.larkoffice.com/docx/K30CdP3iEo7SRixlVZ8c4W3snkf
//go:generate go run go.uber.org/mock/mockgen -source api.go -destination mock/devgpt_mock_gen.go -package devgptmock . APIIFace
type APIIFace interface {
	CreateChatRecord(ctx context.Context, appID string, token string, preSets map[string]string, userContent string) (*ChatRecord, error)
	CreateAndSendMessage(ctx context.Context, appID string, token string, preSets map[string]string, userContent string) (answer string, chatID string, messageID string, err error)
	CreateAndSendMessageStream(ctx context.Context, appID string, token string, preSets map[string]string, userContent string) (ChatMessageRecordStream, error)
	SendMessageStreamWithConversationID(ctx context.Context, appID, token, userContent, conversationID string) (ChatMessageRecordStream, error)
}

type apiClient struct {
	httpclient *httpclient.BytedHttpClient
	//todo 合并 http client 与 stream client
	streamClient *hertz.Client
	mu           sync.Mutex
}

var _ APIIFace = &apiClient{}

func NewClient() APIIFace {
	cli := httpclient.MustNewBytedHttpClient().
		SetRetryCount(1).
		SetBaseURL("https://chatverse.bytedance.net").
		SetTimeout(10 * 60 * time.Second).
		DisableRequestLogging()

	streamCli, err := hertz.NewClient("https://chatverse.bytedance.net", hertz.NewHTTPClientOption{
		EnableStream: true,
		Timeout:      time.Minute * 10,
	})
	if err != nil {
		panic(errors.Errorf("failed to init devgpt client, error: %s", err.Error()))
	}
	return &apiClient{
		httpclient:   cli,
		streamClient: streamCli,
		mu:           sync.Mutex{},
	}
}

func (c *apiClient) newRequest(ctx context.Context, token string) (*httpclient.Request, error) {
	if len(token) != 0 {
		c.mu.Lock()
		request := c.httpclient.
			SetHeader(model.XJwtToken, token).
			R().
			SetContext(ctx)
		c.mu.Unlock()
		return request, nil
	}
	serviceAccountSecret, err := tcc.GetServiceAccountSecret(ctx)
	if err != nil {
		return nil, err
	}
	// 最佳使用姿势
	genBest := jwt.NewGenerator(jwt.WithRegion(jwt.RegionCN))
	jwtStr, err := genBest.Generate(context.Background(), serviceAccountSecret)
	if err != nil {
		return nil, err
	}

	c.mu.Lock()
	request := c.httpclient.
		SetHeader(model.XJwtToken, jwtStr).
		R().
		SetContext(ctx)
	c.mu.Unlock()
	return request, nil
}
func (c *apiClient) CreateChatRecord(ctx context.Context, appID string, token string, preSets map[string]string, userContent string) (*ChatRecord, error) {
	// call 1 /openapi/v2/apps/a_6560683542e09e929780091c/chat_records
	chatRequest, err := c.newRequest(ctx, token)
	if err != nil {
		return nil, err
	}
	chatRecordResponse := &ChatRecordResponse{}

	chatRecordURL := fmt.Sprintf("/openapi/v4/apps/%s/chat_records", appID)
	chatBody := &ChatRecordRequest{}
	if preSets != nil {
		if data, err := json.Marshal(preSets); err == nil {
			chatBody.Presets = string(data)
		}
	}
	resp, err := chatRequest.
		SetBody(chatBody).
		SetResult(chatRecordResponse).
		Post(chatRecordURL)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != 200 || chatRecordResponse.Code != "Success" {
		return nil, err
	}
	return chatRecordResponse.Data.ChatRecord, nil
}
func (c *apiClient) CreateAndSendMessage(ctx context.Context, appID string, token string, preSets map[string]string, userContent string) (answer string, chatID string, messageID string, err error) {
	chatRecord, err := c.CreateChatRecord(ctx, appID, "", preSets, userContent)
	if err != nil {
		return "", "", "", err
	}

	if chatRecord.ChatRecordId == "" {
		return "", "", "", errors.Errorf("failed to create chat record, error: %v", chatRecord)
	}

	// call  /openapi/v2/apps/:appId/chat_records/:chatRecordId/chat_message_records
	ctx = bytedtrace.WithToMethod(ctx, appID)
	chatMessageRequest, err := c.newRequest(ctx, token)
	if err != nil {
		return "", "", "", err
	}

	messageBody := &ChatMessageRecordRequest{
		Type:    "json",
		Content: userContent,
		Role:    "user",
	}

	chatMessageResponse := &ChatMessageRecordResponse{}
	chatMessageURL := fmt.Sprintf("/openapi/v2/apps/%s/chat_records/%s/chat_message_records", appID,
		chatRecord.ChatRecordId)
	resp, err := chatMessageRequest.
		SetBody(messageBody).
		SetResult(chatMessageResponse).
		Post(chatMessageURL)
	if err != nil {
		return "", "", "", err
	}

	if resp.StatusCode() != 200 || chatMessageResponse.Code != "Success" {
		return "", "", "", errors.Errorf("resp status code is not 200,code:%d", resp.StatusCode())
	}

	return chatMessageResponse.Data.Content, chatRecord.ChatRecordId, chatMessageResponse.Data.ChatMessageRecordId, nil
}

func (c *apiClient) CreateAndSendMessageStream(ctx context.Context, appID string, token string, preSets map[string]string, userContent string) (*stream.RecvChannel[*ChatMessageRecordEvent], error) {
	chatRecord, err := c.CreateChatRecord(ctx, appID, "", preSets, userContent)
	if err != nil {
		return nil, err
	}

	if chatRecord.ChatRecordId == "" {
		return nil, errors.Errorf("failed to create chat record, error: %v", chatRecord)
	}

	// call 2 /openapi/v4/apps/:appId/chat_records/:chatRecordId/chat_message_records
	return c._sendMessageStream(ctx, appID, token, chatRecord.ChatRecordId, userContent)
}

func (c *apiClient) SendMessageStreamWithConversationID(ctx context.Context, appID, token, userContent, conversationID string) (*stream.RecvChannel[*ChatMessageRecordEvent], error) {
	// call 2 /openapi/v4/apps/:appId/chat_records/:chatRecordId/chat_message_records
	return c._sendMessageStream(ctx, appID, token, conversationID, userContent)
}

func (c *apiClient) _sendMessageStream(ctx context.Context, appID, token, conversationID,
	userContent string) (*stream.RecvChannel[*ChatMessageRecordEvent], error) {
	var jwtStr string
	if len(token) > 0 {
		jwtStr = token
	} else {
		serviceAccountSecret, err := tcc.GetServiceAccountSecret(ctx)
		if err != nil {
			return nil, err
		}
		// 最佳使用姿势
		genBest := jwt.NewGenerator(jwt.WithRegion(jwt.RegionCN))
		jwtStr, err = genBest.Generate(context.Background(), serviceAccountSecret)
		if err != nil {
			return nil, err
		}
	}
	headers := map[string]string{
		"X-JWT-TOKEN": jwtStr,
	}

	content := map[string]string{
		"content": userContent,
	}
	raw, _ := jsoniter.MarshalToString(content)

	messageBody := &ChatMessageRecordRequest{
		Type:    "json",
		Content: raw,
		Role:    "user",
	}

	ctx = bytedtrace.WithToMethod(ctx, appID)
	chatMessageURL := fmt.Sprintf("/openapi/v4/apps/%s/chat_records/%s/chat_message_records", appID,
		conversationID)

	httpSend, httpRecv := stream.NewChannel[*hertz.Event](10)
	chatMsgSend, chatMsgRecv := stream.NewChannel[*ChatMessageRecordEvent](10)
	_, err := c.streamClient.DoJSONReq(ctx, http.MethodPost, chatMessageURL, hertz.ReqOption{
		ExpectedCode:       http.StatusOK,
		Body:               messageBody,
		Result:             httpSend,
		Headers:            headers,
		SetConnectionClose: false,
		Timeout:            time.Minute * 10,
		ResponseHeaders:    nil,
	})
	if err != nil {
		return nil, err
	}
	go stream.Forward(ctx, httpRecv, chatMsgSend, func(item *hertz.Event) *ChatMessageRecordEvent {
		record := &ChatMessageRecord{}
		if err := json.Unmarshal(item.Data, record); err != nil {
			logs.CtxError(ctx, "failed to unmarshal chat message record: %s", err.Error())
		}
		return &ChatMessageRecordEvent{
			ID:                item.ID,
			Event:             ChatMessageRecordEventType(item.Event),
			ChatMessageRecord: record,
		}
	})
	return chatMsgRecv, nil

}
