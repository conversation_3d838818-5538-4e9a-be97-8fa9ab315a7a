package custom_model

import (
	"context"
	"net/http"
	"strings"
	"time"

	"code.byted.org/gopkg/logs"
	json "github.com/bytedance/sonic"

	"code.byted.org/devinfra/hagrid/libs/hertz"
	"code.byted.org/devinfra/hagrid/libs/stream"
)

//go:generate mockgen -destination mock/mockgen.go -package mock . APIIFace
type APIIFace interface {
	Chat(ctx context.Context, path, content string) (string, error)
	ChatStream(ctx context.Context, path, content string) (*stream.RecvChannel[*MessageChunk], error)
}

type client struct {
	httpClient *hertz.Client
}

var _ APIIFace = &client{}

func NewClient() APIIFace {
	httpClient, err := hertz.NewClient("http://bitsai-code-llm.bytedance.net/", hertz.NewHTTPClientOption{
		EnableStream: true,
		Timeout:      time.Minute * 10,
	})
	if err != nil {
		panic(err)
	}
	return &client{
		httpClient: httpClient,
	}
}

func (c *client) ChatStream(ctx context.Context, path, content string) (*stream.RecvChannel[*MessageChunk], error) {
	if strings.HasPrefix(path, "/") {
		path = path[1:]
	}

	reqBody := &llmRequest{
		Model: "dsv2",
		Messages: []struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		}{
			{
				Role:    "user",
				Content: content,
			},
		},
		Stream: true,
	}

	httpSend, httpRecv := stream.NewChannel[*hertz.Event](10)
	chatMsgSend, chatMsgRecv := stream.NewChannel[*MessageChunk](10)

	_, err := c.httpClient.DoJSONReq(ctx, http.MethodPost, path, hertz.ReqOption{
		ExpectedCode:       http.StatusOK,
		Body:               reqBody,
		Result:             httpSend,
		SetConnectionClose: false,
		ResponseHeaders:    nil,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	})
	if err != nil {
		return nil, err
	}
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logs.CtxError(ctx, "panic: %v", r)
			}
		}()
		err := stream.Forward(ctx, httpRecv, chatMsgSend, func(item *hertz.Event) *MessageChunk {
			data := &MessageChunk{}
			if strings.TrimSpace(string(item.Data)) == "[DONE]" {
				return nil
			}
			if err := json.Unmarshal(item.Data, data); err != nil {
				logs.CtxError(ctx, "failed to unmarshal chat message record: %s", err.Error())
			}
			return data
		})
		if err != nil {
			logs.CtxError(ctx, "failed to forward chat message record: %s", err.Error())
		}
	}()

	return chatMsgRecv, nil
}

func (c *client) Chat(ctx context.Context, path, content string) (string, error) {
	if strings.HasPrefix(path, "/") {
		path = path[1:]
	}

	reqBody := &llmRequest{
		Model: "dsv2",
		Messages: []struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		}{
			{
				Role:    "user",
				Content: content,
			},
		},
		Stream: false,
	}

	res := &MessageChunk{}
	_, err := c.httpClient.DoJSONReq(ctx, http.MethodPost, path, hertz.ReqOption{
		ExpectedCode:       http.StatusOK,
		Body:               reqBody,
		Result:             res,
		SetConnectionClose: false,
		ResponseHeaders:    nil,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	})
	if err!= nil {
		return "", err
	}

	if len(res.Choices) == 0 || res.Choices[0].Delta == nil {
		logs.CtxWarn(ctx, "empty choices")
		return "", nil
	}
	return res.Choices[0].Delta.Content, nil
}
