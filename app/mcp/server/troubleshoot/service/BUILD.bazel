load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = ["troubleshoot.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/mcp/server/troubleshoot/service",
    visibility = ["//visibility:public"],
    deps = [
        "//app/mcp/pkg/auth",
        "//app/mcp/pkg/bits",
        "//app/mcp/server/troubleshoot/model/tool",
        "//idls/byted/devinfra/atommanage:atommanage_go_proto",
        "//idls/byted/devinfra/atommanage:atommanage_go_proto_kitexcli_AtomMarketService",
        "//idls/byted/devinfra/atommanage:atommanage_go_proto_xrpc_and_kitex_AtomMarketService",
        "//idls/byted/devinfra/pipeline/dsl:dsl_go_proto",
        "//idls/byted/devinfra/pipeline/platform:platform_go_proto",
        "//idls/byted/devinfra/pipeline/service:service_go_proto",
        "//libs/middleware/kitexmw",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_jsonx//:jsonx",
        "@org_byted_code_gopkg_logs_v2//:logs",
        "@org_byted_code_gopkg_metainfo//:metainfo",
        "@org_byted_code_kite_kitex//client",
        "@org_byted_code_pipeline_go_sdk//:go-sdk",
    ],
)
