package handler

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/data"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/trigger"
	atom_service "code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/atom"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_run"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/template"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	"code.byted.org/devinfra/hagrid/internal/no_cancel_ctx"
	"code.byted.org/devinfra/hagrid/internal/pipeline/constvar"
	"code.byted.org/devinfra/hagrid/internal/pipeline/patch"
	"code.byted.org/devinfra/hagrid/internal/pipeline/pentity"
	"code.byted.org/devinfra/hagrid/internal/pipeline/perror"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils/ctxutil"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils/timeutil"
	"code.byted.org/devinfra/hagrid/internal/pipeline/validation"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	libUtil "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	pb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/triggerpb"
	"code.byted.org/devinfra/hagrid/pkg/pipeline_event/operator_record_event"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/erri"
	cdutils "code.byted.org/iesarch/cdaas_utils/utils"
)

func (p *PipelineHandler) UpdatePipeline(ctx context.Context, req *servicepb.UpdatePipelineRequest, updateFromTemplatePatch bool) (*servicepb.UpdatePipelineResponse, error) {
	// TODO: if it is bits yaml, then trigger should have id and name field
	var (
		pipelineVersion *entity.PipelineVersion
		sameWithLastRun bool
	)
	// id and name should be converted when parsing pipeline pb
	pipelinePB, err := p.pipelineService.GetPipelinePB(ctx, req.PipelineId, req.EditorType, req.Pipeline, req.YamlFileContent)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline pb, error:%v", err)
		err = perror.GetBitsError(ctx, err)
		return nil, bits_err.PIPELINECOMMON.ErrParsePB.AddOrPass(ctx, err)
	}
	if pipelinePB.Concurrency == nil {
		pipelinePB.Concurrency = &dslpb.Concurrency{
			Max:         -1,
			NewRunFirst: false,
		}
	}

	originPipeline, err := infra.PipelineRepo().GetPipeline(ctx, req.PipelineId)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline, error:%s", err.Error())
		return nil, err
	}
	editorType, err := entity.EditorTypeFromPB(req.EditorType)
	if err != nil {
		err = perror.GetBitsError(ctx, err)
		return nil, bits_err.PIPELINECOMMON.ErrParsePB.AddOrPass(ctx, err)
	}
	if editorType != originPipeline.EditorType {
		return nil, bits_err.PIPELINE.ErrDifferentEditorType.AddErrMsg(fmt.Sprintf("EditorType is different %s!=%s", editorType.String(), originPipeline.EditorType.String()))
	}

	// GUI 模式 pipeline 信息校验
	err = validation.ValidatePipeline(pipelinePB)
	if err != nil {
		logs.CtxError(ctx, "failed to validate gui pipeline, error:%v", err)
		err = perror.GetBitsError(ctx, err)
		return nil, bits_err.PIPELINE.ErrValidatePipelineFailed.AddOrPass(ctx, err)
	}
	spaceID, err := infra.PipelineRepo().GetSpaceIdOfPipeline(ctx, req.GetPipelineId())
	if err != nil {
		logs.CtxError(ctx, "failed to get space id , error:%s", err.Error())
		return nil, err
	}

	// 非模板调用更新，需要判读锁逻辑和orca变更逻辑
	var originVersion *entity.PipelineVersion
	if !updateFromTemplatePatch {
		if editorType == entity.EditorTypeGui {
			originVersion, err = infra.PipelineRepo().GetPipelineVersion(ctx, originPipeline.PipelineID, originPipeline.PipelineVersion)
			if err != nil {
				logs.CtxError(ctx, "failed to get pipeline version, error:%s", err.Error())
				return nil, err
			}

			originPipelinePB, err := p.pipelineService.GetPipelinePBFromVersion(ctx, originPipeline, originVersion)
			if err != nil {
				logs.CtxError(ctx, "failed to get pipeline by id, error:%s", err.Error())
				return nil, err
			}

			// pipeline lock is read-only
			pipelinePB.LockInfos = originPipelinePB.LockInfos
			if orcaLockByTemplate, varLockedByTemplate, err := canUpdatePipeline(ctx, originPipelinePB, pipelinePB); err != nil {
				err = perror.GetBitsError(ctx, err)
				return nil, bits_err.PIPELINE.ErrUpdatePipeline.AddOrPass(ctx, err)
			} else if orcaLockByTemplate || varLockedByTemplate {
				return nil, bits_err.PIPELINE.ErrPipelineLock.AddErrMsg(fmt.Sprintf("can not update pipeline because orca_locked:%t var_locked:%t", orcaLockByTemplate, varLockedByTemplate))
			}
		}
	}

	// update var
	varGroup, err, _ := p.UpdateVarGroup(ctx, pipelinePB, spaceID, req.Username, originPipeline.VarGroupID)
	if err != nil {
		logs.CtxError(ctx, "failed to update var group, error:%s", err.Error())
		return nil, err
	}
	pipelinePB.VarGroup = varGroup

	g := errgroup.Group{}

	// update yaml file
	if req.EditorType == pb.EditorType_EDITOR_TYPE_YAML {
		g.Go(func() error {
			defer cdutils.PanicGuard(ctx)
			innerErr := data.UpsertPipelineYaml(ctx, req.PipelineId, req.YamlFileContent)
			if innerErr != nil {
				logs.CtxError(ctx, "failed to update tos yaml file, Error:%s", innerErr.Error())
				return err
			}
			return nil
		})
	}

	triggerGroup, err := p.SavePipelineTriggers(ctx, pipelinePB, spaceID, req.Username)
	if err != nil {
		logs.CtxError(ctx, "failed to save pipeline %s trigger, error:%s", req.PipelineId, err.Error())
		return nil, erri.Error(err)
	}
	pipelinePB.Triggers = triggerGroup.GetTriggers()
	pipelinePB.TriggerGroup = triggerGroup

	if len(req.Jwt) > 0 {
		g.Go(func() error {
			defer cdutils.PanicGuard(ctx)
			innerErr := p.permissionService.UpdatePipelineAuthorizations(ctx, req.Jwt, req.PipelineId, pipelinePB.Authorizations)
			if innerErr != nil {
				logs.CtxError(ctx, "failed to update pipeline authorizations, error:%s", innerErr.Error())
				return innerErr
			}
			return nil
		})
	}

	if err = g.Wait(); err != nil {
		logs.CtxError(ctx, "failed to update pipeline, error:%s", err.Error())
		return nil, err
	}

	err = mysql.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// update notification
		notificationGroup, err := p.notificationService.Save(ctx, entity.InstanceTypePipeline, req.PipelineId, pipelinePB.Notifications, req.Username)
		if err != nil {
			logs.CtxError(ctx, "failed to save pipeline %s notifications, error:%s", req.PipelineId, err.Error())
			return err
		}
		pipelinePB.Notifications = notificationGroup.Notifications
		pipelinePB.NotificationGroup = notificationGroup

		originPipelineTags, err := infra.PipelineRepo().GetPipelineTagsByPipelineIDs(ctx, []uint64{req.PipelineId})
		if err != nil {
			logs.CtxError(ctx, "failed to get pipeline tag psm, error:%s", err.Error())
			return err
		}
		if err = data.TxUpdatePipelineTag(ctx, req.PipelineId, originPipelineTags, pipelinePB.Tag, tx); err != nil {
			logs.CtxError(ctx, "failed to update pipeline tag, error:%s", err.Error())
			return err
		}
		if err = SavePipelineScheduledDelete(ctx, req.PipelineId, pipelinePB, req.Username, tx); err != nil {
			logs.CtxError(ctx, "failed to save pipeline scheduled delete, error:%s", err.Error())
			return err
		}

		// update pipeline version
		pipelineVersion, sameWithLastRun, err = p.pipelineService.CreatePipelineVersion(ctx, pipelinePB, req.Username, req.UpdatedType, req.Note, tx)
		if err != nil {
			logs.CtxError(ctx, "failed to update pipeline version, error:%s", err.Error())
			if errors.Is(err, gorm.ErrDuplicatedKey) || strings.Contains(err.Error(), "Duplicate entry") {
				err = bits_err.PIPELINE.ErrCreatePipelineVersionOnDuplicatedKey.AddErrMsg("pipeline version is duplicated, please refresh and retry")
			}
			return err
		}

		// update pipeline
		opts := &entity.Pipeline{
			VarPreference:   entity.VarPreferenceFromPB(pipelinePB.VarPreference),
			Description:     utils.GetDefaultString(pipelinePB.Desc),
			Name:            utils.GetDefaultString(pipelinePB.Name),
			PipelineVersion: pipelineVersion.Version,
			Concurrency:     pipelinePB.Concurrency.Max,
			NewRunFirst:     pipelinePB.Concurrency.NewRunFirst,
			VarOption:       entity.FromVarOptionPB(pipelinePB.GetVarOption()),
			Notifications:   nil, // 清空原本的通知
			VarGroupID:      varGroup.GetGroupId(),
			UpdatedBy:       req.Username,
			UpdatedAt:       time.Now(),
			ExpiredDays:     pipelinePB.GetExpiredDays(),
		}
		_, err = data.TxUpdatePipeline(ctx, req.PipelineId, opts, tx)
		if err != nil {
			logs.CtxError(ctx, "failed to update pipeline, error:%s", err.Error())
			return err
		}

		return nil
	})
	if err != nil {
		logs.CtxError(ctx, "failed to update pipeline %s, error:%s", req.PipelineId, err.Error())
		return nil, perror.GetBitsError(ctx, err)
	}

	_ = p.translationService.UpsertPipelineName(ctx, req.PipelineId, pipelinePB.Name)
	if pipelinePB.Desc != nil {
		_ = p.translationService.UpsertPipelineDesc(ctx, req.PipelineId, pipelinePB.Desc)
	}

	pipeline, err := infra.PipelineRepo().GetPipeline(ctx, req.PipelineId)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline, error:%s", err.Error())
		return nil, err
	}
	err = p.AdjustConcurrency(ctx, originPipeline, pipeline)
	if err != nil {
		logs.CtxError(ctx, "failed to run blocking pipeline runs when concurrency is changed, error", err)
	}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				logs.CtxError(ctx, "AddAtomRecentUse error %v", err)
			}
		}()
		if !updateFromTemplatePatch {
			if originVersion == nil && originPipeline != nil {
				originVersion, err = infra.PipelineRepo().GetPipelineVersion(ctx, originPipeline.PipelineID, originPipeline.PipelineVersion)
				if err != nil {
					logs.CtxError(ctx, "failed to get pipeline version, error:%s", err.Error())
				}
			}
			atom_service.AddRecentUsedAtom(ctx, req.Username, pipelinePB.Stages, originVersion.GetOrca().Stages)
		}
	}()
	ctx2 := no_cancel_ctx.WithoutCancel(ctx)
	libUtil.SafeGoWithoutCopyCtx(ctx, func() {
		TeaDoneUpdatePipeline(ctx2)
	})

	// 发送操作记录事件
	p.pipelineEventService.SyncSendRecordEvent(ctx, operator_record_event.OperateEvent{
		EventType:   operator_record_event.EventType_Pipeline,
		PipelineId:  req.PipelineId,
		Operator:    req.Username,
		OperateType: operator_record_event.OperateType_Pipeline_Edit,
		Params: &operator_record_event.OperateEventParams{
			SpaceId:   spaceID,
			SceneType: pipeline.SceneType,
		},
	})

	return &servicepb.UpdatePipelineResponse{Pipeline: &pb.Pipeline{
		PipelineId:      req.PipelineId,
		SpaceId:         spaceID,
		EditorType:      req.EditorType,
		Pipeline:        pipelinePB,
		YamlFileContent: req.YamlFileContent,
		PipelineVersion: &pb.PipelineVersion{
			Version:   pipelineVersion.Version,
			UpdatedAt: timeutil.TimeToString(pipelineVersion.CreatedAt),
			UpdatedBy: pipelineVersion.CreatedBy,
		},
		SameWithLastRun: sameWithLastRun,
		LastRunId:       pipeline.LastRunID,
		IsFavor:         p.pipelineService.IsFavorPipeline(ctx, req.Username, pipeline.PipelineID),
		CreatedAt:       timeutil.TimeToString(pipeline.CreatedAt),
		CreatedBy:       pipeline.CreatedBy,
		UpdatedAt:       timeutil.TimeToString(pipeline.UpdatedAt),
		UpdatedBy:       pipeline.UpdatedBy,
		ArchivedAt:      timeutil.TimePtrToString(pipeline.ArchivedAt),
		ArchivedBy:      pipeline.ArchivedBy,
	}}, nil
}

func (p *PipelineHandler) UpdateVarGroup(ctx context.Context, pipelinePB *dslpb.Pipeline, spaceID uint64,
	username string, varGroupId uint64) (*varstorepb.VarGroup, error, bool) {
	if !p.pipelineVarService.VarExisted(pipelinePB.VarGroup) {
		return nil, nil, false
	}
	// 防止上游传递无用参数
	pipelinePB.VarGroup.BitsWorkspaceId = spaceID
	pipelinePB.VarGroup.GroupId = varGroupId

	group, err, created := p.pipelineVarService.UpgradeOrCreateVarGroup(ctx, pipelinePB.VarGroup, username, pipelinePB.Id)
	if err != nil {
		if strings.Contains(err.Error(), "duplicated variable name") {
			err = perror.GetInvalidArgHerror(err.Error())
		}
		return nil, err, false
	}
	return group, nil, created
}

func (p *PipelineHandler) UpdatePipelineControlPanel(ctx context.Context, pipelineID uint64, controlPanel dslpb.ControlPanel, username string) error {
	cp := pentity.ControlPanelFromPB(controlPanel)
	err := infra.PipelineRepo().UpdatePipelineControlPanel(ctx, pipelineID, cp, username)
	if err != nil {
		return err
	}
	return nil
}

func (p *PipelineHandler) UpdatePipelineVars(ctx context.Context, req *servicepb.UpdatePipelineVarsRequest) (*servicepb.UpdatePipelineVarsResponse, error) {
	if !p.pipelineVarService.VarExisted(req.VarGroup) {
		return &servicepb.UpdatePipelineVarsResponse{
			VarPreference: req.VarPreference,
			VarGroup:      req.VarGroup,
		}, nil
	}
	pipeline, err := infra.PipelineRepo().GetPipeline(ctx, req.PipelineId)
	if err != nil {
		logs.CtxError(ctx, "failed to get space id , error:%s", err.Error())
		return nil, err
	}

	varGroup, err := p.pipelineVarService.GetVarGroupByPipelineID(ctx, req.PipelineId)
	if err != nil {
		return nil, err
	}
	req.VarGroup.WorkspaceId = pipeline.SpaceID
	req.VarGroup.GroupId = varGroup.GetGroupId()
	req.VarGroup.Version = varGroup.GetVersion()

	group, err, created := p.pipelineVarService.UpgradeOrCreateVarGroup(ctx, req.VarGroup, req.Username, req.PipelineId)
	if err != nil {
		return nil, err
	}
	if created {
		req.VarGroup.GroupId = group.GroupId
	}
	// 版本联动 TODO：
	if err := infra.PipelineRepo().UpdatePipelineVarPreferenceAndId(ctx, req.PipelineId, req.VarPreference, req.VarGroup.GroupId, req.Username); err != nil {
		return nil, err
	}
	return &servicepb.UpdatePipelineVarsResponse{
		VarPreference: req.VarPreference,
		VarGroup:      group,
	}, err
}

func (p *PipelineHandler) SavePipelineTriggers(ctx context.Context, pipelinePB *dslpb.Pipeline, spaceID uint64, username string) (*dslpb.TriggerGroup, error) {
	p.pipelineService.FillTriggerVarGroup(pipelinePB.Triggers, spaceID)
	triggerGroupID, err := p.pipelineService.GetTriggerGroupIDOfPipeline(ctx, pipelinePB.Id)
	if err != nil {
		logs.CtxError(ctx, "failed to get trigger group id, error:%s", err.Error())
		return nil, err
	}
	resp, err := trigger.GetClient().SavePipelineTriggers(ctx, &triggerpb.SavePipelineTriggersRequest{
		PipelineId: pipelinePB.Id,
		TriggerGroup: &dslpb.TriggerGroup{
			GroupId:  triggerGroupID,
			Triggers: pipelinePB.Triggers,
		},
		OperatedBy: username,
	})
	if err != nil {
		logs.CtxError(ctx, "failed to save pipeline %s triggers, error:%s", pipelinePB.Id, err.Error())
		err = perror.GetBitsError(ctx, err)
		return nil, bits_err.PIPELINE.ErrSavePipelineTriggers.AddOrPass(ctx, err)
	}

	return resp.TriggerGroup, nil
}

func canUpdatePipeline(ctx context.Context, originPipelinePB, pipelinePB *dslpb.Pipeline) (orcaLockByTemplate bool, varLockedByTemplate bool, err error) {
	bindingResp, err := template.GetClient().GetTemplateByPipelineID(ctx, &pb.GetTemplateByPipelineIDReq{
		PipelineId: originPipelinePB.GetId(),
	})
	if err != nil {
		logs.CtxError(ctx, "get bind info fail, err:%s", err.Error())
		return false, false, err
	}

	if bindingResp.GetExist() {
		// check orca changed
		if orcaLockByTemplate, err = patch.PipelineOrcaIsChanged(ctx, originPipelinePB, pipelinePB); err != nil {
			logs.CtxError(ctx, "judge pipeline orca changed fail, err: %s", err.Error())
			return false, false, err
		}
	}

	if varLockedByTemplate, err = patch.DiffIsLocked(ctx, originPipelinePB, pipelinePB); err != nil {
		logs.CtxError(ctx, "fail to judge diff is locked, err:%s", err.Error())
		return false, false, err
	}
	return orcaLockByTemplate, varLockedByTemplate, nil
}

func (p *PipelineHandler) AdjustConcurrency(ctx context.Context, originPipeline *entity.Pipeline, newPipeline *entity.Pipeline) error {
	if originPipeline.Concurrency == -1 {
		// no limit of original pipeline, so there is no blocking runs
		return nil
	}
	if (newPipeline.Concurrency != -1) && (newPipeline.Concurrency <= originPipeline.Concurrency) {
		return nil
	}

	// if original pipeline has a limit while new pipeline does not
	hasBlockingRuns, err := infra.PipelineRunRepo().HasPipelineRunsByPipelineIDAndStatus(ctx, newPipeline.PipelineID, entity.PipelineRunStatusBlocking)
	if err != nil {
		return err
	}
	if !hasBlockingRuns {
		return nil
	}

	// 如果有阻塞的运行，获取具体的运行列表进行处理
	blockingRuns, err := infra.PipelineRunRepo().GetPipelineRunsByPipelineIDAndStatus(ctx, newPipeline.PipelineID, entity.PipelineRunStatusBlocking)
	if err != nil {
		return err
	}
	blockingToRun := len(blockingRuns)
	if newPipeline.Concurrency > originPipeline.Concurrency {
		gap := int(newPipeline.Concurrency - originPipeline.Concurrency)
		if gap < blockingToRun {
			blockingToRun = gap
		}
	}
	var wg sync.WaitGroup
	for i := 0; i < blockingToRun; i++ {
		wg.Add(1)
		go func(blockingRun *entity.PipelineRun) {
			defer wg.Done()
			pipeline, err := infra.PipelineRepo().GetPipelineWithOrca(ctx, blockingRun.PipelineId, blockingRun.PipelineVersion)
			if err != nil {
				logs.CtxError(ctx, "failed to get pipeline with orca, error: %s", err)
				return
			}
			parentRunID, err := infra.PipelineRunRelationRepo().GetParentPipelineRunID(ctx, blockingRun.RunId)
			if err != nil {
				logs.CtxError(ctx, "failed to get parent pipeline run id, error: %s", err)
				return
			}
			runCtx := &pipeline_run.RunContext{
				PipelineID:  blockingRun.PipelineId,
				Username:    blockingRun.CreatedBy,
				ParentRunID: parentRunID,
				Pipeline:    pipeline,
				PipelineRun: blockingRun, // trying to run this one
				// no need to set values of the rest fields, already in pipeline run
			}
			err = p.engineCallbackService.RunNextBlockingRun(ctx, runCtx)
			if err != nil {
				logs.CtxError(ctx, "failed to try next blocking run, error: %s", err)
				return
			}
		}(blockingRuns[i])
	}
	wg.Wait()
	return nil
}

func (p *PipelineHandler) UpdatePipelineDsl(ctx context.Context, req *servicepb.UpdatePipelineDslRequest) (*servicepb.UpdatePipelineDslResponse, error) {
	getPipelineReq := &servicepb.GetPipelineRequest{
		PipelineId: req.GetPipelineId(),
		Username:   ctxutil.GetContextStringValue(ctx, constvar.ContextKey_USERNAME),
	}
	originPipeline, err := p.GetPipeline(ctx, getPipelineReq)
	if err != nil {
		return nil, err
	}
	updatedPipeline := originPipeline.GetPipeline().GetPipeline()
	updatedPipeline.Stages = req.GetStages()
	updatePipelineReq := &servicepb.UpdatePipelineRequest{
		PipelineId:  req.PipelineId,
		EditorType:  pb.EditorType_EDITOR_TYPE_GUI,
		Pipeline:    updatedPipeline,
		Username:    ctxutil.GetContextStringValue(ctx, constvar.ContextKey_USERNAME),
		UpdatedType: pb.PipelineVersionUpdatedType_PIPELINE_VERSION_UPDATED_TYPE_MANUAL,
	}
	updatePipelineResp, err := p.UpdatePipeline(ctx, updatePipelineReq, false)
	if err != nil {
		return nil, err
	}
	return &servicepb.UpdatePipelineDslResponse{
		Pipeline: updatePipelineResp.GetPipeline().GetPipeline(),
	}, nil
}
