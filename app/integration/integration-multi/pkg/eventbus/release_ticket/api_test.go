package release_ticket

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/service/integration"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/lang/gg/gresult"
)

func TestNewListenHandlerSvcAutoGen(t *testing.T) {
	// Verify the scenario in which NewListenHandlerSvc initializes successfully.验证NewListenHandlerSvc成功初始化的场景
	t.Run("testNewListenHandlerSvc_Success", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var integrationSrvMockPtrValue integration.IntegrationSrv
			integrationSrvMock := &integrationSrvMockPtrValue
			mockey.Mock(integration.NewIntegrationSrv).Return(integrationSrvMock).Build()

			// prepare parameters
			var dbPtrValueConfigDialector gorm.Dialector
			dbPtrValueConfig := gorm.Config{Dialector: dbPtrValueConfigDialector}
			dbPtrValue := gorm.DB{Config: &dbPtrValueConfig}
			db := &dbPtrValue

			// run target function and assert
			got1 := NewListenHandlerSvc(db)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestListenHandlerManager_ListenHandlerAutoGen(t *testing.T) {
	// Verify the functionality of the ListenHandler method. 验证ListenHandler方法的功能
	t.Run("testListenHandlerManager_ListenHandler", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*integration.IntegrationSrv).RemoveBranchUsed).Return(rMock).Build()

			// prepare parameters
			var receiverPtrValue ListenHandlerManager
			receiver := &receiverPtrValue
			ctx := context.Background()
			var eventPtrValue events.ReleaseTicketStatusChangeEventV1
			event := &eventPtrValue

			// run target function and assert
			got1 := receiver.ListenHandler(ctx, event)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

