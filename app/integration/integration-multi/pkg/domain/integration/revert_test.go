package integration

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/kitex_gen/bits/integration/multi"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/dal/rds/entity"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/dal/rds/repository"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/model"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/rpc"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/service/common"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/branching_model_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/lang/gg/gresult"
)

func TestIntegration_GetRevertIntegrationAutoGen(t *testing.T) {
	// Verify the function behavior when GetCleanChangeItemsByIntegrationId returns an error. 验证GetCleanChangeItemsByIntegrationId返回错误时的函数行为
	t.Run("testIntegration_GetRevertIntegration_FailureCase", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var releaseTicketConfigMockPtrValue workflowpb.ReleaseTicketConfig
			releaseTicketConfigMock := &releaseTicketConfigMockPtrValue
			mockey.Mock((*workflowpb.WorkflowConfig).GetReleaseTicketConfig, mockey.OptUnsafe).Return(releaseTicketConfigMock).Build()

			var workflowConfigMockPtrValue workflowpb.WorkflowConfig
			workflowConfigMock := &workflowConfigMockPtrValue
			mockey.Mock((*release_ticketpb.ReleaseTicketDetail).GetWorkflowConfig, mockey.OptUnsafe).Return(workflowConfigMock).Build()

			var branchingModelConfigMockPtrValue branching_model_configpb.BranchingModelConfig
			branchingModelConfigMock := &branchingModelConfigMockPtrValue
			mockey.Mock((*workflowpb.ReleaseTicketConfig).GetBranchingModelConfig, mockey.OptUnsafe).Return(branchingModelConfigMock).Build()

			var getProjectsForRevertRespMockPtrValue multi.GetProjectsForRevertResp
			getProjectsForRevertRespMock := &getProjectsForRevertRespMockPtrValue
			mockey.Mock(multi.NewGetProjectsForRevertResp, mockey.OptUnsafe).Return(getProjectsForRevertRespMock).Build()

			var branchNamingTypeMock branching_model_configpb.BranchNamingType
			mockey.Mock((*branching_model_configpb.BranchNaming).GetBranchNamingType, mockey.OptUnsafe).Return(branchNamingTypeMock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*repository.IntegrationRepository).GetInfoByIntegrationId).Return(rMock).Build()

			var changeItemsGroupByRepoIdRet1Mock map[int64][]*entity.ChangeItem
			mockey.Mock(model.ChangeItemsGroupByRepoId).Return(changeItemsGroupByRepoIdRet1Mock).Build()

			var getCleanChangeItemsByIntegrationIdRet1Mock []*entity.ChangeItem
			getCleanChangeItemsByIntegrationIdRet2Mock := fmt.Errorf("error")
			mockey.Mock(common.GetCleanChangeItemsByIntegrationId).Return(getCleanChangeItemsByIntegrationIdRet1Mock, getCleanChangeItemsByIntegrationIdRet2Mock).Build()

			mockey.Mock((*multi.GetProjectsForRevertResp).SetProjectRepos, mockey.OptUnsafe).Return().Build()

			var getIntegrationIdRet1Mock int64
			mockey.Mock((*multi.GetProjectsForRevertReq).GetIntegrationId, mockey.OptUnsafe).Return(getIntegrationIdRet1Mock).Build()

			mockey.Mock((*multi.GetProjectsForRevertResp).SetWillRemoveBranch, mockey.OptUnsafe).Return().Build()

			var releaseTicketDetailMockPtrValue release_ticketpb.ReleaseTicketDetail
			releaseTicketDetailMock := &releaseTicketDetailMockPtrValue
			var getReleaseTicketRet2Mock error
			mockey.Mock((*rpc.ReleaseTicketRpc).GetReleaseTicket).Return(releaseTicketDetailMock, getReleaseTicketRet2Mock).Build()

			var branchNamingMockPtrValue branching_model_configpb.BranchNaming
			branchNamingMock := &branchNamingMockPtrValue
			mockey.Mock((*branching_model_configpb.BranchingModelConfig).GetIntegrationBranch, mockey.OptUnsafe).Return(branchNamingMock).Build()

			// prepare parameters
			var receiverPtrValue Integration
			receiver := &receiverPtrValue
			ctx := context.Background()
			var reqPtrValue multi.GetProjectsForRevertReq
			req := &reqPtrValue

			// run target function and assert
			got1, got2 := receiver.GetRevertIntegration(ctx, req)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the function behavior when conditions lead to a panic. 验证条件导致恐慌时的函数行为
	t.Run("testIntegration_GetRevertIntegration_PanicCase", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getIntegrationIdRet1Mock int64
			mockey.Mock((*multi.GetProjectsForRevertReq).GetIntegrationId, mockey.OptUnsafe).Return(getIntegrationIdRet1Mock).Build()

			var workflowConfigMockPtrValue workflowpb.WorkflowConfig
			workflowConfigMock := &workflowConfigMockPtrValue
			mockey.Mock((*release_ticketpb.ReleaseTicketDetail).GetWorkflowConfig, mockey.OptUnsafe).Return(workflowConfigMock).Build()

			var getProjectsForRevertRespMockPtrValue multi.GetProjectsForRevertResp
			getProjectsForRevertRespMock := &getProjectsForRevertRespMockPtrValue
			mockey.Mock(multi.NewGetProjectsForRevertResp, mockey.OptUnsafe).Return(getProjectsForRevertRespMock).Build()

			var changeItemsGroupByRepoIdRet1Mock0 []*entity.ChangeItem
			changeItemsGroupByRepoIdRet1Mock := map[int64][]*entity.ChangeItem{0: changeItemsGroupByRepoIdRet1Mock0}
			mockey.Mock(model.ChangeItemsGroupByRepoId).Return(changeItemsGroupByRepoIdRet1Mock).Build()

			var branchingModelConfigMockPtrValue branching_model_configpb.BranchingModelConfig
			branchingModelConfigMock := &branchingModelConfigMockPtrValue
			mockey.Mock((*workflowpb.ReleaseTicketConfig).GetBranchingModelConfig, mockey.OptUnsafe).Return(branchingModelConfigMock).Build()

			var releaseTicketDetailMockPtrValue release_ticketpb.ReleaseTicketDetail
			releaseTicketDetailMock := &releaseTicketDetailMockPtrValue
			var getReleaseTicketRet2Mock error
			mockey.Mock((*rpc.ReleaseTicketRpc).GetReleaseTicket).Return(releaseTicketDetailMock, getReleaseTicketRet2Mock).Build()

			mockey.Mock((*multi.GetProjectsForRevertResp).SetWillRemoveBranch, mockey.OptUnsafe).Return().Build()

			mockey.Mock((*multi.GetProjectsForRevertResp).SetProjectRepos, mockey.OptUnsafe).Return().Build()

			var branchNamingTypeMock branching_model_configpb.BranchNamingType
			mockey.Mock((*branching_model_configpb.BranchNaming).GetBranchNamingType, mockey.OptUnsafe).Return(branchNamingTypeMock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*repository.IntegrationRepository).GetInfoByIntegrationId).Return(rMock).Build()

			var releaseTicketConfigMockPtrValue workflowpb.ReleaseTicketConfig
			releaseTicketConfigMock := &releaseTicketConfigMockPtrValue
			mockey.Mock((*workflowpb.WorkflowConfig).GetReleaseTicketConfig, mockey.OptUnsafe).Return(releaseTicketConfigMock).Build()

			var getCleanChangeItemsByIntegrationIdRet1Mock []*entity.ChangeItem
			var getCleanChangeItemsByIntegrationIdRet2Mock error
			mockey.Mock(common.GetCleanChangeItemsByIntegrationId).Return(getCleanChangeItemsByIntegrationIdRet1Mock, getCleanChangeItemsByIntegrationIdRet2Mock).Build()

			var branchNamingMockPtrValue branching_model_configpb.BranchNaming
			branchNamingMock := &branchNamingMockPtrValue
			mockey.Mock((*branching_model_configpb.BranchingModelConfig).GetIntegrationBranch, mockey.OptUnsafe).Return(branchNamingMock).Build()

			// prepare parameters
			var receiverPtrValue Integration
			receiver := &receiverPtrValue
			ctx := context.Background()
			var reqPtrValue multi.GetProjectsForRevertReq
			req := &reqPtrValue
			convey.So(func() { _, _ = receiver.GetRevertIntegration(ctx, req) }, convey.ShouldPanic)
		})
	})

}

