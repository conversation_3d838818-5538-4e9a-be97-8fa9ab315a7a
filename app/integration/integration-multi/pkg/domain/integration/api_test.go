package integration

import (
	"context"
	"errors"
	"testing"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/kitex_gen/bits/integration/multi"
	"code.byted.org/devinfra/hagrid/libs/connections/sqlite"
	"code.byted.org/lang/gg/gresult"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/dal/rds/repository"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/rpc"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/service/dev"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/service/message"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/service/release_ticket"
)

func TestNewIntegrationDomainAutoGen(t *testing.T) {
	// Verify the normal initialization of NewIntegrationDomain function under default conditions. 验证在默认条件下NewIntegrationDomain函数的正常初始化
	t.Run("testNewIntegrationDomainDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var integrationRepositoryMockPtrValue repository.IntegrationRepository
			integrationRepositoryMock := &integrationRepositoryMockPtrValue
			mockey.Mock(repository.NewIntegrationRepository, mockey.OptUnsafe).Return(integrationRepositoryMock).Build()

			var changeItemRepositoryMockPtrValue repository.ChangeItemRepository
			changeItemRepositoryMock := &changeItemRepositoryMockPtrValue
			mockey.Mock(repository.NewChangeItemRepository, mockey.OptUnsafe).Return(changeItemRepositoryMock).Build()

			var devSrvMockPtrValue dev.DevSrv
			devSrvMock := &devSrvMockPtrValue
			mockey.Mock(dev.NewDevSrv).Return(devSrvMock).Build()

			var messageSvcMockPtrValue message.MessageSvc
			messageSvcMock := &messageSvcMockPtrValue
			mockey.Mock(message.NewMessageSvc, mockey.OptUnsafe).Return(messageSvcMock).Build()

			var releaseTicketSrvMockPtrValue release_ticket.ReleaseTicketSrv
			releaseTicketSrvMock := &releaseTicketSrvMockPtrValue
			mockey.Mock(release_ticket.NewReleaseTicketSrv, mockey.OptUnsafe).Return(releaseTicketSrvMock).Build()

			var releaseTicketSvcMockValueImpl rpc.ReleaseTicketRpc
			releaseTicketSvcMockValue := &releaseTicketSvcMockValueImpl
			releaseTicketSvcMock := releaseTicketSvcMockValue
			mockey.Mock(rpc.NewReleaseTicketRpc, mockey.OptUnsafe).Return(releaseTicketSvcMock).Build()

			// prepare parameters
			var dbPtrValueConfigDialector gorm.Dialector
			dbPtrValueConfig := gorm.Config{Dialector: dbPtrValueConfigDialector}
			dbPtrValue := gorm.DB{Config: &dbPtrValueConfig}
			db := &dbPtrValue

			// run target function and assert
			got1 := NewIntegrationDomain(db)
			convey.So((*got1).IntegrationRepository == nil, convey.ShouldBeFalse)
			convey.So((*got1).ChangeItemRepository == nil, convey.ShouldBeFalse)
			convey.So((*got1).DevSrv == nil, convey.ShouldBeFalse)
			convey.So((*got1).MessageSrv == nil, convey.ShouldBeFalse)
			convey.So((*got1).ReleaseTicketSrv == nil, convey.ShouldBeFalse)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func Test_Integration_CheckBranchActive(t *testing.T) {
	mockDB := sqlite.MustInitialize()
	mockIntegrationRepo := repository.NewIntegrationRepository(mockDB)
	mockDomain := &Integration{
		IntegrationRepository: mockIntegrationRepo,
	}

	mockey.PatchConvey("测试CheckBranchActive方法", t, func() {
		mockey.PatchConvey("请求验证失败的场景", func() {
			// Arrange
			mockReq := &multi.CheckBranchActiveRequest{}
			mockErr := errors.New("请求验证失败")
			mockey.Mock(verifyCheckBranchActiveRequest).Return(mockErr).Build()

			// Act
			result := mockDomain.CheckBranchActive(context.Background(), mockReq)

			// Assert
			_, err := result.Get()
			convey.So(err, convey.ShouldEqual, mockErr)
		})

		mockey.PatchConvey("查询数据库失败的场景", func() {
			// Arrange
			mockReq := &multi.CheckBranchActiveRequest{
				WorkSpaceId: 1,
				Branch:      "testBranch",
			}
			mockey.Mock(verifyCheckBranchActiveRequest).Return(nil).Build()
			mockErr := errors.New("数据库查询失败")
			mockey.MockGeneric(mockIntegrationRepo.CountByWorkSpaceIdAndBranchAndStatus).Return(gresult.Err[int64](mockErr)).Build()

			// Act
			result := mockDomain.CheckBranchActive(context.Background(), mockReq)

			// Assert
			_, err := result.Get()
			convey.So(err, convey.ShouldEqual, mockErr)
		})

		mockey.PatchConvey("查询数据库成功且分支活跃的场景", func() {
			// Arrange
			mockReq := &multi.CheckBranchActiveRequest{
				WorkSpaceId: 1,
				Branch:      "testBranch",
			}
			mockey.Mock(verifyCheckBranchActiveRequest).Return(nil).Build()
			mockey.MockGeneric(mockIntegrationRepo.CountByWorkSpaceIdAndBranchAndStatus).Return(gresult.OK[int64](1)).Build()

			// Act
			result := mockDomain.CheckBranchActive(context.Background(), mockReq)

			// Assert
			resp, err := result.Get()
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp.Active, convey.ShouldBeTrue)
		})

		mockey.PatchConvey("查询数据库成功但分支不活跃的场景", func() {
			// Arrange
			mockReq := &multi.CheckBranchActiveRequest{
				WorkSpaceId: 1,
				Branch:      "testBranch",
			}
			mockey.Mock(verifyCheckBranchActiveRequest).Return(nil).Build()
			mockey.MockGeneric(mockIntegrationRepo.CountByWorkSpaceIdAndBranchAndStatus).Return(gresult.OK[int64](0)).Build()

			// Act
			result := mockDomain.CheckBranchActive(context.Background(), mockReq)

			// Assert
			resp, err := result.Get()
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp.Active, convey.ShouldBeFalse)
		})
	})
}
