package integration

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/dal/rds/repository"
	"code.byted.org/lang/gg/gresult"
)

func TestNewIntegrationSrvAutoGen(t *testing.T) {
	// Verify the successful creation of the IntegrationSrv object. 验证IntegrationSrv对象创建成功
	t.Run("testNewIntegrationSrv_ObjectCreationSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var integrationRepositoryMockPtrValue repository.IntegrationRepository
			integrationRepositoryMock := &integrationRepositoryMockPtrValue
			mockey.Mock(repository.NewIntegrationRepository, mockey.OptUnsafe).Return(integrationRepositoryMock).Build()

			var changeItemRepositoryMockPtrValue repository.ChangeItemRepository
			changeItemRepositoryMock := &changeItemRepositoryMockPtrValue
			mockey.Mock(repository.NewChangeItemRepository, mockey.OptUnsafe).Return(changeItemRepositoryMock).Build()

			var devTaskRelationRepositoryMockPtrValue repository.DevTaskRelationRepository
			devTaskRelationRepositoryMock := &devTaskRelationRepositoryMockPtrValue
			mockey.Mock(repository.NewDevTaskRelationRepository, mockey.OptUnsafe).Return(devTaskRelationRepositoryMock).Build()

			// prepare parameters
			var dbPtrValueConfigDialector gorm.Dialector
			dbPtrValueConfig := gorm.Config{Dialector: dbPtrValueConfigDialector}
			dbPtrValue := gorm.DB{Config: &dbPtrValueConfig}
			db := &dbPtrValue

			// run target function and assert
			got1 := NewIntegrationSrv(db)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).DevTaskRelationRepository == nil, convey.ShouldBeFalse)
			convey.So((*got1).IntegrationRepository == nil, convey.ShouldBeFalse)
			convey.So((*got1).ChangeItemRepository == nil, convey.ShouldBeFalse)
		})
	})

}

func TestIntegrationSrv_GetDevTasksInfoByIntegrationIdAutoGen(t *testing.T) {
	// Verify the normal execution of GetDevTasksInfoByIntegrationId function. 验证GetDevTasksInfoByIntegrationId函数的正常执行
	t.Run("testIntegrationSrv_GetDevTasksInfoByIntegrationId", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue IntegrationSrv
			receiver := &receiverPtrValue
			convey.So(func() { receiver.GetDevTasksInfoByIntegrationId() }, convey.ShouldNotPanic)
		})
	})

}

func TestIntegrationSrv_GetIntegrationByDevTaskIdAutoGen(t *testing.T) {
	// Verify behavior when repositories in IntegrationSrv are not nil. 验证IntegrationSrv中的存储库不为空时的行为
	t.Run("testIntegrationSrv_GetIntegrationByDevTaskIdRepoNotNil", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*repository.DevTaskRelationRepository).GetIntegrationRelationByDevTaskId).Return(rMock).Build()

			var getInfoByIntegrationIdRet1Mock gresult.R[int]
			mockey.Mock((*repository.IntegrationRepository).GetInfoByIntegrationId).Return(getInfoByIntegrationIdRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue IntegrationSrv
			receiver := &receiverPtrValue
			var receiverDevTaskRelationRepositoryPtrValue repository.DevTaskRelationRepository
			receiver.DevTaskRelationRepository = &receiverDevTaskRelationRepositoryPtrValue
			ctx := context.Background()
			var devTaskId int64
			convey.So(func() { _ = receiver.GetIntegrationByDevTaskId(ctx, devTaskId) }, convey.ShouldPanic)
		})
	})

}

