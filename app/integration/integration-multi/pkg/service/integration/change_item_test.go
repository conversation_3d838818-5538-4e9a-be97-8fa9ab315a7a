package integration

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/dal/rds/entity"
)

func TestIntegrationSrv_FilterChangeItemNotUseAutoGen(t *testing.T) {
	// Verify the function behavior under default conditions. 验证默认条件下的函数行为
	t.Run("testIntegrationSrv_FilterChangeItemNotUse", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue IntegrationSrv
			receiver := &receiverPtrValue
			ctx := context.Background()
			var changeItems []*entity.ChangeItem

			// run target function and assert
			got1 := receiver.FilterChangeItemNotUse(ctx, changeItems)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestIntegrationSrv_GetSameChangeItemAndBranchInPlatformAutoGen(t *testing.T) {
	// Verify the normal operation of GetSameChangeItemAndBranchInPlatform. 验证GetSameChangeItemAndBranchInPlatform的正常运行
	t.Run("testIntegrationSrv_GetSameChangeItemAndBranchInPlatform", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue IntegrationSrv
			receiver := &receiverPtrValue
			var changeItems []*entity.ChangeItem
			convey.So(func() { receiver.GetSameChangeItemAndBranchInPlatform(changeItems) }, convey.ShouldNotPanic)
		})
	})

}

func TestIntegrationSrv_GetNotConsistentMainScmAutoGen(t *testing.T) {
	// Verify the function's behavior when certain conditions in target and current change items are met. 验证当目标和当前变更项中某些条件满足时函数的行为
	t.Run("testIntegrationSrv_GetNotConsistentMainScm", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue IntegrationSrv
			receiver := &receiverPtrValue
			var targetChangeItems []*entity.ChangeItem
			var currentChangeItems []*entity.ChangeItem

			// run target function and assert
			got1 := receiver.GetNotConsistentMainScm(targetChangeItems, currentChangeItems)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

