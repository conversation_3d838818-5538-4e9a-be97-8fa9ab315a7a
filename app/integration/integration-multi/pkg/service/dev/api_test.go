package dev

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/dal/rds/repository"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/rpc"
)

func TestNewDevSrvAutoGen(t *testing.T) {
	// Verify the normal operation and outputs of NewDevSrv function. 验证NewDevSrv函数的正常运行及输出
	t.Run("testNewDevSrv_AutoGen", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var devTaskRelationRepositoryMockPtrValue repository.DevTaskRelationRepository
			devTaskRelationRepositoryMock := &devTaskRelationRepositoryMockPtrValue
			mockey.Mock(repository.NewDevTaskRelationRepository, mockey.OptUnsafe).Return(devTaskRelationRepositoryMock).Build()

			var integrationRepositoryMockPtrValue repository.IntegrationRepository
			integrationRepositoryMock := &integrationRepositoryMockPtrValue
			mockey.Mock(repository.NewIntegrationRepository, mockey.OptUnsafe).Return(integrationRepositoryMock).Build()

			var devTaskRpcSvcMock rpc.DevTaskRpcSvc
			mockey.Mock(rpc.NewDevTaskRpc, mockey.OptUnsafe).Return(devTaskRpcSvcMock).Build()

			// prepare parameters
			var dbPtrValueConfigDialector gorm.Dialector
			dbPtrValueConfig := gorm.Config{Dialector: dbPtrValueConfigDialector}
			dbPtrValue := gorm.DB{Config: &dbPtrValueConfig}
			db := &dbPtrValue

			// run target function and assert
			got1 := NewDevSrv(db)
			convey.So((*got1).DevRelationRepository == nil, convey.ShouldBeFalse)
			convey.So((*got1).IntegrationTaskRepository == nil, convey.ShouldBeFalse)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

