package dev

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/kitex_gen/bytedance/bits/dev"
)

func TestDevSrv_GetChangeItemsByDevTaskIdAutoGen(t *testing.T) {
	// Verify the panic situation in the default condition for GetChangeItemsByDevTaskId method. 验证GetChangeItemsByDevTaskId方法默认条件下的panic情况
	t.Run("testDevSrv_GetChangeItemsByDevTaskId_DefaultConditionPanic", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var devChangeManifestMockPtrValue dev.DevChangeManifest
			devChangeManifestMock := &devChangeManifestMockPtrValue
			mockey.Mock((*dev.DevChange).GetManifest, mockey.OptUnsafe).Return(devChangeManifestMock).Build()

			var getTargetBranchRet1Mock string
			mockey.Mock((*dev.DevCodeChangeElement).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			var devCodeChangeElementMockPtrValue dev.DevCodeChangeElement
			devCodeChangeElementMock := &devCodeChangeElementMockPtrValue
			mockey.Mock((*dev.DevChangeManifest).GetCodeElement, mockey.OptUnsafe).Return(devCodeChangeElementMock).Build()

			var getRepoIdRet1Mock int64
			mockey.Mock((*dev.DevCodeChangeElement).GetRepoId, mockey.OptUnsafe).Return(getRepoIdRet1Mock).Build()

			var getNameRet1Mock string
			mockey.Mock((*dev.DeployProjectInfo).GetName, mockey.OptUnsafe).Return(getNameRet1Mock).Build()

			var deployProjectInfoMockPtrValue dev.DeployProjectInfo
			deployProjectInfoMock := &deployProjectInfoMockPtrValue
			mockey.Mock((*dev.DevChangeDeployConfig).GetProjectInfo, mockey.OptUnsafe).Return(deployProjectInfoMock).Build()

			var getDeployConfigsRet1Mock []*dev.DevChangeDeployConfig
			mockey.Mock((*dev.DevChange).GetDeployConfigs, mockey.OptUnsafe).Return(getDeployConfigsRet1Mock).Build()

			var getProjectUniqueIdRet1Mock string
			mockey.Mock((*dev.DeployProjectInfo).GetProjectUniqueId, mockey.OptUnsafe).Return(getProjectUniqueIdRet1Mock).Build()

			var projectTypeMock dev.ProjectType
			mockey.Mock((*dev.DeployProjectInfo).GetType, mockey.OptUnsafe).Return(projectTypeMock).Build()

			// prepare parameters
			var receiverPtrValue DevSrv
			receiver := &receiverPtrValue
			ctx := context.Background()
			var devTaskId int64
			convey.So(func() { _ = receiver.GetChangeItemsByDevTaskId(ctx, devTaskId) }, convey.ShouldPanic)
		})
	})

}

