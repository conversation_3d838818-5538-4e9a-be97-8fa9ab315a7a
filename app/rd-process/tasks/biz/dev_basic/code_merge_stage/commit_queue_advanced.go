package code_merge_stage

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/dev_basic/base"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/dev_basic/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_mysql"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_tcc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bits/gatekeeper/process"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/utils"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gslice"
	json "github.com/bytedance/sonic"
)

type CommitQueueTaskAdvanced struct {
	base.ServiceTask
}

type MergeParamsAdvanced struct {
	ProjectID                int64   `json:"project_id"`
	IID                      int64   `json:"iid"`
	Username                 string  `json:"username"`
	MergeUserName            *string `json:"merge_username,omitempty"`
	CommitQueueEnabled       bool    `json:"commit_queue_enabled"`
	SquashEnabled            *bool   `json:"squash_enabled,omitempty"`
	SquashCommitMsg          string  `json:"squash_commit_msg"`
	RemoveSourceBranch       *bool   `json:"remove_source_branch,omitempty"`
	MergeMethod              *string `json:"merge_method,omitempty"`
	HideCommitMsg            bool    `json:"hide_commit_msg"`
	MergeMessageTemplateType string  `json:"merge_message_template_type"`
	MergeMessageTemplate     string  `json:"merge_message_template"`
	IsUsingNextCode          bool    `json:"is_using_next_code"`
	UseUserIdentity          bool    `json:"use_user_identity"`
}

func CheckChangeValidAdvanced(ctx context.Context, devBasicID int64, spaceID int64, changes []*dev.DevChangeBasicInfo, config *dev.GetDevTaskBizConfigReponse, workflowResp *config_service.GetOnesiteWorkflowResponse) (bool, map[string]*MergeParamsAdvanced) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: devBasicID,
	}
	//
	spaceIDs, hideCommitMsgSpaceIDs := dal_tcc.GetUseUserIdentityMergeSpaceIDs(ctx)
	useUserIdentity := gslice.Contains(spaceIDs, spaceID) || gslice.Contains(spaceIDs, -1)
	hideCommitMsg := gslice.Contains(hideCommitMsgSpaceIDs, spaceID) || gslice.Contains(hideCommitMsgSpaceIDs, -1)

	mergeParams := make(map[string]*MergeParamsAdvanced)
	useUserIdentityParam := false
	for _, val := range changes {

		if val.ChangeType != dev.ChangeType_CHANGE_TYPE_CODE {
			logs.CtxInfo(ctx, "dev task change.changeType not match (%v)", utils.ToJSON(val))
			continue
		}
		//
		if val.Manifest == nil || val.Manifest.CodeElement == nil {
			logs.CtxInfo(ctx, "dev task change.manifest not valid (%v)", utils.ToJSON(val))
			return false, nil
		}

		//
		codeChangeId := val.Manifest.CodeElement.CodeChangeId
		repoID := val.Manifest.CodeElement.CodebaseRepoId
		changeID := val.Manifest.CodeElement.CodebaseChangeId
		projectID := val.Manifest.CodeElement.RepoId
		iid := val.Manifest.CodeElement.Iid
		targetBranch := val.Manifest.CodeElement.TargetBranch

		//
		if codeChangeId == 0 || repoID == 0 || changeID == 0 || projectID == 0 || iid == 0 || len(targetBranch) <= 0 {
			logs.CtxInfo(ctx, "dev task change.manifest.codeElement not valid (%v)", utils.ToJSON(val))
			return false, nil
		}

		//
		username := val.Creator
		if len(username) <= 0 {
			username = val.Manifest.CodeElement.Creator
			logs.CtxInfo(ctx, "dev task change.creator not valid use change.manifest.codeElement.creator instead (%v)", utils.ToJSON(val))
		}
		if len(username) <= 0 {
			logs.CtxInfo(ctx, "dev task change username not valid (%v)", utils.ToJSON(val))
			return false, nil
		}

		//
		var mergeUsername *string
		if useUserIdentity {
			mergeUsername = &username
			useUserIdentityParam = true
		}

		for _, node := range workflowResp.Workflow.Orchestration {
			if node.NodeFixedName == "DevDevelopStageChangeTask" {
				if node.NodeConfig != nil &&
					node.NodeConfig.CiServiceNodeConfig != nil &&
					node.NodeConfig.CiServiceNodeConfig.ChangeTaskConfig != nil &&
					node.NodeConfig.CiServiceNodeConfig.ChangeTaskConfig.MrMergeByUserIdentityConfig != nil &&
					node.NodeConfig.CiServiceNodeConfig.ChangeTaskConfig.MrMergeByUserIdentityConfig.Enable {
					useUserIdentityParam = true
					userInfo, _ := dal_mysql.GetLatestDevTaskUserMergeInfo(ctx, devBasicID)
					if userInfo != nil && len(userInfo.Username) > 0 {
						mergeUsername = &userInfo.Username
					} else {
						mergeUsername = &username
					}
				}
			}
		}

		//
		commitQueueEnabled, err := rpc.IsMergeQueueEnabledWithCache(ctx, repoID, targetBranch, extra)
		if err != nil {
			return false, nil
		}

		//
		var squash *bool
		squashCommitMsg := ""
		var removeSourceBranch *bool
		var mergeMethod *string
		mergeMsgTemplateType := ""
		mergeMsgTemplate := ""
		isUsingNextCode := false

		//
		if config != nil && config.ChangeConfigMap != nil {
			changeConfig, ok := config.ChangeConfigMap[val.Id]
			if ok && changeConfig != nil {
				if changeConfig.MergeRuleSetting != nil {
					squash = &changeConfig.MergeRuleSetting.EnableSquashCommit
					squashCommitMsg = changeConfig.MergeRuleSetting.CommitMessage
					removeSourceBranch = &changeConfig.MergeRuleSetting.EnableRemoveSourceBranch
				} else {
					falseValue := false
					squash = &falseValue
					removeSourceBranch = &falseValue
				}
				if changeConfig.IsUsingNextCode != nil && *changeConfig.IsUsingNextCode {
					isUsingNextCode = true
				}
			}
		}

		// CAC
		if squash == nil || *squash == false || removeSourceBranch == nil || *removeSourceBranch == false {
			rules, _ := rpc.GetIntegrationRulesByProject(ctx, nil, projectID, targetBranch, extra)
			if rules != nil {
				if squash == nil || *squash == false {
					if rules.SquashCommits != nil {
						squash = rules.SquashCommits
					}
				}
				if removeSourceBranch == nil || *removeSourceBranch == false {
					if rules.DeleteSourceBranch != nil {
						removeSourceBranch = rules.DeleteSourceBranch
					}
				}
			}
		}

		for _, node := range workflowResp.Workflow.Orchestration {
			if node.NodeFixedName == "DevDevelopStageChangeTask" {
				if node.NodeConfig != nil && node.NodeConfig.CiServiceNodeConfig != nil && node.NodeConfig.CiServiceNodeConfig.ChangeTaskConfig != nil {
					mergeMsgTemplateType = node.NodeConfig.CiServiceNodeConfig.ChangeTaskConfig.MrMergeMsgTemplate.TemplateType.String()
					mergeMsgTemplate = node.NodeConfig.CiServiceNodeConfig.ChangeTaskConfig.MrMergeMsgTemplate.MergeMsgTemplate
				}
			}
		}

		if !hideCommitMsg {
			hideCommitMsg = mergeMsgTemplateType == "NONE"
		}

		if isUsingNextCode {
			squash = nil
			squashCommitMsg = ""
			removeSourceBranch = nil
			hideCommitMsg = true // merge commit message 仍然使用 bits 平台的，不使用用户在 Codebase 上设置的
		}

		params := &MergeParamsAdvanced{
			ProjectID:                projectID,
			IID:                      iid,
			Username:                 username,
			MergeUserName:            mergeUsername,
			CommitQueueEnabled:       commitQueueEnabled,
			SquashEnabled:            squash,
			SquashCommitMsg:          squashCommitMsg,
			RemoveSourceBranch:       removeSourceBranch,
			MergeMethod:              mergeMethod,
			HideCommitMsg:            hideCommitMsg,
			MergeMessageTemplateType: mergeMsgTemplateType,
			MergeMessageTemplate:     mergeMsgTemplate,
			IsUsingNextCode:          isUsingNextCode,
			UseUserIdentity:          useUserIdentityParam,
		}
		mergeParams[fmt.Sprintf("%v_%v", projectID, iid)] = params
	}
	return true, mergeParams
}

func (t *CommitQueueTaskAdvanced) Start(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {

	ts := time.Now().Unix()

	taskKey := base.GetTaskStartKey(req.UniqueID, req.TaskName)
	redisResult, err := dal_redis.Optimus.Set(taskKey, ts, 600*time.Second).Result()
	logs.CtxInfo(ctx, "did set redis bc_21_dev_task_start_key: (%v) (%v) (%v)", taskKey, redisResult, err)

	return HandleMergeAdvanced(ctx, req)
}

func (t *CommitQueueTaskAdvanced) Reset(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	notFailedOutput, _ := json.Marshal(map[string]bool{"failed": false})
	//
	return true, notFailedOutput, nil
}

func (t *CommitQueueTaskAdvanced) Close(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	notFailedOutput, _ := json.Marshal(map[string]bool{"failed": false})
	//
	return true, notFailedOutput, nil
}

func (t *CommitQueueTaskAdvanced) Ping(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: req.UniqueID,
	}

	taskKey := base.GetTaskStartKey(req.UniqueID, req.TaskName)
	redisResult, _ := dal_redis.Optimus.Get(taskKey).Result()
	startTime, _ := strconv.ParseInt(redisResult, 10, 64)
	if startTime > 0 {
		ts := time.Now().Unix()
		delta := dal_tcc.TaskRunningTime(ctx, req.TaskName)
		if delta > 0 && ts-startTime > delta {
			rpc.AsyncEmitExceptions(ctx, req.UniqueID, []bits_err.Err{bits_err.TASKS.ErrDevCodeMergeStageCommitQueueTaskTakingTooLong}, extra)
		}
	}

	return HandleMergeAdvanced(ctx, req)
}

func HandleMergeAdvanced(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: req.UniqueID,
	}

	//
	notFailedOutput, _ := json.Marshal(map[string]bool{"failed": false})

	//
	opts := []string{"basic", "change", "biz_config", "workflow", "changes_to_merge"}

	//
	_, changeInfo, devTask, config, workflowResp, _, devChangesToMerge, controlPlaneProjectIdMap, err := base.DevBasicInfo(ctx, req.UniqueID, req.TemplateSnapshotID, opts)
	if err != nil {
		return false, notFailedOutput, nil
	}

	changesInDB, _ := dal_mysql.GetDevBasicChangeIntegrateByDevBasicID(ctx, req.UniqueID)
	changesInDB = gslice.Filter(changesInDB, func(item *dal_mysql.DevBasicChangeIntegrate) bool {
		return item.Status != "merged" && item.Status != "closed"
	})
	changesIDs := gslice.Map(changesInDB, func(item *dal_mysql.DevBasicChangeIntegrate) int64 {
		return item.DevBasicChangeId
	})
	changes := gslice.Filter(changeInfo.Changes, func(item *dev.DevChange) bool {
		return gslice.Contains(changesIDs, item.Id)
	})

	devChangesToMerge = append(devChangesToMerge, gslice.Map(changes, func(item *dev.DevChange) *dev.DevChangeBasicInfo {

		byteArr, _ := json.Marshal(item.Manifest)
		manifest := &dev.DevChangeManifest{}
		_ = json.Unmarshal(byteArr, manifest)

		return &dev.DevChangeBasicInfo{
			Id:               item.Id,
			ChangeType:       item.ChangeType,
			Creator:          item.Creator,
			Manifest:         manifest,
			Status:           "",
			Title:            item.Title,
			CreateAt:         item.CreateAt,
			DevBasicId:       req.UniqueID,
			ContributionId:   nil,
			MergeRuleSetting: nil,
			IsDraft:          item.IsDraft,
		}
	})...)

	valid, mergeParamsDict := CheckChangeValidAdvanced(ctx, req.UniqueID, req.SpaceID, devChangesToMerge, config, workflowResp)
	if !valid {
		logs.CtxInfo(ctx, "dev task with commit queue change params not valid (%v)", utils.ToJSON(devChangesToMerge))
		return false, notFailedOutput, nil
	}

	//
	//if devTask != nil && devTask.BasicInfo != nil && devTask.BasicInfo.Status == dev.DevTaskStatus_closed {
	//	//
	//	CleanDevTaskRedisKeys(ctx, req.UniqueID)
	//	//
	//	return true, notFailedOutput, nil
	//}

	//
	codeMergeChanges := gslice.Filter(devChangesToMerge, func(c *dev.DevChangeBasicInfo) bool {
		return c.ChangeType == dev.ChangeType_CHANGE_TYPE_CODE
	})

	for _, val := range codeMergeChanges {

		controlPlaneProjectIds, ok := controlPlaneProjectIdMap[val.Id]
		if ok {

			for _, v := range controlPlaneProjectIds {
				thirdPartyTasks, err := dal_mysql.GetLatestDevBasicThirdPartyCheckTasks(ctx, req.UniqueID, fmt.Sprintf("gatekeeper_stage_gatekeeper_check_%v", v))
				if err != nil {
					return false, nil, err
				}

				for _, thirdPartyTask := range thirdPartyTasks {
					if thirdPartyTask.StopStatus != "succeeded" {
						err = rpc.ForceTerminateCheckPoint(ctx, req.UniqueID, process.BizScene_BizSceneDevTaskChangeItemTestExit, fmt.Sprintf("%v", v), extra)
						if err != nil {
							return false, nil, nil
						}
						err = dal_mysql.UpdateDevBasicThirdPartyCheckTaskWithStopStatus(ctx, thirdPartyTask.Id, "succeeded")
						if err != nil {
							return false, nil, nil
						}
					}
				}
			}
		}
	}

	//
	changesToCQ, changesToMerge, changesClosed := GetChangesToMergeAdvanced(ctx, req, codeMergeChanges, mergeParamsDict)

	_, draftLockEnabled, mrLockEnabled := dal_tcc.CodebaseLockEnabled(ctx, req.SpaceID)
	if draftLockEnabled || mrLockEnabled {

		for _, val := range changesToCQ {

			projectID := val.Manifest.CodeElement.RepoId
			iid := val.Manifest.CodeElement.Iid
			repoID := val.Manifest.CodeElement.CodebaseRepoId

			status := "completed"
			checkRunStatus := consts.CodebaseCheckStatusCompleted
			conclusion := consts.CodebaseCheckConclusionSuccess

			_ = rpc.UpdateDevTaskLock(ctx, projectID, iid, repoID, req.UniqueID, req.SpaceID, status, checkRunStatus, conclusion)
		}

		for _, val := range changesToMerge {

			projectID := val.Manifest.CodeElement.RepoId
			iid := val.Manifest.CodeElement.Iid
			repoID := val.Manifest.CodeElement.CodebaseRepoId

			status := "completed"
			checkRunStatus := consts.CodebaseCheckStatusCompleted
			conclusion := consts.CodebaseCheckConclusionSuccess

			_ = rpc.UpdateDevTaskLock(ctx, projectID, iid, repoID, req.UniqueID, req.SpaceID, status, checkRunStatus, conclusion)
		}
	}

	//
	SubmitToCommitQueueAdvanced(ctx, req.UniqueID, changesToCQ, mergeParamsDict)

	//
	MergeAdvanced(ctx, req.UniqueID, devTask, changesToMerge, mergeParamsDict)

	//
	//if len(codeMergeChanges) == len(changesClosed) {
	//	autoClose := dal_tcc.EnableAutoClose(ctx)
	//	if autoClose {
	//		_ = rpc.CloseDevTask(ctx, req.UniqueID, extra)
	//	}
	//}

	//
	if len(changesClosed) > 0 {
		return false, notFailedOutput, nil
	}

	// 查询 gitlab 查看是否已经全部合入
	if len(changesToCQ) <= 0 && len(changesToMerge) <= 0 {
		CleanDevTaskRedisKeysAdvanced(ctx, req.UniqueID)
		return false, notFailedOutput, nil
	}

	item, _ := dal_mysql.GetDevBasicTaskByDevBasicID(ctx, req.UniqueID)
	if item == nil {
		logs.CtxInfo(ctx, "get dev basic task by basic id nil (%v)", req.UniqueID)
		return false, notFailedOutput, nil
	}
	if item.DevelopPass == 1 && item.GatekeeperPass == 1 {
		return true, notFailedOutput, nil
	}

	return false, notFailedOutput, nil
}

func GetChangesToMergeAdvanced(ctx context.Context, req *base.TaskRequest, codeMergeChanges []*dev.DevChangeBasicInfo, mergeParamsDict map[string]*MergeParamsAdvanced) ([]*dev.DevChangeBasicInfo, []*dev.DevChangeBasicInfo, []*dev.DevChangeBasicInfo) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: req.UniqueID,
	}

	changesToCQ := make([]*dev.DevChangeBasicInfo, 0)
	changesToMerge := make([]*dev.DevChangeBasicInfo, 0)
	changesClosed := make([]*dev.DevChangeBasicInfo, 0)

	for _, val := range codeMergeChanges {
		//
		projectID := val.Manifest.CodeElement.RepoId
		iid := val.Manifest.CodeElement.Iid

		mergeParams := mergeParamsDict[fmt.Sprintf("%v_%v", projectID, iid)]
		mergeStrategy := choose.If(mergeParams.CommitQueueEnabled, "commit_queue", "normal")

		//
		resp, _ := rpc.GetMR(ctx, projectID, iid, extra)

		//
		mrEnded := false
		if resp != nil && (resp.Mr.State == "merged" || resp.Mr.State == "closed") {
			mrEnded = true
		}

		//
		status := "opened"
		if mrEnded {
			status = resp.Mr.State
		}

		//
		arr, _ := dal_mysql.GetDevBasicChangeIntegrateByDevBasicIDAndChangeID(ctx, req.UniqueID, val.Id)
		recordNotCreated := len(arr) <= 0

		//
		mergeStatus := ""
		if mrEnded && recordNotCreated && status == "merged" { // 合入了 但是还没有创建 db 记录 说明不是我这里合入的
			mergeStatus = "merged_on_codebase"
		}
		if mrEnded && recordNotCreated && status == "closed" { // 关闭了 但是还没有创建 db 记录 说明不是我这里关闭的
			mergeStatus = "closed_on_codebase"
		}

		//
		var dbErr error
		if recordNotCreated { // 第一次记录数据库记录
			dbErr = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateStatus(ctx, req.UniqueID, val.Id, status, mergeStatus, mergeStrategy, "", "")
			if status == "merged" {
				_ = rpc.MarkChangesMerged(ctx, req.UniqueID, []int64{val.Id}, extra)
			}
		} else {
			if mrEnded { // 合入或关闭后更新数据库记录
				dbErr = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateStatus(ctx, req.UniqueID, val.Id, status, "", mergeStrategy, "", "")
				if status == "merged" {
					_ = rpc.MarkChangesMerged(ctx, req.UniqueID, []int64{val.Id}, extra)
				}
			}
		}

		//
		if status == "merged" && len(resp.Mr.MergeCommitSha) > 0 { // 合入了 则记录 merge commit id
			if recordNotCreated || len(gslice.Filter(arr, func(item *dal_mysql.DevBasicChangeIntegrate) bool {
				return item.MergeCommitSha != resp.Mr.MergeCommitSha
			})) > 0 {
				_ = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateMergeCommitSha(ctx, req.UniqueID, val.Id, resp.Mr.MergeCommitSha)
			}
		}

		//
		if !mrEnded || dbErr != nil {
			if mergeParams.CommitQueueEnabled {
				if len(gslice.Filter(changesToCQ, func(item *dev.DevChangeBasicInfo) bool {
					return item.Manifest.CodeElement.RepoId == projectID && item.Manifest.CodeElement.Iid == iid
				})) <= 0 {
					changesToCQ = append(changesToCQ, val)
				}
			} else {
				if len(gslice.Filter(changesToMerge, func(item *dev.DevChangeBasicInfo) bool {
					return item.Manifest.CodeElement.RepoId == projectID && item.Manifest.CodeElement.Iid == iid
				})) <= 0 {
					changesToMerge = append(changesToMerge, val)
				}
			}
		}

		//
		if status == "closed" {
			changesClosed = append(changesClosed, val)
		}
	}
	return changesToCQ, changesToMerge, changesClosed
}

/** commit_queue */

func SubmitToCommitQueueAdvanced(ctx context.Context, devBasicID int64, changesToCQ []*dev.DevChangeBasicInfo, mergeParamsDict map[string]*MergeParamsAdvanced) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: devBasicID,
	}

	for _, val := range changesToCQ {
		//
		projectID := val.Manifest.CodeElement.RepoId
		iid := val.Manifest.CodeElement.Iid
		repoID := val.Manifest.CodeElement.CodebaseRepoId
		changeID := val.Manifest.CodeElement.CodebaseChangeId
		sourceBranch := val.Manifest.CodeElement.SourceBranch

		//
		mergeParams := mergeParamsDict[fmt.Sprintf("%v_%v", projectID, iid)]

		//
		inQueue, _, err := rpc.GetCommitQueueStatus(ctx, repoID, changeID, mergeParams.Username, extra)
		if err != nil {
			continue
		}

		arr, _ := dal_mysql.GetDevBasicChangeIntegrateByDevBasicIDAndChangeID(ctx, devBasicID, val.Id)
		if inQueue {
			if len(arr) > 0 && arr[0].CqStatus != "in_queue" {
				_ = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateCQStatus(ctx, devBasicID, val.Id, "in_queue")
			}
			continue
		} else {
			// 之前在 cq 中，但是被踢出来了，目前不在
			if len(arr) > 0 && arr[0].CqStatus == "in_queue" {
				errMsgZh := fmt.Sprintf("Commit Queue 执行失败")
				errMsgEn := fmt.Sprintf("Commit Queue Execution Failed")
				_ = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateStatus(ctx, devBasicID, val.Id, "opened", "", "", errMsgZh, errMsgEn)
				_ = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateCQStatus(ctx, devBasicID, val.Id, "failed")
				continue
			}
			if len(arr) > 0 && arr[0].CqStatus == "failed" {
				continue
			}
		}

		//
		commit, err := rpc.GetLatestCommitId(ctx, projectID, sourceBranch, extra)
		if err != nil {
			logs.CtxInfo(ctx, "get branch info error: response (%v) error (%v)", commit, err)
		}

		// 调用 commit queue
		_ = rpc.UpdateMergeRequest(ctx, projectID, iid, mergeParams.SquashEnabled, mergeParams.RemoveSourceBranch, mergeParams.Username, extra)

		//
		if len(commit) > 0 && len(arr) > 0 && len(gslice.Filter(arr, func(item *dal_mysql.DevBasicChangeIntegrate) bool {
			return item.LastCommitId != commit
		})) > 0 {
			_ = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateLastCommitID(ctx, devBasicID, val.Id, commit)
		}

		//
		err = rpc.SubmitToCommitQueue(ctx, repoID, changeID, mergeParams.Username, extra)
		if err != nil {
			errMsgZh := fmt.Sprintf("提交到 Commit Queue 失败: (%v)", err)
			errMsgEn := fmt.Sprintf("Submit to Commit Queue Error: (%v)", err)
			_ = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateStatus(ctx, devBasicID, val.Id, "opened", "", "", errMsgZh, errMsgEn)
		} else {
			_ = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateCQStatus(ctx, devBasicID, val.Id, "in_queue")
		}
	}
}

/** merge */

func MergeAdvanced(ctx context.Context, devBasicID int64, devTask *dev.GetDevTaskBasicInfoResponse, changes []*dev.DevChangeBasicInfo, mergeParamsDict map[string]*MergeParamsAdvanced) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: devBasicID,
	}

	//
	for _, val := range changes {
		//
		codeChangeID := val.Manifest.CodeElement.CodeChangeId
		projectID := val.Manifest.CodeElement.RepoId
		iid := val.Manifest.CodeElement.Iid
		repoID := val.Manifest.CodeElement.CodebaseRepoId
		changeID := val.Manifest.CodeElement.CodebaseChangeId
		repoPath := val.Manifest.CodeElement.RepoPath
		sourceBranch := val.Manifest.CodeElement.SourceBranch
		targetBranch := val.Manifest.CodeElement.TargetBranch

		//
		mergeParams := mergeParamsDict[fmt.Sprintf("%v_%v", projectID, iid)]

		//
		commit, err := rpc.GetLatestCommitId(ctx, projectID, sourceBranch, extra)
		if err != nil {
			logs.CtxInfo(ctx, "get branch info error: response (%v) error (%v)", commit, err)
			continue
		}

		_ = rpc.UpdateMergeRequest(ctx, projectID, iid, mergeParams.SquashEnabled, mergeParams.RemoveSourceBranch, mergeParams.Username, extra)

		mergeCommitMsg := GenerateMergeCommitMessageAdvanced(devBasicID, devTask.BasicInfo.GetType().String(), projectID, iid, repoPath, devTask.BasicInfo.Name, sourceBranch, targetBranch, mergeParams.Username, FormDevTaskUrlAdvanced(devTask.BasicInfo.SpaceId, devBasicID), mergeParams.SquashCommitMsg, mergeParams.HideCommitMsg, mergeParams.MergeMessageTemplate)

		arr, _ := dal_mysql.GetDevBasicChangeIntegrateByDevBasicIDAndChangeID(ctx, devBasicID, val.Id)
		if len(arr) > 0 && len(gslice.Filter(arr, func(item *dal_mysql.DevBasicChangeIntegrate) bool {
			return item.LastCommitId != commit
		})) > 0 {
			_ = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateLastCommitID(ctx, devBasicID, val.Id, commit)
		}

		if !mergeParams.UseUserIdentity {
			rpc.TryAddPermissionToMerge(ctx, repoID, targetBranch, mergeParams.MergeMethod)
		}

		AcceptMergeRequestAdvanced(ctx, devBasicID, val.Id, codeChangeID, projectID, iid, repoID, changeID, commit, mergeCommitMsg, mergeParams.SquashCommitMsg, mergeParams.MergeMethod, mergeParams.SquashEnabled, mergeParams.MergeUserName)
	}
}

const (
	BitsBaseURLProdAdvanced = "https://bits.bytedance.net"
	BitsBaseURLBoeAdvanced  = "https://bits-boe.bytedance.net"
)

func GetBitsBaseUrlAdvanced() string {
	return choose.If(env.IsBoe(), BitsBaseURLBoeAdvanced, BitsBaseURLProdAdvanced)
}

func FormDevTaskUrlAdvanced(spaceId int64, devBasicId int64) string {
	return fmt.Sprintf("%v/devops/%v/develop/developmentTask/detail/%v", GetBitsBaseUrlAdvanced(), spaceId, devBasicId)
}

func GenerateMergeCommitMessageAdvanced(devBasicID int64, devBasicTaskType string, projectID int64, iid int64, repoPath string, title string, source string, target string, creator string, bitsUrl string, squashCommitMessage string, hideCommitMsg bool, mergeCommitMessageTemplate string) string {
	if hideCommitMsg {
		return ""
	}

	variableMap := make(map[string]string)
	variableMap["${development_task_id}"] = fmt.Sprintf("%v", devBasicID)
	variableMap["${development_task_type}"] = devBasicTaskType
	variableMap["${development_task_title}"] = title
	variableMap["${development_task_squash_commit_message}"] = squashCommitMessage
	variableMap["${development_task_change_source_branch}"] = source
	variableMap["${development_task_change_target_branch}"] = target
	variableMap["${development_task_change_creator}"] = creator
	variableMap["${development_task_change_creator_email}"] = emails.WithSuffix(creator)
	variableMap["${development_task_change_project_id}"] = fmt.Sprintf("%v", projectID)
	variableMap["${development_task_change_iid}"] = fmt.Sprintf("%v", iid)
	variableMap["${development_task_change_repo_path}"] = fmt.Sprintf("%v", repoPath)
	variableMap["${development_task_url}"] = bitsUrl

	ret := mergeCommitMessageTemplate
	for k, v := range variableMap {
		ret = strings.Replace(ret, k, v, -1)
	}
	return ret
}

type submittableReasonDescriptionAdvanced struct {
	EN string `json:"en"`
	ZH string `json:"zh"`
}

var submittableReasonAdvanced = map[string]submittableReasonDescriptionAdvanced{
	"submitted":              {EN: "mr is already merged", ZH: "MR 已被提前合入"},
	"abandoned":              {EN: "mr is already closed", ZH: "MR 已经被关闭"},
	"locked":                 {EN: "mr is locked", ZH: "MR 被锁，暂时无法合入，稍后重试"},
	"repo_archived":          {EN: "repo is archived", ZH: "仓库已被归档，无法使用"},
	"missing_branch":         {EN: "branch is missing", ZH: "分支不存在，请检查仓库的分支"},
	"no_commits":             {EN: "mr has no commits", ZH: "MR 下无新增 commit，无法合入，请提交代码"},
	"checking_conflict":      {EN: "mr is checking conflict", ZH: "MR 正在检查冲突，请稍等"},
	"conflict":               {EN: "mr is conflicted", ZH: "MR 有冲突，无法合入，请解决冲突后重试"},
	"wip":                    {EN: "mr is WIP", ZH: "MR 开启了 WIP，请关闭后重试"},
	"rebase_needed":          {EN: "mr need rebase target branch", ZH: "MR 需要 rebase 目标分支，请 rebase 后重试"},
	"pipeline_failed":        {EN: "gitlab pipeline failed", ZH: "Codebase Pipeline 失败，无法合入"},
	"discussions_unresolved": {EN: "gitlab discussions unresolved", ZH: "存在未解决的评论，请完成后重试"},
	"pipeline_blocked":       {EN: "gitlab pipeline blocked", ZH: "Codebase Pipeline 阻塞，无法合入"},
	"sha_mismatch":           {EN: "gitlab sha mismatch", ZH: "MR 分支的 SHA 不匹配，请重新 push 代码后重试"},
	"not_allowed":            {EN: "no permission to merge into the target branch, please apply for repository branch permission", ZH: "无权合入目标分支，请申请仓库分支权限"},
	"checks_pending":         {EN: "gitlab checks are pending", ZH: "MR 检查项未开始"},
	"checks_not_passed":      {EN: "gitlab checks not passed", ZH: "MR 检查项未通过，请完成后重试"},     // codebase
	"review_not_passed":      {EN: "gitlab review not passed", ZH: "MR review 未通过，请完成后重试"}, // codebase
	"branch_protected":       {EN: "no permission to merge into the protected target branch, please apply for repository branch permission", ZH: "无权合入受保护的目标分支，请申请仓库分支权限"},
}

func AcceptMergeRequestAdvanced(ctx context.Context, devBasicID int64, devChangeID int64, codeChangeID int64, projectID int64, iid int64, repoID int64, changeID int64, sha string, mergeCommitMsg string, squashCommitMsg string, mergeMethod *string, squash *bool, username *string) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: devBasicID,
	}

	if sha == "" {
		logs.CtxInfo(ctx, "accept merge request error: sha is none (%v) (%v)", projectID, iid)
		return
	}

	// Retry accept merge request
	maxRetryCount := 1
	retryCount := maxRetryCount
	retryInterval := 0
	for retryCount > 0 {
		// retry
		if retryInterval > 0 {
			time.Sleep(time.Duration(retryInterval) * time.Second)
		}
		retryInterval = (retryInterval + 20) * 2
		retryCount -= 1
		//
		mergeError := rpc.MergeCodeChange(ctx, projectID, iid, codeChangeID, mergeCommitMsg, squashCommitMsg, sha, mergeMethod, squash, username, extra)
		//
		mr, _ := rpc.GetMR(ctx, projectID, iid, extra)
		nextCodeMR, _ := rpc.GetMergeRequest(ctx, repoID, iid, extra)
		status := "opened"
		if mr != nil {
			status = mr.Mr.State
		}

		if status == "merged" {
			logs.CtxInfo(ctx, "accept merge request succeeded (%v) (%v)", projectID, iid)
			break
		} else {
			errMsgZh, errMsgEn := GetMergeErrorMessageAdvanced(ctx, devBasicID, repoID, changeID, sha, username)
			if status == "closed" || len(errMsgEn) > 0 {
				_ = dal_mysql.UpdateOrCreateDevBasicChangeIntegrateStatus(ctx, devBasicID, devChangeID, status, "", "normal", errMsgZh, errMsgEn)
				if status == "merged" {
					_ = rpc.MarkChangesMerged(ctx, devBasicID, []int64{devChangeID}, extra)
				}
			}
		}

		if (nextCodeMR != nil && nextCodeMR.Status == "merged" && status == "opened" && mr != nil) ||
			(mergeError != nil &&
				strings.Contains(strings.ToLower(mergeError.Error()), "sha") &&
				strings.Contains(strings.ToLower(mergeError.Error()), "not") &&
				strings.Contains(strings.ToLower(mergeError.Error()), "match")) {
			currentTime := time.Now().Unix()
			errorKey := fmt.Sprintf("handle_gitlab_status_error_key_%v_%v", projectID, iid)
			redisResult, err := dal_redis.Optimus.WithContext(ctx).SetNX(errorKey, currentTime, time.Second*600).Result()
			logs.CtxInfo(ctx, "did set redis handle_gitlab_status_error_key: (%v) (%v) (%v)", errorKey, redisResult, err)
			if err != nil || !redisResult {
				setTimeStr, _ := dal_redis.Optimus.WithContext(ctx).Get(errorKey).Result()
				if len(setTimeStr) > 0 {
					setTimeInt, err := strconv.ParseInt(setTimeStr, 10, 64)
					if err == nil && setTimeInt > 0 && currentTime-setTimeInt > 90 {
						handleGitlabStatusErrorAdvanced(ctx, devBasicID, projectID, iid, mr.Mr.Description)
					}
				}
			}
		}
	}
}

func handleGitlabStatusErrorAdvanced(ctx context.Context, devBasicID int64, projectID int64, iid int64, description string) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: devBasicID,
	}

	whiteSpace := "by bits"
	if strings.HasSuffix(description, whiteSpace) {
		description = description[0 : len(description)-len(whiteSpace)]
	} else {
		description = description + "\n" + whiteSpace
	}
	_ = rpc.UpdateMergeRequestDescription(ctx, projectID, iid, description, extra)
}

func GetMergeErrorMessageAdvanced(ctx context.Context, devBasicID int64, repoID int64, changeID int64, sha string, username *string) (string, string) {
	//
	errMsgZhArr := make([]string, 0)
	errMsgEnArr := make([]string, 0)

	submittable, _ := rpc.GetMergeRequestMergeability(ctx, repoID, changeID).Get()
	//
	if submittable != nil && submittable.UnmergeableReason == "merged" {
		return "", ""
	}
	//
	if submittable != nil && !submittable.Mergeable {
		if len(submittable.GetUnmergeableReason()) > 0 {

			reasonDesc, ok := submittableReasonAdvanced[submittable.GetUnmergeableReason()]
			if ok {
				errMsgZhArr = append(errMsgZhArr, reasonDesc.ZH)
				errMsgEnArr = append(errMsgEnArr, reasonDesc.EN)
			} else {
				errMsgZhArr = append(errMsgZhArr, submittable.GetUnmergeableReason())
				errMsgEnArr = append(errMsgEnArr, submittable.GetUnmergeableReason())
			}
		}
	}

	if len(errMsgEnArr) <= 0 {
		return "", ""
	}
	return strings.Join(errMsgZhArr, ", "), strings.Join(errMsgEnArr, ", ")
}

/** clean */

func CleanDevTaskRedisKeysAdvanced(ctx context.Context, devBasicID int64) {

	developStageKey := fmt.Sprintf("develop_stage_ready_%v", devBasicID)
	gatekeeperStageKey := fmt.Sprintf("gatekeeper_stage_ready_%v", devBasicID)
	codeMergeStageKey := fmt.Sprintf("code_merge_after_check_error_%v", devBasicID)
	codeMergeErrorHasStartedKey := fmt.Sprintf("code_merge_after_check_error_has_enter_started_%v", devBasicID)
	ret, err := dal_redis.Optimus.Del(developStageKey, gatekeeperStageKey, codeMergeStageKey, codeMergeErrorHasStartedKey).Result()

	logs.CtxInfo(ctx, "clean dev task redis keys error (%v) (%v) (%v) (%v) (%v) (%v)", developStageKey, gatekeeperStageKey, codeMergeStageKey, codeMergeErrorHasStartedKey, ret, err)
}
