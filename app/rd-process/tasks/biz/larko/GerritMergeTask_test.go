package larko

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/aosp"
)

func TestGerritMergeTask_IsEndAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var gerritSubmitRet1Mock error
			mockey.Mock(rpc.GerritSubmit).Return(gerritSubmitRet1Mock).Build()

			var updateGerritDevStatusRet1Mock error
			mockey.Mock(rpc.UpdateGerritDevStatus).Return(updateGerritDevStatusRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			// prepare parameters
			receiver := GerritMergeTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var gerritSubmitRet1Mock error
			mockey.Mock(rpc.GerritSubmit).Return(gerritSubmitRet1Mock).Build()

			var updateGerritDevStatusRet1Mock error
			mockey.Mock(rpc.UpdateGerritDevStatus).Return(updateGerritDevStatusRet1Mock).Build()

			// prepare parameters
			var paramsJson string
			var contextJson string
			receiver := GerritMergeTask{}
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, true)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-2 != nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var updateGerritDevStatusRet1Mock error
			mockey.Mock(rpc.UpdateGerritDevStatus).Return(updateGerritDevStatusRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			listGerritUnitsRet2Mock := fmt.Errorf("error")
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var gerritSubmitRet1Mock error
			mockey.Mock(rpc.GerritSubmit).Return(gerritSubmitRet1Mock).Build()

			// prepare parameters
			receiver := GerritMergeTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-2 == nil
	// len(code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-1) > 0
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/aosp.GerritUnit:GetRealTimeInfo()_ret-1 != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var updateGerritDevStatusRet1Mock error
			mockey.Mock(rpc.UpdateGerritDevStatus).Return(updateGerritDevStatusRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var listGerritUnitsRet1MockItem0PtrValue aosp.GerritUnit
			listGerritUnitsRet1MockItem0 := &listGerritUnitsRet1MockItem0PtrValue
			listGerritUnitsRet1Mock := []*aosp.GerritUnit{listGerritUnitsRet1MockItem0,}
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var gerritSubmitRet1Mock error
			mockey.Mock(rpc.GerritSubmit).Return(gerritSubmitRet1Mock).Build()

			// prepare parameters
			var contextJson string
			receiver := GerritMergeTask{}
			ctx := context.Background()
			var paramsJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:GerritSubmit()_ret-1 != nil
	t.Run("case_4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			gerritSubmitRet1Mock := fmt.Errorf("error")
			mockey.Mock(rpc.GerritSubmit).Return(gerritSubmitRet1Mock).Build()

			var updateGerritDevStatusRet1Mock error
			mockey.Mock(rpc.UpdateGerritDevStatus).Return(updateGerritDevStatusRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var paramsJson string
			var contextJson string
			receiver := GerritMergeTask{}

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-2 == nil
	// len(code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-1) > 0
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/aosp.GerritUnit:GetRealTimeInfo()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-1[0] != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/aosp.GerritUnit:GetRealTimeInfo()_ret-1.Submittable != nil
	t.Run("case_5", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// mock function returns or global values
			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			listGerritUnitsRet1Mock := []*aosp.GerritUnit{}
			var listGerritUnitsRet1Mock0ItemPtrValue aosp.GerritUnit
			listGerritUnitsRet1Mock0Item := &listGerritUnitsRet1Mock0ItemPtrValue
			listGerritUnitsRet1Mock = append(listGerritUnitsRet1Mock, listGerritUnitsRet1Mock0Item)
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			var gerritUnitRealTimeInfoMockSubmittablePtrValue bool
			gerritUnitRealTimeInfoMock.Submittable = &gerritUnitRealTimeInfoMockSubmittablePtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var gerritSubmitRet1Mock error
			mockey.Mock(rpc.GerritSubmit).Return(gerritSubmitRet1Mock).Build()

			var updateGerritDevStatusRet1Mock error
			mockey.Mock(rpc.UpdateGerritDevStatus).Return(updateGerritDevStatusRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			// prepare parameters
			receiver := GerritMergeTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

func TestGerritMergeTask_StartAutoGen(t *testing.T) {
	// Verify the default behavior of the GerritMergeTask Start function.
	t.Run("testGerritMergeTask_StartDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var isEndRet1Mock bool
			var isEndRet2Mock interface{}
			var isEndRet3Mock interface{}
			var isEndRet4Mock error
			mockey.Mock(GerritMergeTask.IsEnd).Return(isEndRet1Mock, isEndRet2Mock, isEndRet3Mock, isEndRet4Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			// prepare parameters
			receiver := GerritMergeTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestGerritMergeTask_CloseAutoGen(t *testing.T) {
	// Verify the default behavior of the Close function.
	t.Run("testGerritMergeTask_CloseDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := GerritMergeTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3 := receiver.Close(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestGerritMergeTask_ResetAutoGen(t *testing.T) {
	// Verify the Reset function under default conditions.
	t.Run("testGerritMergeTask_ResetDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var contextJson string
			receiver := GerritMergeTask{}
			ctx := context.Background()
			var paramsJson string

			// run target function and assert
			got1, got2, got3 := receiver.Reset(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

