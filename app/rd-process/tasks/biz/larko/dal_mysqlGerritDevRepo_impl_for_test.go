package larko
import (
	"context"

	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_mysql"
)

type dal_mysqlGerritDevRepoImplForTestAutoGen struct{}

func (*dal_mysqlGerritDevRepoImplForTestAutoGen) Begin(param1 context.Context)(dal_mysql.GerritDevRepo, error) {
	var ret1ValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
	ret1Value := &ret1ValueImpl
	ret1 := ret1Value
	var ret2 error
	return ret1, ret2
}

func (*dal_mysqlGerritDevRepoImplForTestAutoGen) Commit(param1 context.Context)(*gorm.DB, error) {
	var ret1PtrValueConfigDialector gorm.Dialector
	ret1PtrValueConfig := gorm.Config{Dialector: ret1PtrValueConfigDialector}
	ret1PtrValue := gorm.DB{Config: &ret1PtrValueConfig}
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*dal_mysqlGerritDevRepoImplForTestAutoGen) Rollback(param1 context.Context)(*gorm.DB, error) {
	var ret1PtrValueConfigDialector gorm.Dialector
	ret1PtrValueConfig := gorm.Config{Dialector: ret1PtrValueConfigDialector}
	ret1PtrValue := gorm.DB{Config: &ret1PtrValueConfig}
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*dal_mysqlGerritDevRepoImplForTestAutoGen) DoTransaction(param1 context.Context, param2 func(ctx context.Context, inter dal_mysql.GerritDevRepo) error)error {
	var ret1 error
	return ret1
}

func (*dal_mysqlGerritDevRepoImplForTestAutoGen) CreateGerritDevPipeline(param1 context.Context, param2 *dal_mysql.GerritDevPipeline)error {
	var ret1 error
	return ret1
}

func (*dal_mysqlGerritDevRepoImplForTestAutoGen) ListGerritDevLatestPipeline(param1 context.Context, param2 int64, param3 string)(int64, error) {
	var ret1 int64
	var ret2 error
	return ret1, ret2
}

func (*dal_mysqlGerritDevRepoImplForTestAutoGen) UpdateGerritPipeline(param1 context.Context, param2 bool, param3 int64, param4 int64)error {
	var ret1 error
	return ret1
}

func (*dal_mysqlGerritDevRepoImplForTestAutoGen) GetGerritPipeline(param1 context.Context, param2 int64, param3 int64)(bool, error) {
	var ret1 bool
	var ret2 error
	return ret1, ret2
}
