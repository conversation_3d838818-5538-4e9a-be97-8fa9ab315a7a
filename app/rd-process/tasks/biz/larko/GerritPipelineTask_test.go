package larko

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_mysql"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bits/devops/pipeline_template"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/aosp"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/env"
	"code.byted.org/lang/gg/gresult"
)

func TestTriggerPipeline(t *testing.T) {
	t.SkipNow()
	
	ctx := context.Background()
	os.Setenv("RUNTIME_IDC_NAME", "boe")
	os.Setenv("BYTED_HOST_IPV6", "::1")
	os.Setenv("RUNTIME_UNIT_NAME", "boecn")
	os.Setenv("CONSUL_HTTP_HOST", "***********")
	os.Setenv("CONSUL_HTTP_PORT", "2280")
	dal_mysql.Init()
	
	reqParams := Params{
		DevId: 12047333,
		AppId: 2020094297,
	}
	_, _, data, err := triggerPipeline(ctx, reqParams, aosp.GerritDevPipelineType_aosp)
	if err != nil {
		t.Error(data, err)
	}
}

func TestGerritPipelineTask_IsEndAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var mysqlNoRowsRet1Mock bool
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			// prepare parameters
			var paramsJson string
			var contextJson string
			receiver := GerritPipelineTask{}
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	// GerritDevOperatorGlobal != nil
	// code.byted.org/devinfra/hagrid/libs/common_lib/utils:MysqlNoRows()_ret-1 == true
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			mysqlNoRowsRet1Mock := true
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			// prepare parameters
			receiver := GerritPipelineTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	// GerritDevOperatorGlobal != nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var mysqlNoRowsRet1Mock bool
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			// prepare parameters
			var paramsJson string
			var contextJson string
			receiver := GerritPipelineTask{}
			ctx := context.Background()
			convey.So(func() { _, _, _, _ = receiver.IsEnd(ctx, paramsJson, contextJson) }, convey.ShouldPanic)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	// GerritDevOperatorGlobal != nil
	// code.byted.org/devinfra/hagrid/libs/common_lib/utils:MysqlNoRows()_ret-1 != true
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_mysql.GerritDevRepo:ListGerritDevLatestPipeline()_ret-2 != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			mysqlNoRowsRet1Mock := false
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var listGerritDevLatestPipelineRet1Mock int64
			listGerritDevLatestPipelineRet2Mock := fmt.Errorf("error")
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			// prepare parameters
			var contextJson string
			receiver := GerritPipelineTask{}
			ctx := context.Background()
			var paramsJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestGerritPipelineTask_ResetAutoGen(t *testing.T) {
	// Verify the behavior of the Reset function under default conditions.
	t.Run("testGerritPipelineTask_ResetDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var paramsJson string
			var contextJson string
			receiver := GerritPipelineTask{}
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3 := receiver.Reset(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func Test_triggerPipelineAutoGen(t *testing.T) {
	// Verify the scenario when rpc.GetGerritTargetInfo fails.
	t.Run("testTriggerPipeline_GetGerritTargetInfoFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var getAospBranchByGerritUnitsRet1Mock []*aosp.AospBranchUnits
			var getAospBranchByGerritUnitsRet2Mock error
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			var triggerPipelineTemplateRet2Mock error
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			getGerritTargetInfoRet2Mock := fmt.Errorf("error")
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			var gerritDevContextFormMock aosp.GerritDevContextForm
			var getDevContextByDevIdRet2Mock error
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var params Params
			var pipelineType aosp.GerritDevPipelineType

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

	// Verify the scenario when rpc.ListGerritUnits fails while rpc.GetGerritTargetInfo succeeds.
	t.Run("testTriggerPipeline_ListGerritUnitsFailure", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			listGerritUnitsRet2Mock := fmt.Errorf("error")
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var getAospBranchByGerritUnitsRet1Mock []*aosp.AospBranchUnits
			var getAospBranchByGerritUnitsRet2Mock error
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			var triggerPipelineTemplateRet2Mock error
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			var gerritDevContextFormMock aosp.GerritDevContextForm
			var getDevContextByDevIdRet2Mock error
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			var getGerritTargetInfoRet2Mock error
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			var params Params
			var pipelineType aosp.GerritDevPipelineType
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

	// Verify the scenario when sonic.MarshalString fails while rpc.GetGerritTargetInfo and rpc.ListGerritUnits succeed.
	t.Run("testTriggerPipeline_SonicMarshalStringFailure", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			var getGerritTargetInfoRet2Mock error
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			var triggerPipelineTemplateRet2Mock error
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var marshalStringRet1Mock string
			marshalStringRet2Mock := fmt.Errorf("error")
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var gerritDevContextFormMock aosp.GerritDevContextForm
			var getDevContextByDevIdRet2Mock error
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var getAospBranchByGerritUnitsRet1Mock []*aosp.AospBranchUnits
			var getAospBranchByGerritUnitsRet2Mock error
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var params Params
			var pipelineType aosp.GerritDevPipelineType

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the scenario when larko.GetDevContextByDevId fails while other functions succeed.
	t.Run("testTriggerPipeline_GetDevContextByDevIdFailure", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			var getGerritTargetInfoRet2Mock error
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			var triggerPipelineTemplateRet2Mock error
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var gerritDevContextFormMock aosp.GerritDevContextForm
			getDevContextByDevIdRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var getAospBranchByGerritUnitsRet1Mock []*aosp.AospBranchUnits
			var getAospBranchByGerritUnitsRet2Mock error
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var params Params
			var pipelineType aosp.GerritDevPipelineType

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the scenario when rpc.ListGerritUnits returns a non-empty list and its first item is not null.
	t.Run("testTriggerPipeline_ListGerritUnitsNotEmptyAndFirstItemNotNull", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			listGerritUnitsRet1Mock := []*aosp.GerritUnit{}
			var listGerritUnitsRet1Mock0ItemPtrValue aosp.GerritUnit
			listGerritUnitsRet1Mock0Item := &listGerritUnitsRet1Mock0ItemPtrValue
			listGerritUnitsRet1Mock = append(listGerritUnitsRet1Mock, listGerritUnitsRet1Mock0Item)
			listGerritUnitsRet1Mock[0].TargetBranch = "not "
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var gerritDevContextFormMock aosp.GerritDevContextForm
			var getDevContextByDevIdRet2Mock error
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			var triggerPipelineTemplateRet2Mock error
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			var getGerritTargetInfoRet2Mock error
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var getAospBranchByGerritUnitsRet1Mock []*aosp.AospBranchUnits
			var getAospBranchByGerritUnitsRet2Mock error
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var params Params
			var pipelineType aosp.GerritDevPipelineType

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the scenario when all functions succeed and param3 is 3.
	t.Run("testTriggerPipeline_AllFunctionsSucceed", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// mock function returns or global values
			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var gerritDevContextFormMock aosp.GerritDevContextForm
			var getDevContextByDevIdRet2Mock error
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			var getGerritTargetInfoRet2Mock error
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			var triggerPipelineTemplateRet2Mock error
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			var getAospBranchByGerritUnitsRet1Mock []*aosp.AospBranchUnits
			var getAospBranchByGerritUnitsRet2Mock error
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var params Params
			pipelineTypeAlias := int64(3)
			pipelineType := aosp.GerritDevPipelineType(pipelineTypeAlias)

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the scenario when rpc.GetAospBranchByGerritUnits fails while other functions succeed and param3 is 3.
	t.Run("testTriggerPipeline_GetAospBranchByGerritUnitsFailure", func(t *testing.T) {
		mockey.PatchConvey("case_6", t, func() {
			// mock function returns or global values
			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			var getGerritTargetInfoRet2Mock error
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var gerritDevContextFormMock aosp.GerritDevContextForm
			var getDevContextByDevIdRet2Mock error
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			var getAospBranchByGerritUnitsRet1Mock []*aosp.AospBranchUnits
			getAospBranchByGerritUnitsRet2Mock := fmt.Errorf("error")
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			var triggerPipelineTemplateRet2Mock error
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var params Params
			pipelineTypeAlias := int64(3)
			pipelineType := aosp.GerritDevPipelineType(pipelineTypeAlias)

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the scenario when rpc.TriggerPipelineTemplate fails while other functions succeed.
	t.Run("testTriggerPipeline_TriggerPipelineTemplateFailure", func(t *testing.T) {
		mockey.PatchConvey("case_7", t, func() {
			// mock function returns or global values
			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			triggerPipelineTemplateRet2Mock := fmt.Errorf("error")
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			var getGerritTargetInfoRet2Mock error
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var getAospBranchByGerritUnitsRet1Mock []*aosp.AospBranchUnits
			var getAospBranchByGerritUnitsRet2Mock error
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			var gerritDevContextFormMock aosp.GerritDevContextForm
			var getDevContextByDevIdRet2Mock error
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var params Params
			var pipelineType aosp.GerritDevPipelineType

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the scenario when rpc.TriggerPipelineTemplate succeeds and GerritDevOperatorGlobal is not null.
	t.Run("testTriggerPipeline_TriggerPipelineTemplateSucceed", func(t *testing.T) {
		mockey.PatchConvey("case_8", t, func() {
			// mock function returns or global values
			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			var getAospBranchByGerritUnitsRet1Mock []*aosp.AospBranchUnits
			var getAospBranchByGerritUnitsRet2Mock error
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var gerritDevContextFormMock aosp.GerritDevContextForm
			var getDevContextByDevIdRet2Mock error
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			var triggerPipelineTemplateRet2Mock error
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			var getGerritTargetInfoRet2Mock error
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var params Params
			var pipelineType aosp.GerritDevPipelineType

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

	// Verify the scenario when rpc.GetAospBranchByGerritUnits returns a non-empty list and its first item is not null while other conditions are met.
	t.Run("testTriggerPipeline_GetAospBranchByGerritUnitsNotEmptyAndFirstItemNotNull", func(t *testing.T) {
		mockey.PatchConvey("case_9", t, func() {
			// mock function returns or global values
			var triggerTemplateResponseMockPtrValue pipeline_template.TriggerTemplateResponse
			triggerTemplateResponseMock := &triggerTemplateResponseMockPtrValue
			var triggerPipelineTemplateRet2Mock error
			mockey.Mock(rpc.TriggerPipelineTemplate).Return(triggerTemplateResponseMock, triggerPipelineTemplateRet2Mock).Build()

			var getShadowBranchRet1Mock string
			mockey.Mock((*aosp.GerritUnitRealTimeInfo).GetShadowBranch, mockey.OptUnsafe).Return(getShadowBranchRet1Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var getBuildTypeByDevIdRet1Mock string
			mockey.Mock(GetBuildTypeByDevId).Return(getBuildTypeByDevIdRet1Mock).Build()

			var gerritTargetInfoMockPtrValue git_server.GerritTargetInfo
			gerritTargetInfoMock := &gerritTargetInfoMockPtrValue
			var getGerritTargetInfoRet2Mock error
			mockey.Mock(rpc.GetGerritTargetInfo).Return(gerritTargetInfoMock, getGerritTargetInfoRet2Mock).Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var gerritUnitRealTimeInfoMockPtrValue aosp.GerritUnitRealTimeInfo
			gerritUnitRealTimeInfoMock := &gerritUnitRealTimeInfoMockPtrValue
			mockey.Mock((*aosp.GerritUnit).GetRealTimeInfo, mockey.OptUnsafe).Return(gerritUnitRealTimeInfoMock).Build()

			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var gerritDevContextFormMock aosp.GerritDevContextForm
			var getDevContextByDevIdRet2Mock error
			mockey.Mock(GetDevContextByDevId).Return(gerritDevContextFormMock, getDevContextByDevIdRet2Mock).Build()

			getAospBranchByGerritUnitsRet1Mock := []*aosp.AospBranchUnits{}
			var getAospBranchByGerritUnitsRet1Mock0ItemPtrValue aosp.AospBranchUnits
			getAospBranchByGerritUnitsRet1Mock0Item := &getAospBranchByGerritUnitsRet1Mock0ItemPtrValue
			getAospBranchByGerritUnitsRet1Mock = append(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet1Mock0Item)
			var getAospBranchByGerritUnitsRet2Mock error
			mockey.Mock(rpc.GetAospBranchByGerritUnits).Return(getAospBranchByGerritUnitsRet1Mock, getAospBranchByGerritUnitsRet2Mock).Build()

			var isBoeRet1Mock bool
			mockey.Mock(env.IsBoe, mockey.OptUnsafe).Return(isBoeRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var createGerritDevPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "CreateGerritDevPipeline"), mockey.OptUnsafe).Return(createGerritDevPipelineRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var params Params
			pipelineTypeAlias := int64(2)
			pipelineType := aosp.GerritDevPipelineType(pipelineTypeAlias)

			// run target function and assert
			got1, got2, got3, got4 := triggerPipeline(ctx, params, pipelineType)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

func TestGerritPipelineTask_StartAutoGen(t *testing.T) {
	// Verify the behavior when GetParams fails in Start method.
	t.Run("testGerritPipelineTask_Start_GetParamsFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			// prepare parameters
			var paramsJson string
			var contextJson string
			receiver := GerritPipelineTask{}
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the behavior when GetParams succeeds in Start method.
	t.Run("testGerritPipelineTask_Start_GetParamsSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			// prepare parameters
			receiver := GerritPipelineTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
		})
	})

}

func Test_checkPipelineEndAutoGen(t *testing.T) {
	// Verify the function behavior under default condition.
	t.Run("testCheckPipelineEnd_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var status string

			// run target function and assert
			got1 := checkPipelineEnd(status)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

func TestGerritPipelineTask_CloseAutoGen(t *testing.T) {
	// Verify the default behavior of the Close function.
	t.Run("testGerritPipelineTask_CloseDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := GerritPipelineTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3 := receiver.Close(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func Test_checkPipelineFailedAutoGen(t *testing.T) {
	// Verify the function behavior under default condition.
	t.Run("testCheckPipelineFailed_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var status string

			// run target function and assert
			got1 := checkPipelineFailed(status)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

