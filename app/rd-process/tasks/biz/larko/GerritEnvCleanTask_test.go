package larko

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/aosp"
)

func TestGerritEnvCleanTask_CloseAutoGen(t *testing.T) {
	// Verify the default behavior of the Close function.
	t.Run("testGerritEnvCleanTask_CloseDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var paramsJson string
			var contextJson string
			receiver := GerritEnvCleanTask{}
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3 := receiver.Close(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestGerritEnvCleanTask_IsEndAutoGen(t *testing.T) {
	// Verify the default behavior of IsEnd function.
	t.Run("testGerritEnvCleanTask_IsEnd", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			// prepare parameters
			var paramsJson string
			var contextJson string
			receiver := GerritEnvCleanTask{}
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, true)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestGerritEnvCleanTask_StartAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var gerritDeleteBranchRet1Mock error
			mockey.Mock(rpc.GerritDeleteBranch).Return(gerritDeleteBranchRet1Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var obsDeleteShadowProjectRet1Mock error
			mockey.Mock(rpc.ObsDeleteShadowProject).Return(obsDeleteShadowProjectRet1Mock).Build()

			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			// prepare parameters
			receiver := GerritEnvCleanTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, true)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var gerritDeleteBranchRet1Mock error
			mockey.Mock(rpc.GerritDeleteBranch).Return(gerritDeleteBranchRet1Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var obsDeleteShadowProjectRet1Mock error
			mockey.Mock(rpc.ObsDeleteShadowProject).Return(obsDeleteShadowProjectRet1Mock).Build()

			// prepare parameters
			receiver := GerritEnvCleanTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, true)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-2 != nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var listGerritUnitsRet1Mock []*aosp.GerritUnit
			listGerritUnitsRet2Mock := fmt.Errorf("error")
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			var gerritDeleteBranchRet1Mock error
			mockey.Mock(rpc.GerritDeleteBranch).Return(gerritDeleteBranchRet1Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var obsDeleteShadowProjectRet1Mock error
			mockey.Mock(rpc.ObsDeleteShadowProject).Return(obsDeleteShadowProjectRet1Mock).Build()

			// prepare parameters
			receiver := GerritEnvCleanTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, true)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/larko:GetParams()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-2 == nil
	// len(code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-1) > 0
	// code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc:ListGerritUnits()_ret-1[0] != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var gerritDeleteBranchRet1Mock error
			mockey.Mock(rpc.GerritDeleteBranch).Return(gerritDeleteBranchRet1Mock).Build()

			var genShadowBranchNameRet1Mock string
			mockey.Mock(genShadowBranchName, mockey.OptUnsafe).Return(genShadowBranchNameRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var obsDeleteShadowProjectRet1Mock error
			mockey.Mock(rpc.ObsDeleteShadowProject).Return(obsDeleteShadowProjectRet1Mock).Build()

			listGerritUnitsRet1Mock := []*aosp.GerritUnit{}
			var listGerritUnitsRet1Mock0ItemPtrValue aosp.GerritUnit
			listGerritUnitsRet1Mock0Item := &listGerritUnitsRet1Mock0ItemPtrValue
			listGerritUnitsRet1Mock = append(listGerritUnitsRet1Mock, listGerritUnitsRet1Mock0Item)
			var listGerritUnitsRet2Mock error
			mockey.Mock(rpc.ListGerritUnits).Return(listGerritUnitsRet1Mock, listGerritUnitsRet2Mock).Build()

			// prepare parameters
			receiver := GerritEnvCleanTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, true)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestGerritEnvCleanTask_ResetAutoGen(t *testing.T) {
	// Verify the Reset function under default conditions.
	t.Run("testGerritEnvCleanTask_Reset", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := GerritEnvCleanTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3 := receiver.Reset(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

