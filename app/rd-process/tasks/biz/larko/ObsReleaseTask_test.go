package larko

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_mysql"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/lang/gg/gresult"
)

func TestObsReleaseTask_ResetAutoGen(t *testing.T) {
	// Verify the behavior of the Reset function under default conditions.
	t.Run("testObsReleaseTask_ResetDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := ObsReleaseTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3 := receiver.Reset(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestObsReleaseTask_StartAutoGen(t *testing.T) {
	// Verify the behavior when GetParams fails in Start method.
	t.Run("testObsReleaseTask_Start_GetParamsFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			// prepare parameters
			var contextJson string
			receiver := ObsReleaseTask{}
			ctx := context.Background()
			var paramsJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the behavior when GetParams succeeds in Start method.
	t.Run("testObsReleaseTask_Start_GetParamsSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			// prepare parameters
			receiver := ObsReleaseTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestObsReleaseTask_IsEndAutoGen(t *testing.T) {
	// Verify the scenario when GetParams fails.
	t.Run("testObsReleaseTask_GetParamsFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var mysqlNoRowsRet1Mock bool
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			// prepare parameters
			var contextJson string
			receiver := ObsReleaseTask{}
			ctx := context.Background()
			var paramsJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the scenario when GetParams succeeds and MysqlNoRows returns true.
	t.Run("testObsReleaseTask_GetParamsSuccessMysqlNoRowsTrue", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			mysqlNoRowsRet1Mock := true
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			// prepare parameters
			receiver := ObsReleaseTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the scenario when GetParams succeeds, MysqlNoRows returns false and ListGerritDevLatestPipeline fails.
	t.Run("testObsReleaseTask_GetParamsSuccessMysqlNoRowsFalseListGerritDevLatestPipelineFailure", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			mysqlNoRowsRet1Mock := false
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var listGerritDevLatestPipelineRet1Mock int64
			listGerritDevLatestPipelineRet2Mock := fmt.Errorf("error")
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			// prepare parameters
			var contextJson string
			receiver := ObsReleaseTask{}
			ctx := context.Background()
			var paramsJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestObsReleaseTask_CloseAutoGen(t *testing.T) {
	// Verify the default behavior of the Close function.
	t.Run("testObsReleaseTask_CloseDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			var paramsJson string
			var contextJson string
			receiver := ObsReleaseTask{}

			// run target function and assert
			got1, got2, got3 := receiver.Close(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

