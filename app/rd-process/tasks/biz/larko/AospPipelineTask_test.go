package larko

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_mysql"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/aosp"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/lang/gg/gresult"
)

func TestAospPipelineTask_StartAutoGen(t *testing.T) {
	// Verify the behavior when GetParams fails.
	t.Run("testAospPipelineTask_Start_GetParamsFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var gerritDevPipelineTypeMock aosp.GerritDevPipelineType
			var getPipelineTypeRet2Mock error
			mockey.Mock(GetPipelineType).Return(gerritDevPipelineTypeMock, getPipelineTypeRet2Mock).Build()

			// prepare parameters
			var paramsJson string
			var contextJson string
			receiver := AospPipelineTask{}
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the behavior when GetPipelineType fails while GetParams succeeds.
	t.Run("testAospPipelineTask_Start_GetPipelineTypeFailure", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var gerritDevPipelineTypeMock aosp.GerritDevPipelineType
			getPipelineTypeRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetPipelineType).Return(gerritDevPipelineTypeMock, getPipelineTypeRet2Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			// prepare parameters
			receiver := AospPipelineTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the successful execution of the Start function.
	t.Run("testAospPipelineTask_Start_Success", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var gerritDevPipelineTypeMock aosp.GerritDevPipelineType
			var getPipelineTypeRet2Mock error
			mockey.Mock(GetPipelineType).Return(gerritDevPipelineTypeMock, getPipelineTypeRet2Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			// prepare parameters
			receiver := AospPipelineTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

func TestAospPipelineTask_ResetAutoGen(t *testing.T) {
	// Verify the Reset function under default conditions.
	t.Run("testAospPipelineTask_ResetDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := AospPipelineTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3 := receiver.Reset(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestAospPipelineTask_CloseAutoGen(t *testing.T) {
	// Verify the behavior of the Close function under default conditions.
	t.Run("testAospPipelineTask_CloseDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := AospPipelineTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3 := receiver.Close(ctx, paramsJson, contextJson)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestAospPipelineTask_IsEndAutoGen(t *testing.T) {
	// Verify the behavior when GetParams fails.
	t.Run("testAospPipelineTask_IsEnd_GetParamsFail", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			var gerritDevPipelineTypeMock aosp.GerritDevPipelineType
			var getPipelineTypeRet2Mock error
			mockey.Mock(GetPipelineType).Return(gerritDevPipelineTypeMock, getPipelineTypeRet2Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var mysqlNoRowsRet1Mock bool
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var paramsJson string
			var contextJson string
			receiver := AospPipelineTask{}

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the behavior when GetPipelineType fails and GetParams succeeds.
	t.Run("testAospPipelineTask_IsEnd_GetPipelineTypeFail", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var mysqlNoRowsRet1Mock bool
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var gerritDevPipelineTypeMock aosp.GerritDevPipelineType
			getPipelineTypeRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetPipelineType).Return(gerritDevPipelineTypeMock, getPipelineTypeRet2Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			// prepare parameters
			receiver := AospPipelineTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the panic situation when GetPipelineType and GetParams succeed.
	t.Run("testAospPipelineTask_IsEnd_GetPipelineTypeAndParamsSucceedButPanic", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var gerritDevPipelineTypeMock aosp.GerritDevPipelineType
			var getPipelineTypeRet2Mock error
			mockey.Mock(GetPipelineType).Return(gerritDevPipelineTypeMock, getPipelineTypeRet2Mock).Build()

			var mysqlNoRowsRet1Mock bool
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			// prepare parameters
			receiver := AospPipelineTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string
			convey.So(func() { _, _, _, _ = receiver.IsEnd(ctx, paramsJson, contextJson) }, convey.ShouldPanic)
		})
	})

	// Verify the behavior when GetPipelineType and GetParams succeed and MysqlNoRows is true.
	t.Run("testAospPipelineTask_IsEnd_GetPipelineTypeAndParamsSucceedAndMysqlNoRowsTrue", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			mysqlNoRowsRet1Mock := true
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var gerritDevPipelineTypeMock aosp.GerritDevPipelineType
			var getPipelineTypeRet2Mock error
			mockey.Mock(GetPipelineType).Return(gerritDevPipelineTypeMock, getPipelineTypeRet2Mock).Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var paramsJson string
			var contextJson string
			receiver := AospPipelineTask{}

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the behavior when GetPipelineType and GetParams succeed, MysqlNoRows is false and ListGerritDevLatestPipeline fails.
	t.Run("testAospPipelineTask_IsEnd_GetPipelineTypeAndParamsSucceedAndMysqlNoRowsFalseAndListGerritDevLatestPipelineFail", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			mysqlNoRowsRet1Mock := false
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			var gerritDevPipelineTypeMock aosp.GerritDevPipelineType
			var getPipelineTypeRet2Mock error
			mockey.Mock(GetPipelineType).Return(gerritDevPipelineTypeMock, getPipelineTypeRet2Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var stringRet1Mock string
			mockey.Mock(aosp.GerritDevPipelineType.String).Return(stringRet1Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			listGerritDevLatestPipelineRet2Mock := fmt.Errorf("error")
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			// prepare parameters
			var paramsJson string
			var contextJson string
			receiver := AospPipelineTask{}
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

}

