package larko

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_mysql"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/lang/gg/gresult"
)

func TestStart(t *testing.T) {
	t.SkipNow()
	
	ctx := context.Background()
	os.Setenv("RUNTIME_IDC_NAME", "boe")
	os.Setenv("BYTED_HOST_IPV6", "::1")
	os.Setenv("RUNTIME_UNIT_NAME", "boecn")
	os.Setenv("CONSUL_HTTP_HOST", "***********")
	os.Setenv("CONSUL_HTTP_PORT", "2280")
	dal_mysql.Init()
	qaCheck := QACheckTask{}
	reqJson := `
	{
	    "dev_id":12047333,
	    "app_id": 2020094297
	}
	`
	
	_, _, data, err := qaCheck.Start(ctx, reqJson, "")
	if err != nil {
		t.Error(data, err)
	}
	_, _, data, err = qaCheck.IsEnd(ctx, reqJson, "")
	if err != nil {
		t.Error(data, err)
	}
}

func TestQACheckTask_CloseAutoGen(t *testing.T) {
	// Verify the QACheckTask Close function under default conditions.
	t.Run("testQACheckTask_CloseDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			var paramsJson string
			var contextJson string
			receiver := QACheckTask{}

			// run target function and assert
			got1, got2, got3 := receiver.Close(ctx, paramsJson, contextJson)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestQACheckTask_ResetAutoGen(t *testing.T) {
	// Verify the Reset function under default conditions.
	t.Run("testQACheckTask_ResetDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := QACheckTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3 := receiver.Reset(ctx, paramsJson, contextJson)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestQACheckTask_StartAutoGen(t *testing.T) {
	// Verify the behavior when GetParams fails in Start method.
	t.Run("testQACheckTask_Start_GetParamsFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			// prepare parameters
			receiver := QACheckTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the behavior when GetParams succeeds in Start method.
	t.Run("testQACheckTask_Start_GetParamsSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			// prepare parameters
			receiver := QACheckTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.Start(ctx, paramsJson, contextJson)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestQACheckTask_IsEndAutoGen(t *testing.T) {
	// Verify the behavior when GetParams fails.
	t.Run("testQACheckTask_IsEnd_GetParamsFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var mysqlNoRowsRet1Mock bool
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var gerritDevOperatorMockValueImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMockValue := &gerritDevOperatorMockValueImpl
			gerritDevOperatorMock := gerritDevOperatorMockValue
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var paramsMock Params
			getParamsRet2Mock := fmt.Errorf("error")
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			// prepare parameters
			receiver := QACheckTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the panic situation when GetParams succeeds and GerritDevOperator is not null.
	t.Run("testQACheckTask_IsEnd_GetParamsSuccess_GerritDevOperatorNotNull_Panic", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var mysqlNoRowsRet1Mock bool
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			// prepare parameters
			receiver := QACheckTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string
			convey.So(func() { _, _, _, _ = receiver.IsEnd(ctx, paramsJson, contextJson) }, convey.ShouldPanic)
		})
	})

	// Verify the behavior when GetParams succeeds, GerritDevOperator is not null and MysqlNoRows returns true.
	t.Run("testQACheckTask_IsEnd_GetParamsSuccess_GerritDevOperatorNotNull_MysqlNoRowsTrue", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			mysqlNoRowsRet1Mock := true
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			var listGerritDevLatestPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			// prepare parameters
			receiver := QACheckTask{}
			ctx := context.Background()
			var paramsJson string
			var contextJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got4 == nil, convey.ShouldBeTrue)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the behavior when GetParams succeeds, GerritDevOperator is not null, MysqlNoRows returns false and ListGerritDevLatestPipeline fails.
	t.Run("testQACheckTask_IsEnd_GetParamsSuccess_GerritDevOperatorNotNull_MysqlNoRowsFalse_ListGerritDevLatestPipelineFailure", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var checkPipelineEndRet1Mock bool
			mockey.Mock(checkPipelineEnd, mockey.OptUnsafe).Return(checkPipelineEndRet1Mock).Build()

			larkoCtxEnvRet1Mock := context.Background()
			mockey.Mock(LarkoCtxEnv).Return(larkoCtxEnvRet1Mock).Build()

			mysqlNoRowsRet1Mock := false
			mockey.Mock(utils.MysqlNoRows).Return(mysqlNoRowsRet1Mock).Build()

			mockey.Mock(rpc.GerritCardNotify).Return().Build()

			var gerritDevOperatorMockImpl dal_mysqlGerritDevRepoImplForTestAutoGen
			gerritDevOperatorMock := &gerritDevOperatorMockImpl
			mockey.MockValue(&dal_mysql.GerritDevOperator).To(gerritDevOperatorMock)

			var rMock gresult.R[int]
			mockey.Mock(rpc.GetPipelineStatus).Return(rMock).Build()

			mockey.Mock(AsyncWebsocketGerritGraph).Return().Build()

			var paramsMock Params
			var getParamsRet2Mock error
			mockey.Mock(GetParams).Return(paramsMock, getParamsRet2Mock).Build()

			var checkPipelineFailedRet1Mock bool
			mockey.Mock(checkPipelineFailed, mockey.OptUnsafe).Return(checkPipelineFailedRet1Mock).Build()

			var triggerPipelineRet1Mock bool
			var triggerPipelineRet2Mock interface{}
			var triggerPipelineRet3Mock interface{}
			var triggerPipelineRet4Mock error
			mockey.Mock(triggerPipeline).Return(triggerPipelineRet1Mock, triggerPipelineRet2Mock, triggerPipelineRet3Mock, triggerPipelineRet4Mock).Build()

			var listGerritDevLatestPipelineRet1Mock int64
			listGerritDevLatestPipelineRet2Mock := fmt.Errorf("error")
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "ListGerritDevLatestPipeline"), mockey.OptUnsafe).Return(listGerritDevLatestPipelineRet1Mock, listGerritDevLatestPipelineRet2Mock).Build()

			var getGerritPipelineRet1Mock bool
			var getGerritPipelineRet2Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "GetGerritPipeline"), mockey.OptUnsafe).Return(getGerritPipelineRet1Mock, getGerritPipelineRet2Mock).Build()

			var updateGerritPipelineRet1Mock error
			mockey.Mock(mockey.GetMethod(dal_mysql.GerritDevOperator, "UpdateGerritPipeline"), mockey.OptUnsafe).Return(updateGerritPipelineRet1Mock).Build()

			// prepare parameters
			var contextJson string
			receiver := QACheckTask{}
			ctx := context.Background()
			var paramsJson string

			// run target function and assert
			got1, got2, got3, got4 := receiver.IsEnd(ctx, paramsJson, contextJson)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got3 == nil, convey.ShouldBeTrue)
			convey.So(got4 == nil, convey.ShouldBeFalse)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

