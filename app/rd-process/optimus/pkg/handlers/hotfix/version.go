package hotfix

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/hotfix"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/git"
	"context"
	"errors"
	"fmt"
	json "github.com/bytedance/sonic"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bits_optimus_infra/kitex_gen/bits/optimus/infra"
	"code.byted.org/overpass/bits_optimus_infra/rpc/bits_optimus_infra"
	"code.byted.org/overpass/bits_release_hotfix/rpc/bits_release_hotfix"

	cd_hotfix "code.byted.org/overpass/bits_release_hotfix/kitex_gen/bits/release/hotfix"
)

func DeleteVersion(ctx context.Context, versionID int64) error {
	version, err := data.GetHotfixVersionByID(ctx, versionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get version by id %d: %s", versionID, err.Error())
		return err
	}
	if version == nil {
		logs.CtxWarn(ctx, "version %d not found", versionID)
		return nil
	}
	task, err := data.GetHotfixTaskByID(ctx, version.HotfixTaskID, true)
	if err != nil {
		logs.CtxError(ctx, "failed to get task by id %d: %s", version.HotfixTaskID, err.Error())
		return err
	}
	if task == nil {
		logs.CtxWarn(ctx, "task %d not found", version.HotfixTaskID)
		return errors.New("task not found")
	}
	// delete branch
	if version.Branch != "" {
		res, err := rpc.GitClient.DeleteBranch(ctx, &git_server.DeleteBranchRequest{
			ProjectId: int32(version.MainProjectID),
			Branch:    version.Branch,
		})
		if err != nil {
			logs.CtxError(ctx, "failed to delete branch:%s", err.Error())
			if !strings.Contains(err.Error(), "Branch Not Found") {
				return err
			}
		} else {
			if !res.Success {
				logs.CtxError(ctx, "failed to delete branch")
				return errors.New("failed to delete branch")
			}
		}
	}
	if version.TargetBranch != "" {
		res, err := rpc.GitClient.DeleteBranch(ctx, &git_server.DeleteBranchRequest{
			ProjectId: int32(version.MainProjectID),
			Branch:    version.TargetBranch,
		})
		if err != nil {
			logs.CtxError(ctx, "failed to delete target branch:%s", err.Error())
			if !strings.Contains(err.Error(), "Branch Not Found") {
				return err
			}
		} else {
			if !res.Success {
				logs.CtxError(ctx, "failed to delete target branch")
				return errors.New("failed to delete branch")
			}
		}
	}
	// delete modules
	repos, err := data.GetHotfixRepoByVersionID(ctx, versionID)
	if err != nil {
		return err
	}
	wg := sync.WaitGroup{}
	var globalErr error
	for _, repo := range repos {
		wg.Add(1)
		go func(id int64) {
			defer wg.Done()
			err := DeleteRepo(ctx, id)
			if err != nil {
				logs.CtxError(ctx, "failed to delete repo:%s", err.Error())
				globalErr = err
				return
			}
		}(repo.ID)
	}
	wg.Wait()
	if globalErr != nil {
		return globalErr
	}
	// delete record
	tx, err := data.DeleteHotFixVersion(ctx, version)
	if err != nil {
		logs.CtxError(ctx, "failed to delete version %d: %s", versionID, err.Error())
		return err
	}
	_, err = bits_optimus_infra.RemoveDevDependencies(ctx, task.DevID, []*infra.Dependency{{Id: version.ID, Type: infra.DependencyType_HotFixVersion}})
	if err != nil {
		logs.CtxError(ctx, "failed to remove dev dependencies: %s", err.Error())
		tx.Rollback()
		return err
	}
	publishIDs, err := data.GetHotfixPublishIDList(ctx, versionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish id list: %s", err.Error())
		tx.Rollback()
		return err
	}
	// Terminate all publish
	wg.Add(len(publishIDs))
	for _, publishID := range publishIDs {
		go func(publishID int64) {
			defer wg.Done()
			err := TerminatePublish(ctx, publishID, "bits")
			if err != nil {
				logs.CtxError(ctx, "failed to terminal publish %d: %s", publishID, err.Error())
				globalErr = err
			}
			logs.CtxInfo(ctx, "terminated publish %d", publishID)
		}(publishID)
	}
	wg.Wait()
	if globalErr != nil {
		tx.Rollback()
		return globalErr
	}
	if err := tx.Commit().Error; err != nil {
		logs.CtxError(ctx, "failed to commit delete version %d: %s", versionID, err.Error())
		return err
	}
	return nil
}

func CreateHotFixVersion(ctx context.Context, req *hotfix.CreateVersionDTO) (resp *hotfix.CreateVersionResp, retErr error) {
	task, err := data.GetHotfixTaskByID(ctx, req.TaskID, true)
	if err != nil {
		logs.CtxError(ctx, "failed to get task:%s", err.Error())
		return nil, err
	}
	if task == nil {
		logs.CtxError(ctx, "task not found:%d", req.TaskID)
		return nil, errors.New("task not found")
	}
	if int(task.HotfixType) == int(cd_hotfix.HotfixType_IOSSDKHotfix) { //sdk热修走自己逻辑
		return createHotfixVersionForSDK(ctx, req, task)
	}
	bitsAppID := int64(0)
	if req.BitsAppID != nil {
		bitsAppID = *req.BitsAppID
	} else {
		bitsAppID = task.BitsAppID
	}
	// 要发布宿主的app信息
	appInfo, err := rpc.MetaClient.QueryAppInfoById(ctx, &meta.QueryAppInfoByIdRequest{AppId: bitsAppID})
	if err != nil {
		logs.CtxError(ctx, "failed to get app info:%s", err.Error())
		return nil, err
	}
	// 基于哪个bits app下进行的热修
	baseAppInfo, err := rpc.MetaClient.QueryAppInfoById(ctx, &meta.QueryAppInfoByIdRequest{AppId: task.BitsAppID})
	if err != nil {
		logs.CtxError(ctx, "failed to get app info:%s", err.Error())
		return nil, err
	}

	if exist, _ := data.GetHotfixVersionByTaskIDAndLongVersionAndAppID(ctx, req.TaskID, bitsAppID, req.VersionLong); exist != nil {
		return nil, errors.New("hotfix version exist")
	}
	projectInfo, err := rpc.GitClient.GetProjectByRepo(ctx, &git_server.GetProjectByRepoRequest{RepoUrl: appInfo.AppInfo.GitUrl})
	if err != nil {
		logs.CtxError(ctx, "failed to get project info:%s", err.Error())
		return nil, err
	}
	if projectInfo == nil || projectInfo.Project == nil {
		logs.CtxError(ctx, "failed to get project info")
		return nil, errors.New("failed to get project info")
	}
	pid := projectInfo.Project.Id
	// flutter的branchName和commitID可以为空，因为其发布流程页展示的是mr信息
	// iOSd的branchName和commitID需要从bits_release_hotfix取出信息并计算
	patchBranch := ""
	commitID := ""
	targetBranch := ""
	// flutter从mr里取branchName和commitID
	if baseAppInfo.AppInfo.TechnologyStack == meta.TechnologyStack_Flutter {
		//task.MrID
	}
	// 如果是flutter动态包，则无需创建热修path分支
	if baseAppInfo.AppInfo.TechnologyStack == meta.TechnologyStack_iOS || baseAppInfo.AppInfo.TechnologyStack == meta.TechnologyStack_Android {
		commitInfo, err := bits_release_hotfix.HotfixQueryCommitID(ctx, task.BitsAppID, req.VersionLong)
		if err != nil {
			logs.CtxError(ctx, "failed to get commit info:%s", err.Error())
			return nil, err
		}
		if len(commitInfo.CommitId) == 0 {
			logs.CtxError(ctx, "commit info not found:%s", req.VersionLong)
			return nil, errors.New("commit info not found")
		}
		commitID = commitInfo.CommitId
		now := time.Now()
		hash := model.GenerateVersionHash(task.ID, req.VersionLong, now)
		patchBranch = fmt.Sprintf("bits_patch/%d_%s_%s", task.ID, req.VersionLong, hash[:6])
		targetBranch = fmt.Sprintf("bits_patch_base/%d_%s_%s", task.ID, req.VersionLong, hash[:6])
		defer func() {
			if retErr != nil {
				_, _ = rpc.GitClient.DeleteBranch(ctx, &git_server.DeleteBranchRequest{ProjectId: pid, Branch: patchBranch})
				_, _ = rpc.GitClient.DeleteBranch(ctx, &git_server.DeleteBranchRequest{ProjectId: pid, Branch: targetBranch})
			}
		}()
		logs.CtxInfo(ctx, "current time stamp:%d, patchBranch:%s,targetBranch:%s", now.Unix(), patchBranch, targetBranch)
		err = git.CreateBranch(ctx, int64(pid), patchBranch, commitInfo.CommitId)
		if err != nil {
			logs.CtxError(ctx, "failed to create branch:%s", err.Error())
			return nil, err
		}
		err = git.CreateBranch(ctx, int64(pid), targetBranch, commitInfo.CommitId)
		if err != nil {
			logs.CtxError(ctx, "failed to create branch:%s", err.Error())
			return nil, err
		}
	}

	version, err := data.CreateHotfixVersion(ctx, req.Version, req.VersionLong, bitsAppID, req.TaskID, int64(pid), patchBranch, targetBranch, commitID, req.Author)
	if err != nil {
		logs.CtxError(ctx, "failed to create version:%s", err.Error())
		return nil, err
	}
	_, err = bits_optimus_infra.RawCall.AddDevDepRelations(ctx, &infra.AddDevDepRelationsRequest{
		DevId:        task.DevID,
		Dependencies: []*infra.Dependency{{Id: version.ID, Type: infra.DependencyType_HotFixVersion, Role: infra.DependencyRole_Host}},
		Relations:    nil,
		Base:         nil,
	})
	if err != nil {
		logs.CtxError(ctx, "failed to create dev dep:%s", err.Error())
		return nil, err
	}
	go func() { //发送消息卡片
		_, err := bits_release_hotfix.RawCall.SendCreateVersionCard(ctx, &cd_hotfix.SendCreateVersionCardReq{
			Version:     req.Version,
			VersionLong: req.VersionLong,
			CreateTime:  version.CreateAt.Unix(),
			Creator:     req.Author,
			TaskId:      task.ID,
			BitsAppId:   &bitsAppID,
		})
		if err != nil {
			logs.CtxError(ctx, "failed to SendCreateVersionCard:%s", err.Error())
		}
	}()

	go func() { //创建onesite 资源
		err = CreateOnesiteResource(ctx, ResourceType_HotfixVersion, strconv.Itoa(int(version.ID)), strconv.Itoa(int(task.ID)), version.Author)
		if err != nil {
			logs.CtxError(ctx, "failed to create onesite resource:%s", err.Error())
		}
	}()
	return &hotfix.CreateVersionResp{
		VersionID: version.ID,
		Branch: &version.Branch,
	}, nil
}

func createHotfixVersionForSDK(ctx context.Context, req *hotfix.CreateVersionDTO, task *model.HotfixTask) (resp *hotfix.CreateVersionResp, retErr error) {
	if req.BitsAppID == nil { //下发宿主的app id为空返回错误
		logs.CtxError(ctx, "can not get host bits app id from request")
		return nil, errors.New("can not get host bits app id")
	}
	// 已存在宿主版本，返回错误
	if exist, _ := data.GetHotfixVersionByTaskIDAndLongVersionAndAppID(ctx, req.TaskID, *req.BitsAppID, req.VersionLong); exist != nil {
		return nil, errors.New("hotfix version exist")
	}
	//获取sdk的信息
	baseAppInfo, err := rpc.MetaClient.QueryAppInfoById(ctx, &meta.QueryAppInfoByIdRequest{AppId: task.BitsAppID})
	if err != nil {
		logs.CtxError(ctx, "failed to get app info:%s", err.Error())
		return nil, err
	}
	//获取sdk仓库信息
	projectInfo, err := rpc.GitClient.GetProjectByRepo(ctx, &git_server.GetProjectByRepoRequest{RepoUrl: baseAppInfo.AppInfo.GitUrl})
	if err != nil {
		logs.CtxError(ctx, "failed to get project info:%s", err.Error())
		return nil, err
	}
	if projectInfo == nil || projectInfo.Project == nil {
		logs.CtxError(ctx, "failed to get project info")
		return nil, errors.New("failed to get project info")
	}
	pid := projectInfo.Project.Id
	commitID := task.SDKCommitID
	patchBranch := ""
	targetBranch := ""
	//找之前版本中创建过的patch分支
	versions, err := data.GetHotfixVersionByTaskID(ctx, task.ID)
	if err != nil {
		logs.CtxError(ctx, "failed to get hotfix version by task id: %v", task.ID)
		return nil, errors.New("failed to get hotfix version by task id")
	}
	for _, version := range versions {
		if version.Branch != "" && version.TargetBranch != "" {
			patchBranch = version.Branch
			targetBranch = version.TargetBranch
			break
		}
	}
	if patchBranch == "" && targetBranch == "" { //之前版本未创建过patch分支则创建
		now := time.Now()
		hash := model.GenerateVersionHash(task.ID, req.VersionLong, now)
		patchBranch = fmt.Sprintf("bits_patch/%v_%v_%v", "sdk_hotfix", task.ID, hash[:6])
		targetBranch = fmt.Sprintf("bits_patch_base/%v_%v_%v", "sdk_hotfix", task.ID, hash[:6])
		defer func() {
			if retErr != nil {
				_, _ = rpc.GitClient.DeleteBranch(ctx, &git_server.DeleteBranchRequest{ProjectId: pid, Branch: patchBranch})
				_, _ = rpc.GitClient.DeleteBranch(ctx, &git_server.DeleteBranchRequest{ProjectId: pid, Branch: targetBranch})
			}
		}()
		logs.CtxInfo(ctx, "current time stamp:%d, patchBranch:%s,targetBranch:%s", now.Unix(), patchBranch, targetBranch)
		err = git.CreateBranch(ctx, int64(pid), patchBranch, commitID)
		if err != nil {
			logs.CtxError(ctx, "failed to create branch:%s", err.Error())
			return nil, err
		}
		err = git.CreateBranch(ctx, int64(pid), targetBranch, commitID)
		if err != nil {
			logs.CtxError(ctx, "failed to create branch:%s", err.Error())
			return nil, err
		}
	}
	//插入数据库
	version, err := data.CreateHotfixVersion(ctx, req.Version, req.VersionLong, *req.BitsAppID, req.TaskID, int64(pid), patchBranch, targetBranch, commitID, req.Author)
	if err != nil {
		logs.CtxError(ctx, "failed to create version:%s", err.Error())
		return nil, err
	}
	_, err = bits_optimus_infra.RawCall.AddDevDepRelations(ctx, &infra.AddDevDepRelationsRequest{
		DevId:        task.DevID,
		Dependencies: []*infra.Dependency{{Id: version.ID, Type: infra.DependencyType_HotFixVersion, Role: infra.DependencyRole_Host}},
		Relations:    nil,
		Base:         nil,
	})
	if err != nil {
		logs.CtxError(ctx, "failed to create dev dep:%s", err.Error())
		return nil, err
	}
	go func() { //发送消息卡片
		_, err := bits_release_hotfix.RawCall.SendCreateVersionCard(ctx, &cd_hotfix.SendCreateVersionCardReq{
			Version:     req.Version,
			VersionLong: req.VersionLong,
			CreateTime:  version.CreateAt.Unix(),
			Creator:     req.Author,
			TaskId:      task.ID,
			BitsAppId:   req.BitsAppID,
		})
		if err != nil {
			logs.CtxError(ctx, "failed to SendCreateVersionCard:%s", err.Error())
		}
	}()
	go func() { //创建onesite 资源
		err = CreateOnesiteResource(ctx, ResourceType_HotfixVersion, strconv.Itoa(int(version.ID)), strconv.Itoa(int(task.ID)), version.Author)
		if err != nil {
			logs.CtxError(ctx, "failed to create onesite resource:%s", err.Error())
		}
	}()
	return &hotfix.CreateVersionResp{
		VersionID: version.ID,
		Branch: &version.Branch,
	}, nil
}

func GenerateMboxLink(ctx context.Context, versionID int64) ([]*hotfix.MboxRepoItem, error) {
	// 主仓
	version, err := data.GetHotfixVersionByID(ctx, versionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get version:%s", err.Error())
		return nil, err
	}
	mainProjectInfo, err := data.GetConfigProjectInfoByProjectID(ctx, version.MainProjectID, true)
	if err != nil {
		logs.CtxError(ctx, "failed to get project info:%s", err.Error())
		return nil, err
	}
	ans := make([]*hotfix.MboxRepoItem, 0)
	ans = append(ans, &hotfix.MboxRepoItem{
		Name:         mainProjectInfo.Name,
		SourceBranch: version.Branch,
		TargetBranch: version.TargetBranch,
		GitUrl:       mainProjectInfo.GitRepoAddr,
		Modules:      make([]string, 0),
	})
	repos, err := data.GetHotfixRepoByVersionID(ctx, versionID)
	if err != nil {
		return nil, err
	}
	if len(repos) == 0 {
		return ans, nil
	}
	for _, repo := range repos {
		modules := make([]string, 0)
		err := json.Unmarshal([]byte(repo.Modules), &modules)
		if err != nil {
			logs.CtxError(ctx, "failed to unmarshal modules list:%s,%s", repo.Modules, err.Error())
			return nil, err
		}
		projectInfo, err := data.GetConfigProjectInfoByProjectID(ctx, repo.ProjectID, true)
		if err != nil {
			logs.CtxError(ctx, "failed to get project info:%s", err.Error())
			return nil, err
		}
		ans = append(ans, &hotfix.MboxRepoItem{
			Name:         repo.RepoName,
			Modules:      modules,
			TargetBranch: repo.TargetBranch,
			SourceBranch: repo.Branch,
			GitUrl:       projectInfo.GitRepoAddr,
		})
	}
	return ans, nil
}
