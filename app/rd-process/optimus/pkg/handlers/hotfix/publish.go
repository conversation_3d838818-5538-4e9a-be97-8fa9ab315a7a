package hotfix

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/hotfix"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/handlers/merge_request"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/gitlab"
	hotfixService "code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/hotfix"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/utils"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/git"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bits_optimus_infra/kitex_gen/bits/optimus/infra"
	"code.byted.org/overpass/bits_optimus_infra/rpc/bits_optimus_infra"
	cd_hotfix "code.byted.org/overpass/bits_release_hotfix/kitex_gen/bits/release/hotfix"
	release "code.byted.org/overpass/bits_release_hotfix/kitex_gen/bits/release/hotfix"
	"code.byted.org/overpass/bits_release_hotfix/rpc/bits_release_hotfix"
	meta3 "code.byted.org/overpass/bytedance_bits_meta/kitex_gen/bytedance/bits/meta"
	"code.byted.org/overpass/bytedance_bits_meta/rpc/bytedance_bits_meta"
	"context"
	"errors"
	"fmt"
	json "github.com/bytedance/sonic"
	"strconv"
	"strings"
	"sync"
)

func createFlutterPublish(ctx context.Context, req *hotfix.CreatePublishReq) (*hotfix.CreatePublishResp, error) {
	logs.CtxDebug(ctx, "start to create flutter publish %v", req)
	devInfo, err := bits_optimus_infra.GetDevForDep(ctx, &infra.Dependency{Id: req.VersionID, Type: infra.DependencyType_HotFixVersion, Role: infra.DependencyRole_Host}, false)
	if err != nil {
		logs.CtxError(ctx, "[Flutter]failed to get dev:%s", err.Error())
		return nil, err
	}
	if devInfo.Dev == nil {
		logs.CtxError(ctx, "dev not found")
		return nil, errors.New("dev not found")
	}
	version, err := data.GetHotfixVersionByID(ctx, req.VersionID)
	if err != nil {
		logs.CtxError(ctx, "[Flutter]failed to get hotfix version:%s", err.Error())
		return nil, err
	}
	if version == nil {
		logs.CtxError(ctx, "[Flutter]hotfix version not found")
		return nil, errors.New("[Flutter]hotfix version not found")
	}
	taskInfo, _ := data.GetHotfixTaskByID(ctx, version.HotfixTaskID, true)
	if taskInfo == nil || taskInfo.MrID == 0 {
		logs.CtxError(ctx, "[Flutter]hotfix version not found")
		return nil, errors.New("[Flutter]hotfix task not found")
	}
	mrInfo, err := merge_request.GetMrMainInfo(ctx, &optimus.GetMrMainInfoQuery{
		MrID: int64(taskInfo.MrID),
	})
	if err != nil || mrInfo == nil || mrInfo.Info == nil {
		logs.CtxError(ctx, "[Flutter]mrInfo not found, err: %", err)
		return nil, errors.New("[Flutter]mrInfo not found")
	}
	tx, pub, err := data.CreateHotfixPublishTx(ctx, req.VersionID, req.Author, "", "", mrInfo.Info.ProjectID, mrInfo.Info.IID)
	if err != nil {
		logs.CtxError(ctx, "[Flutter]failed to create hotfix publish:%s", err.Error())
		tx.Rollback()
		return nil, err
	}
	err = hotfixService.InvokeStateMachine(ctx, pub.ID, hotfixService.MachineTaskID_Flutter)
	if err != nil {
		logs.CtxError(ctx, "failed to invoke state machine")
		tx.Rollback()
		return nil, err
	}
	// add to dev
	pubDep := &infra.Dependency{Id: pub.ID, Type: infra.DependencyType_HotFixPublish, Role: infra.DependencyRole_Host}
	_, err = bits_optimus_infra.RawCall.AddDevDepRelations(ctx, &infra.AddDevDepRelationsRequest{
		DevId:        devInfo.Dev.Id,
		Dependencies: []*infra.Dependency{pubDep},
		Relations:    []*infra.Relation{{Primary: &infra.Dependency{Id: req.VersionID, Type: infra.DependencyType_HotFixVersion, Role: infra.DependencyRole_Dependency}, Secondary: pubDep, Relation: infra.RelationType_Host}},
	})
	tx.Commit()
	return &hotfix.CreatePublishResp{PublishID: pub.ID}, nil
}

func CreatePublish(ctx context.Context, req *hotfix.CreatePublishReq) (*hotfix.CreatePublishResp, error) {
	hotfixVersion, err := data.GetHotfixVersionByID(ctx, req.VersionID)
	if err != nil {
		logs.CtxError(ctx, "GetHotfixVersionByID:%s", err.Error())
		return nil, err
	}
	hotfixTask, err := data.GetHotfixTaskByID(ctx, hotfixVersion.HotfixTaskID, true)
	if int(hotfixTask.HotfixType) == int(cd_hotfix.HotfixType_IOSSDKHotfix) { //sdk热修走自己的逻辑
		return createIOSSDKHotfixPublish(ctx, req)
	}
	appInfo, err := bytedance_bits_meta.QueryAppInfo(ctx, hotfixTask.BitsAppID)
	if err != nil {
		logs.CtxError(ctx, "QueryAppInfo error:%v, bits app id: %v", err, hotfixTask.BitsAppID)
		return nil, err
	}
	if appInfo.AppInfo.TechnologyStack == meta3.TechnologyStack_iOS {
		return createIosPublish(ctx, req)
	} else if appInfo.AppInfo.TechnologyStack == meta3.TechnologyStack_FlutterApp ||
		appInfo.AppInfo.TechnologyStack == meta3.TechnologyStack_Flutter {
		return createFlutterPublish(ctx, req)
	}
	return nil, errors.New(fmt.Sprintf("not support tech: %v", appInfo.AppInfo.TechnologyStack.String()))
}

func createIosPublish(ctx context.Context, req *hotfix.CreatePublishReq) (*hotfix.CreatePublishResp, error) {
	logs.CtxDebug(ctx, "start to create iOS hotfix publish %v", req)
	devInfo, err := bits_optimus_infra.GetDevForDep(ctx, &infra.Dependency{Id: req.VersionID, Type: infra.DependencyType_HotFixVersion, Role: infra.DependencyRole_Host}, false)
	if err != nil {
		logs.CtxError(ctx, "failed to get dev:%s", err.Error())
		return nil, err
	}
	if devInfo.Dev == nil {
		logs.CtxError(ctx, "dev not found")
		return nil, errors.New("dev not found")
	}
	version, err := data.GetHotfixVersionByID(ctx, req.VersionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get hotfix version:%s", err.Error())
		return nil, err
	}
	if version == nil {
		logs.CtxError(ctx, "hotfix version not found")
		return nil, errors.New("hotfix version not found")
	}
	packageInfo, err := bits_release_hotfix.HotfixQueryPkgInfo(ctx, version.AppID, version.VersionLong)
	if err != nil {
		logs.CtxError(ctx, "failed to get hotfix package info:%s", err.Error())
		return nil, err
	}
	tx, pub, err := data.CreateHotfixPublishTx(ctx, req.VersionID, req.Author, packageInfo.DysmUrl, packageInfo.IpaUrl, version.MainProjectID, 0)
	if err != nil {
		logs.CtxError(ctx, "failed to create hotfix publish:%s", err.Error())
		tx.Rollback()
		return nil, err
	}
	mainRepoBranchName := strings.ReplaceAll(version.Branch, "bits_patch", "bits_patch_image")
	mainRepoBranchName = fmt.Sprintf("%s_%d", mainRepoBranchName, pub.ID)
	err = git.CreateBranch(ctx, version.MainProjectID, mainRepoBranchName, version.Branch)
	if err != nil {
		logs.CtxError(ctx, "failed to create branch:%s", err.Error())
		tx.Rollback()
		return nil, err
	}
	mainRepoMr, err := gitlab.CreateMR(ctx, version.MainProjectID, mainRepoBranchName, version.TargetBranch, req.Author, fmt.Sprintf("hotfix publish %s", version.VersionLong))
	if err != nil {
		return nil, err
	}
	pub.IID = int64(mainRepoMr.Iid)
	err = data.UpdateHotfixPublish(ctx, tx, pub)
	if err != nil {
		logs.CtxError(ctx, "failed to update hotfix publish:%s", err.Error())
		tx.Rollback()
		return nil, err
	}

	// add to dev
	pubDep := &infra.Dependency{Id: pub.ID, Type: infra.DependencyType_HotFixPublish, Role: infra.DependencyRole_Host}
	_, err = bits_optimus_infra.RawCall.AddDevDepRelations(ctx, &infra.AddDevDepRelationsRequest{
		DevId:        devInfo.Dev.Id,
		Dependencies: []*infra.Dependency{pubDep},
		Relations:    []*infra.Relation{{Primary: &infra.Dependency{Id: req.VersionID, Type: infra.DependencyType_HotFixVersion, Role: infra.DependencyRole_Dependency}, Secondary: pubDep, Relation: infra.RelationType_Host}},
	})
	if err != nil {
		logs.CtxError(ctx, "failed to add to dev %s", err.Error())
		tx.Rollback()
		return nil, err
	}
	repoModuleMap := make(map[int64][]string)
	gslice.ForEach(req.Repos, func(r *hotfix.CreatePublishRepoDTO) {
		repoModuleMap[r.Id] = r.Modules
	})
	if len(req.Repos) != 0 {
		repos, err := data.GetHotfixRepoByIDs(ctx, gslice.Map(req.Repos, func(r *hotfix.CreatePublishRepoDTO) int64 { return r.Id }))
		if err != nil {
			logs.CtxError(ctx, "failed to get repo:%s", err.Error())
			tx.Rollback()
			return nil, err
		}
		if len(repos) == 0 {
			tx.Rollback()
			return nil, errors.New("repo not found")
		}
		wg := sync.WaitGroup{}
		var globalErr error
		for _, component := range repos {
			wg.Add(1)
			go func(repo *model.HotfixRepo) {
				defer wg.Done()
				// checkout snapshot branch
				sourceName := strings.ReplaceAll(repo.Branch, "bits_patch", "bits_patch_image")
				sourceName = fmt.Sprintf("%s_%d", sourceName, pub.ID)
				err := git.CreateBranch(ctx, repo.ProjectID, sourceName, repo.Branch)
				if err != nil {
					logs.CtxError(ctx, "failed to create branch:%s", err.Error())
					globalErr = err
					return
				}
				// checkout target branch
				title := fmt.Sprintf("hotfix mr for %d", req.VersionID)
				change, err := gitlab.CreateMR(ctx, repo.ProjectID, sourceName, repo.TargetBranch, req.Author, title)
				if err != nil {
					logs.CtxError(ctx, "failed to create mr:%s", err.Error())
					globalErr = err
					return
				}
				// try to find
				mr, err := data.GetPicoMergeRequestByID(ctx, strconv.FormatInt(repo.ProjectID, 10), int64(change.Iid), nil)
				if err != nil {
					logs.CtxError(ctx, "failed to get mr:%s", err.Error())
					globalErr = err
					return
				}
				if mr == nil { // 不存在插到数据库
					mr, err = data.CreatePicoMergeRequest(ctx, strconv.FormatInt(repo.ProjectID, 10), int64(change.Iid), change.Author.Username, sourceName, repo.TargetBranch, nil)
					if err != nil {
						logs.CtxError(ctx, "failed to create mr:%s", err.Error())
						globalErr = err
						return
					}
				}
				// add into dev
				mrDep := &infra.Dependency{Id: mr.ID, Type: infra.DependencyType_MR, Role: infra.DependencyRole_Dependency}
				_, err = bits_optimus_infra.RawCall.AddDevDepRelations(ctx, &infra.AddDevDepRelationsRequest{
					DevId:        devInfo.Dev.Id,
					Dependencies: []*infra.Dependency{mrDep},
					Relations:    []*infra.Relation{{Primary: pubDep, Secondary: mrDep, Relation: infra.RelationType_Host}},
				})
				if err != nil {
					logs.CtxError(ctx, "failed to add dev dep:%s", err.Error())
					globalErr = err
					return
				}
				_, _, err = data.CreateHotfixPublishRepo(ctx, pub.ID, repo.ID, mr.ID, repo.ProjectID, mr.IID, mr.SourceBranch, mr.TargetBranch, repoModuleMap[repo.ID], tx)
				if err != nil {
					logs.CtxError(ctx, "failed to create hotfix publish module:%s", err.Error())
					globalErr = err
					return
				}
			}(component)
		}
		wg.Wait()
		if globalErr != nil {
			logs.CtxError(ctx, "failed to publish hotfix:%s", globalErr.Error())
			tx.Rollback()
			return nil, globalErr
		}
	}
	/*
		skip, err := hotfixService.IsSkipIntegration(ctx, version.HotfixTaskID)
		if err != nil {
			logs.CtxError(ctx, "failed to check if skip integration:%s", err.Error())
			tx.Rollback()
			return nil, err
		}

			machineID := hotfixService.MachineTaskID_IOS
			if skip { // Lark等跳过集成
				machineID = hotfixService.MachineTaskID_Lark
			}
			err = hotfixService.InvokeStateMachine(ctx, pub.ID, machineID)
			if err != nil {
				logs.CtxError(ctx, "failed to invoke state machine")
				tx.Rollback()
				return nil, err
			}
	*/
	go hotfixService.CreateTimeline(ctx, pub.ID, "create publish process", req.Author)
	tx.Commit()
	return &hotfix.CreatePublishResp{PublishID: pub.ID}, nil
}

func createIOSSDKHotfixPublish(ctx context.Context, req *hotfix.CreatePublishReq) (*hotfix.CreatePublishResp, error) {
	logs.CtxDebug(ctx, "[createIOSSDKHotfixPublish]start to create iOS SDK hotfix publish %v", req)
	devInfo, err := bits_optimus_infra.GetDevForDep(ctx, &infra.Dependency{Id: req.VersionID, Type: infra.DependencyType_HotFixVersion, Role: infra.DependencyRole_Host}, false)
	if err != nil {
		logs.CtxError(ctx, "[createIOSSDKHotfixPublish]failed to get dev:%s", err.Error())
		return nil, err
	}
	if devInfo.Dev == nil {
		logs.CtxError(ctx, "[createIOSSDKHotfixPublish]dev not found")
		return nil, errors.New("dev not found")
	}
	version, err := data.GetHotfixVersionByID(ctx, req.VersionID)
	if err != nil {
		logs.CtxError(ctx, "[createIOSSDKHotfixPublish]failed to get hotfix version:%s", err.Error())
		return nil, err
	}
	if version == nil {
		logs.CtxError(ctx, "[createIOSSDKHotfixPublish]hotfix version not found")
		return nil, errors.New("hotfix version not found")
	}
	packageInfo, err := bits_release_hotfix.HotfixQueryPkgInfo(ctx, version.AppID, version.VersionLong)
	if err != nil {
		logs.CtxError(ctx, "[createIOSSDKHotfixPublish]failed to get hotfix package info:%s", err.Error())
		return nil, err
	}
	tx, pub, err := data.CreateHotfixPublishTx(ctx, req.VersionID, req.Author, packageInfo.DysmUrl, packageInfo.IpaUrl, version.MainProjectID, 0)
	if err != nil {
		logs.CtxError(ctx, "[createIOSSDKHotfixPublish]failed to create hotfix publish:%s", err.Error())
		tx.Rollback()
		return nil, err
	}
	mainRepoBranchName := strings.ReplaceAll(version.Branch, "bits_patch", "bits_patch_image")
	mainRepoBranchName = fmt.Sprintf("%s_%d", mainRepoBranchName, pub.ID)
	err = git.CreateBranch(ctx, version.MainProjectID, mainRepoBranchName, version.Branch)
	if err != nil {
		logs.CtxError(ctx, "[createIOSSDKHotfixPublish]failed to create branch:%s", err.Error())
		tx.Rollback()
		return nil, err
	}
	mainRepoMr, err := gitlab.CreateMR(ctx, version.MainProjectID, mainRepoBranchName, version.TargetBranch, req.Author, fmt.Sprintf("hotfix publish %s", version.VersionLong))
	if err != nil {
		return nil, err
	}
	pub.IID = int64(mainRepoMr.Iid)
	err = data.UpdateHotfixPublish(ctx, tx, pub)
	if err != nil {
		logs.CtxError(ctx, "[createIOSSDKHotfixPublish]failed to update hotfix publish:%s", err.Error())
		tx.Rollback()
		return nil, err
	}

	// add to dev
	pubDep := &infra.Dependency{Id: pub.ID, Type: infra.DependencyType_HotFixPublish, Role: infra.DependencyRole_Host}
	_, err = bits_optimus_infra.RawCall.AddDevDepRelations(ctx, &infra.AddDevDepRelationsRequest{
		DevId:        devInfo.Dev.Id,
		Dependencies: []*infra.Dependency{pubDep},
		Relations:    []*infra.Relation{{Primary: &infra.Dependency{Id: req.VersionID, Type: infra.DependencyType_HotFixVersion, Role: infra.DependencyRole_Dependency}, Secondary: pubDep, Relation: infra.RelationType_Host}},
	})
	if err != nil {
		logs.CtxError(ctx, "[createIOSSDKHotfixPublish]failed to add to dev %s", err.Error())
		tx.Rollback()
		return nil, err
	}
	go hotfixService.CreateTimeline(ctx, pub.ID, "create publish process", req.Author)
	tx.Commit()
	return &hotfix.CreatePublishResp{PublishID: pub.ID}, nil
}

func GetHotFixPublishBasicInfo(ctx context.Context, publishID int64) (*hotfix.GetHotFixPublishBasicInfoResp, error) {
	publish, err := data.GetHotfixPublishByID(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish:%s", err.Error())
		return nil, err
	}
	if publish == nil {
		return nil, errors.New("publish not found")
	}
	version, err := data.GetHotfixVersionByID(ctx, publish.VersionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get version %d:%s", publish.VersionID, err.Error())
		return nil, err
	}
	if version == nil {
		return nil, errors.New("version not found")
	}
	task, err := data.GetHotfixTaskByID(ctx, version.HotfixTaskID, true)
	if err != nil {
		return nil, err
	}
	if task == nil {
		return nil, errors.New("version not found")
	}
	appInfo, err := rpc.MetaClient.QueryAppInfoById(ctx, &meta.QueryAppInfoByIdRequest{AppId: int64(task.BitsAppID)})
	if err != nil {
		return nil, err
	}
	if appInfo.AppInfo == nil {
		logs.CtxError(ctx, "failed to get app info")
		return nil, errors.New("failed to get app info")
	}
	mrInfo, err := rpc.GitClient.GetMR(ctx, &git_server.GetMRRequest{
		ProjectId: publish.ProjectID,
		MrIid:     publish.IID,
	})
	if err != nil {
		return nil, err
	}
	return &hotfix.GetHotFixPublishBasicInfoResp{
		Title:                 task.TaskName,
		TaskID:                task.ID,
		Operator:              publish.Author,
		TaskCreateTime:        task.CreatedAt.Unix(),
		PublishCreateTime:     publish.CreateAt.Unix(),
		Dsym:                  publish.DSYM,
		Ipa:                   publish.IPA,
		PackageID:             0, // todo
		AppID:                 task.BitsAppID,
		GroupName:             appInfo.AppInfo.EnglishName,
		MainGroupBranch:       mrInfo.Mr.SourceBranch,
		MainGroupCommit:       mrInfo.Mr.DiffRefs.HeadSha,
		MainGroupRepoUrl:      appInfo.AppInfo.GitUrl,
		AppCloudID:            &task.CloudAppID,
		MainGroupTargetBranch: &mrInfo.Mr.TargetBranch,
	}, nil

}

func GetPublishList(ctx context.Context, versionID int64) (*hotfix.GetPublishListResp, error) {
	version, err := data.GetHotfixVersionByID(ctx, versionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get version %d:%s", versionID, err.Error())
		return nil, err
	}
	task, err := data.GetHotfixTaskByID(ctx, version.HotfixTaskID, true)
	if err != nil {
		return nil, err
	}
	var flowID hotfixService.MachineTaskID
	if task.HotfixType == model.HotfixType_flutter {
		flowID = hotfixService.MachineTaskID_Flutter
	} else {
		skipIntegration, err := hotfixService.IsSkipIntegration(ctx, version.HotfixTaskID)
		if err != nil {
			logs.CtxError(ctx, "err: s%", err.Error())
			return nil, err
		}
		if skipIntegration {
			flowID = hotfixService.MachineTaskID_Lark
		} else if task.HotfixType == model.HotfixType_android {
			flowID = hotfixService.MachineTaskID_Android
		} else if task.HotfixType == model.HotfixType_iOS {
			flowID = hotfixService.MachineTaskID_IOS
		}
	}
	publish, err := data.GetHotfixPublishList(ctx, versionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish:%s", err.Error())
		return nil, err
	}
	ans := make([]*hotfix.Publish, 0)
	for _, p := range publish {
		var status hotfix.Status
		var stage hotfix.Stage
		if p.Status == model.HotfixPublishStatus_Failed || p.Status == model.HotfixPublishStatus_Closed {
			status = hotfix.Status_closed
			stage = hotfix.Stage_Close
		} else if p.Status == model.HotfixPublishStatus_Published {
			status = hotfix.Status_success
			stage = hotfix.Stage_Finished
		} else {
			stage, status, err = hotfixService.GetCurrentTaskMachineState(ctx, flowID, p.ID)
			if err != nil {
				return nil, err
			}
		}
		logs.CtxInfo(ctx, "publishID:%d\tstage:%s\tstatus:%s", p.ID, stage.String(), status.String())
		moduleRepos, err := data.GetHotfixPublishRepoList(ctx, p.ID)
		if err != nil {
			logs.CtxError(ctx, "failed to get publish repo:%s", err.Error())
			return nil, err
		}
		modules := make([]*hotfix.Module, 0)
		moduleMap := make(map[string]*hotfix.Module)
		for _, moduleRepo := range moduleRepos {
			moduleList := make([]string, 0)
			err = json.Unmarshal([]byte(moduleRepo.Modules), &moduleList)
			if err != nil {
				logs.CtxError(ctx, "failed to unmarshal modules:%s", err.Error())
				repo, err := data.GetHotfixRepoByID(ctx, moduleRepo.RepoID)
				if err != nil {
					return nil, err
				}
				err = json.Unmarshal([]byte(repo.Modules), &moduleList)
				if err != nil {
					return nil, err
				}
			}
			for _, module := range moduleList {
				moduleMap[module] = &hotfix.Module{Name: module}
			}
		}
		for _, module := range moduleMap {
			modules = append(modules, module)
		}
		var artifact *hotfix.HotfixArtifact
		latestTaskJob, err := data.GetLatestTaskJobByPublishID(ctx, p.ID, "build")
		if latestTaskJob != nil {
			artifacts, err := bits_release_hotfix.QueryDevOpsHotfixArtifact(ctx, latestTaskJob.PipelineID)
			if err != nil {
				logs.CtxError(ctx, "failed to get artifacts:%s", err.Error())
				return nil, err
			}
			logs.CtxInfo(ctx, "get artifacts success. %v", utils.ToJson(artifacts))
			for _, a := range artifacts.Artifacts {
				// liuchangkun要求只要saveu
				if a.ArtifactType != nil && *a.ArtifactType != release.ArtifactType_SaveU {
					continue
				}
				ha := &hotfix.HotfixArtifact{
					Url:             a.Url,
					Md5:             a.Md5,
					Size:            a.Size,
					Id:              a.Id,
					SaveuPkgVersion: a.SaveuPkgVersion,
				}
				artifactType := hotfix.ArtifactType(*a.ArtifactType)
				ha.ArtifactType = &artifactType
				if a.Status != nil {
					artifactStatus := hotfix.SaveuHotfixPkgStatus(*a.Status)
					ha.Status = &artifactStatus
				}
				artifact = ha
			}
		}
		ans = append(ans, &hotfix.Publish{
			Id:        p.ID,
			VersionID: p.VersionID,
			Author:    p.Author,
			Dsym:      p.DSYM,
			Ipa:       p.IPA,
			CreateAt:  p.CreateAt.Unix(),
			Status:    status,
			Stage:     stage,
			Modules:   modules,
			Artifact:  artifact,
		})
	}
	return &hotfix.GetPublishListResp{Publish: ans}, nil
}

func GetPublishBasicInfo(ctx context.Context, publishID int64) (*hotfix.GetPublishInfoResp, error) {
	publish, err := data.GetHotfixPublishByID(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish:%s", err.Error())
		return nil, err
	}
	if publish == nil {
		return nil, errors.New("publish not found")
	}
	return &hotfix.GetPublishInfoResp{
		PublishID: publish.ID,
		VersionID: publish.VersionID,
		Author:    publish.Author,
		Dsym:      publish.DSYM,
		Ipa:       publish.IPA,
		CreateAt:  publish.CreateAt.Unix(),
		Status:    (*string)(&publish.Status),
	}, nil
}

func GetGraph(ctx context.Context, publishID int64) (*hotfix.GetPublishGraphResp, error) {
	// todo 这里会有高频轮询的现象，需要加缓存
	publish, err := data.GetHotfixPublishByID(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish,err:%s", err.Error())
		return nil, err
	}
	if publish == nil {
		return nil, errors.New("publish not found")
	}
	version, err := data.GetHotfixVersionByID(ctx, publish.VersionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get version,err:%s", err.Error())
		return nil, err
	}
	if version == nil {
		return nil, errors.New("version not found")
	}
	task, err := data.GetHotfixTaskByID(ctx, version.HotfixTaskID, true)
	if err != nil {
		logs.CtxError(ctx, "failed to get task:%s", err.Error())
		return nil, err
	}
	if task == nil {
		return nil, errors.New("task not found")
	}
	var flowID hotfixService.MachineTaskID
	if task.HotfixType == model.HotfixType_flutter {
		flowID = hotfixService.MachineTaskID_Flutter
	} else {
		skipIntegration, err := hotfixService.IsSkipIntegration(ctx, version.HotfixTaskID)
		if err != nil {
			logs.CtxError(ctx, "err: s%", err.Error())
			return nil, err
		}
		if skipIntegration {
			flowID = hotfixService.MachineTaskID_Lark
		} else if task.HotfixType == model.HotfixType_android {
			flowID = hotfixService.MachineTaskID_Android
		} else if task.HotfixType == model.HotfixType_iOS {
			flowID = hotfixService.MachineTaskID_IOS
		}
	}
	statusMap, err := hotfixService.GetTaskStatus(ctx, publishID, flowID)
	if err != nil {
		return nil, err
	}
	ans := make(map[hotfix.Stage]*hotfix.Node)
	for stage, status := range statusMap {
		node := &hotfix.Node{
			Stage:   stage,
			Status:  status.Status,
			Name:    stage.String(),
			StartAt: status.StartAt,
			EndAt:   status.EndAt,
		}
		if status.Status == hotfix.Status_running {
			node.EndAt = 0
		}
		ans[stage] = node
	}
	// 只有flutter动态包下发才会存mr信息
	if _, ok := ans[hotfix.Stage_CodeReview]; ok {
		ans[hotfix.Stage_CodeReview].StartAt = 0
		ans[hotfix.Stage_CodeReview].EndAt = 0
	}
	return &hotfix.GetPublishGraphResp{
		Nodes: ans,
	}, nil
}

func GetBranchRepoInfo(ctx context.Context, publishID int64) (*hotfix.GetHotfixBranchInfoResp, error) {
	publish, err := data.GetHotfixPublishByID(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish:%s", err.Error())
		return nil, err
	}
	if publish == nil {
		return nil, errors.New("publish not found")
	}
	repos, err := data.GetHotfixPublishRepoList(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish repos:%s", err.Error())
		return nil, err
	}
	// 主仓
	version, err := data.GetHotfixVersionByID(ctx, publish.VersionID)
	if err != nil {
		return nil, err
	}
	if version == nil {
		return nil, errors.New("version not found")
	}
	mainProjectConfig, err := data.GetConfigProjectInfoByProjectID(ctx, version.MainProjectID, true)
	if err != nil {
		return nil, err
	}
	if mainProjectConfig == nil {
		return nil, errors.New("main project not found")
	}
	mrInfo, err := rpc.GitClient.GetMR(ctx, &git_server.GetMRRequest{
		ProjectId: publish.ProjectID,
		MrIid:     publish.IID,
	})
	if err != nil {
		return nil, err
	}
	ans := make([]*hotfix.RepoBranchInfo, 0)
	for _, repo := range repos {
		repoInfo, err := data.GetHotfixRepoByID(ctx, repo.RepoID)
		if err != nil {
			logs.CtxError(ctx, "failed to get repo:%s", err.Error())
			return nil, err
		}
		if repoInfo == nil {
			return nil, errors.New("repo not found")
		}
		moduleList := make([]string, 0)
		err = json.Unmarshal([]byte(repo.Modules), &moduleList)
		if err != nil {
			logs.CtxError(ctx, "failed to unmarshal modules:%s,%s", err.Error(), repo.Modules)
			err = json.Unmarshal([]byte(repoInfo.Modules), &moduleList)
			if err != nil {
				return nil, err
			}
		}
		ans = append(ans, &hotfix.RepoBranchInfo{
			Name:      repoInfo.RepoName,
			ProjectID: repo.ProjectID,
			CommitID:  repoInfo.CommitID,
			Branch:    repoInfo.Branch,
			Iid:       repo.Iid,
			Modules:   moduleList,
		})
	}
	gslice.SortBy(ans, func(a, b *hotfix.RepoBranchInfo) bool {
		return a.Name > b.Name
	})
	ans = append([]*hotfix.RepoBranchInfo{{
		ProjectID: version.MainProjectID,
		Name:      mainProjectConfig.Name,
		Branch:    version.Branch,
		CommitID:  mrInfo.Mr.DiffRefs.BaseSha,
		Modules:   []string{},
		Iid:       publish.IID,
	}}, ans...)
	return &hotfix.GetHotfixBranchInfoResp{Repos: ans}, nil
}

func GetPublishTimeline(ctx context.Context, publishID int64) (*hotfix.GetHotfixPublishTimelineResp, error) {
	timelines, err := data.GetHotfixTimeLineByPubID(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get timeline:%s", err.Error())
		return nil, err
	}
	ans := make([]*hotfix.PublishTimeline, 0)
	for _, timeline := range timelines {
		ans = append(ans, &hotfix.PublishTimeline{
			Id:       timeline.ID,
			Message:  timeline.Message,
			Operator: timeline.Operator,
			CreateAt: timeline.CreateAt.Unix(),
		})
	}
	return &hotfix.GetHotfixPublishTimelineResp{Timeline: ans}, nil
}

func CreatePublishTimeline(ctx context.Context, req *hotfix.CreatePublishTimelineReq) (*hotfix.CreatePublishTimelineResp, error) {
	timeline, err := hotfixService.CreateTimeline(ctx, req.PublishID, req.Message, req.Operator)
	if err != nil {
		logs.CtxError(ctx, "failed to create timeline:%s", err.Error())
		return nil, err
	}
	return &hotfix.CreatePublishTimelineResp{Timeline: &hotfix.PublishTimeline{
		Id:       timeline.ID,
		Message:  timeline.Message,
		Operator: timeline.Operator,
		CreateAt: timeline.CreateAt.Unix(),
	}}, nil
}

func TerminatePublish(ctx context.Context, publishID int64, operator string) error {
	publish, err := data.GetHotfixPublishByID(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish:%s", err.Error())
		return err
	}
	if publish == nil {
		return errors.New("publish not found")
	}
	err = hotfixService.CloseStateMachine(ctx, publishID)
	if err != nil {
		return err
	}
	publish.Status = model.HotfixPublishStatus_Closed
	err = data.UpdateHotfixPublish(ctx, nil, publish)
	if err != nil {
		logs.CtxError(ctx, "failed to update publish:%s", err.Error())
		return err
	}
	go hotfixService.CreateTimeline(ctx, publishID, "stop publish process", operator)
	return nil
}

func GetHotfixIntegrationInfo(ctx context.Context, publishID int64) (*hotfix.GetHotfixIntegrationInfoResp, error) {
	integration, err := data.GetLatestIntegration(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get integration:%s", err.Error())
		return nil, err
	}
	if integration == nil {
		logs.CtxWarn(ctx, "integration not found")
		return &hotfix.GetHotfixIntegrationInfoResp{
			TaskID:       "",
			TargetBranch: "",
			Status:       "",
			CommitID:     "",
			ProjectID:    0,
			GitUrl:       "",
			Message:      "",
			TraceId:      "",
			BaseResp:     nil,
		}, nil
	}
	if integration.ProjectID == 0 { //未找到project id先直接返回
		logs.CtxWarn(ctx, "project id not found")
		return &hotfix.GetHotfixIntegrationInfoResp{
			TaskID:       "",
			TargetBranch: "",
			Status:       "",
			CommitID:     "",
			ProjectID:    0,
			GitUrl:       "",
			Message:      "",
			TraceId:      "",
			BaseResp:     nil,
		}, nil
	}
	projectInfo, err := data.GetConfigProjectInfoByProjectID(ctx, integration.ProjectID, true)
	if err != nil {
		logs.CtxError(ctx, "failed to get project info:%s", err.Error())
		return nil, err
	}
	if projectInfo == nil {
		return nil, errors.New("project not found")
	}
	return &hotfix.GetHotfixIntegrationInfoResp{
		TaskID:       integration.TaskID,
		TargetBranch: integration.TargetBranch,
		Status:       integration.Status,
		CommitID:     integration.CommitID,
		ProjectID:    integration.ProjectID,
		GitUrl:       projectInfo.GitRepoAddr,
		Message:      integration.Message,
		TraceId:      integration.TraceID,
	}, nil
}

func GetHotfixBuildPackageDetail(ctx context.Context, publishID int64, pipelineType hotfix.HotfixBuildType) (*hotfix.GetHotfixBuildDetailResp, error) {
	publish, err := data.GetHotfixPublishByID(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish:%s", err.Error())
		return nil, err
	}
	if publish == nil {
		return nil, errors.New("publish not found")
	}
	version, err := data.GetHotfixVersionByID(ctx, publish.VersionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get version:%s", err.Error())
		return nil, err
	}
	if version == nil {
		return nil, errors.New("version not found")
	}
	projectInfo, err := data.GetConfigProjectInfoByProjectID(ctx, version.MainProjectID, true)
	if err != nil {
		logs.CtxError(ctx, "failed to get project info:%s", err.Error())
		return nil, err
	}
	if projectInfo == nil {
		return nil, errors.New("project not found")
	}
	mrInfo, err := rpc.GitClient.GetMR(ctx, &git_server.GetMRRequest{
		ProjectId: publish.ProjectID,
		MrIid:     publish.IID,
	})
	if err != nil {
		return nil, err
	}
	var latestTaskJob *model.HotfixTaskJob
	if pipelineType == hotfix.HotfixBuildType_Package {
		latestTaskJob, err = data.GetLatestTaskJobByPublishID(ctx, publishID, "build")
	} else if pipelineType == hotfix.HotfixBuildType_AutomatedTest {
		latestTaskJob, err = data.GetLatestTaskJobByPublishID(ctx, publishID, "automated_test")
	}
	if err != nil {
		logs.CtxError(ctx, "[build]failed to get latest task job:%s", err.Error())
		return nil, err
	}
	if latestTaskJob == nil {
		return &hotfix.GetHotfixBuildDetailResp{
			PipelineID:   0,
			Status:       "waiting",
			ProjectID:    version.MainProjectID,
			GitUrl:       projectInfo.GitRepoAddr,
			CommitID:     mrInfo.Mr.DiffRefs.HeadSha,
			Branch:       mrInfo.Mr.SourceBranch,
			TargetBranch: mrInfo.Mr.TargetBranch,
			CreateAt:     0,
			UpdateAt:     0,
			Artifacts:    nil,
		}, nil
	}
	artifactsList := make([]*hotfix.HotfixArtifact, 0)
	if pipelineType == hotfix.HotfixBuildType_Package {
		artifacts, err := bits_release_hotfix.QueryDevOpsHotfixArtifact(ctx, latestTaskJob.PipelineID)
		if err != nil {
			logs.CtxError(ctx, "failed to get artifacts:%s", err.Error())
			return nil, err
		}
		for _, a := range artifacts.Artifacts {
			artifactsList = append(artifactsList, &hotfix.HotfixArtifact{
				Url:  a.Url,
				Md5:  a.Md5,
				Size: a.Size,
				Id:   a.Id,
			})
		}
	}
	return &hotfix.GetHotfixBuildDetailResp{
		PipelineID:   latestTaskJob.PipelineID,
		Status:       latestTaskJob.Status,
		ProjectID:    version.MainProjectID,
		GitUrl:       projectInfo.GitRepoAddr,
		CommitID:     mrInfo.Mr.DiffRefs.HeadSha,
		Branch:       mrInfo.Mr.SourceBranch,
		TargetBranch: mrInfo.Mr.TargetBranch,
		Artifacts:    artifactsList,
		CreateAt:     latestTaskJob.CreateTime.Unix(),
		UpdateAt:     latestTaskJob.UpdateTime.Unix(),
	}, nil
}

func GetHotfixPipelineIDByPublish(ctx context.Context, publishID int64) (int64, error) {
	publish, err := data.GetHotfixPublishByID(ctx, publishID)
	if err != nil {
		logs.CtxError(ctx, "failed to get publish:%s", err.Error())
		return 0, err
	}
	if publish == nil {
		return 0, errors.New("publish not found")
	}
	if publish.PackageID != 0 { // android publish
		packageInfo, err := data.GetHotfixPackageByID(ctx, publish.PackageID)
		if err != nil {
			logs.CtxError(ctx, "failed to get package:%s", err.Error())
			return 0, err
		}
		if packageInfo == nil {
			return 0, nil
		}
		return packageInfo.PipelineID, nil
	}
	latestTaskJob, err := data.GetLatestTaskJobByPublishID(ctx, publishID, "build")
	if err != nil {
		logs.CtxError(ctx, "failed to get latest task job:%s", err.Error())
		return 0, err
	}
	if latestTaskJob == nil {
		return 0, nil
	}
	return latestTaskJob.PipelineID, nil
}

func CloseHotfixPublish(ctx context.Context, publishID int64) error {
	return hotfixService.CloseStateMachine(ctx, publishID)
}

func RetryHotfixPublishBuild(ctx context.Context, publishID int64) error {
	return hotfixService.RetryTask(ctx, "HotfixPackageTask", publishID)
}

func PublishAndroid(ctx context.Context, packageID int64, operator string) (int64, error) {
	pkg, err := data.GetHotfixPackageByID(ctx, packageID)
	if err != nil {
		logs.CtxError(ctx, "failed to get package:%s", err.Error())
		return 0, err
	}
	if pkg == nil {
		return 0, errors.New("package not found")
	}
	build, err := data.GetHotfixAndroidBuildByID(ctx, pkg.BuildID)
	if err != nil {
		logs.CtxError(ctx, "failed to get build:%s", err.Error())
		return 0, err
	}
	if build == nil {
		return 0, errors.New("build not found")
	}
	version, err := data.GetHotfixVersionByID(ctx, build.VersionID)
	if err != nil {
		logs.CtxError(ctx, "failed to get version:%s", err.Error())
		return 0, err
	}
	tx, pub, err := data.CreateHotfixPublishTx(ctx, version.ID, operator, "", "", version.MainProjectID, 0)
	if err != nil {
		logs.CtxError(ctx, "failed to create hotfix publish:%s", err.Error())
		tx.Rollback()
		return 0, err
	}
	pub.BuildID = pkg.BuildID
	pub.PackageID = packageID
	err = data.UpdateHotfixPublish(ctx, tx, pub)
	if err != nil {
		logs.CtxError(ctx, "failed to update hotfix publish:%s", err.Error())
		tx.Rollback()
		return 0, err
	}
	tx.Commit()
	/*
		err = hotfixService.InvokeStateMachine(ctx, pub.ID, hotfixService.MachineTaskID_Android)
		if err != nil {
			logs.CtxError(ctx, "failed to invoke state machine:%s", err.Error())
			data.DeletePublish(ctx, nil, pub)
			return 0, err
		}
	*/
	go hotfixService.CreateTimeline(ctx, pub.ID, "create publish process", operator)
	return pub.ID, nil
}
