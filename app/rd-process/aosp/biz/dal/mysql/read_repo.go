package mysql

import "context"

/*
	import (
		"gorm.io/gorm"
	)
*/
type ReadCRUDRepo interface {
	/*
		sql(value="select * from aosp_branch_config where id=?", mode="placeholder")
	*/
	GetBranchConfigById(ctx context.Context, id int64) (AospBranchConfig, error)

	/*
		sql(value="select * from aosp_branch_config where app_id=? and deleted=0 order by id desc", mode="placeholder")
	*/
	GetBranchConfigsByAppId(ctx context.Context, id int64) ([]*AospBranchConfig, error)

	/*
		sql(value="select * from aosp_build_target where id=?", mode="placeholder")
	*/
	GetBuildTargetById(ctx context.Context, id int64) (AospBuildTarget, error)

	/*
		sql(value="select * from aosp_build_target where branch_id=? and deleted=0", mode="placeholder")
	*/
	GetBuildTargetsByBranchId(ctx context.Context, id int64) ([]*AospBuildTarget, error)

	/*
		sql(value="select * from aosp_repo_project where branch_id=?", mode="placeholder")
	*/
	GetRepoProjectByBranchId(ctx context.Context, id int64) ([]*AospRepoProject, error)

	/*
		sql(value="select * from aosp_repo_project where project_name=? and revision=?", mode="placeholder")
	*/
	GetRepoProjectByRevision(ctx context.Context, projectName string, revision string) ([]*AospRepoProject, error)

	/*
		sql(value="select * from obs_shadow_project where dev_id=? and deleted=0", mode="placeholder")
	*/
	GetObsShadowTask(ctx context.Context, id int64) (ObsShadowProject, error)

	/*
		sql(value="select * from obs_package_build where job_id=?", mode="placeholder")
	*/
	GetObsPackageBuildByJobId(ctx context.Context, id int64) (ObsPackageBuild, error)

	/*
		sql(value="select * from obs_package_info where dev_id=? and pipeline_id=?", mode="placeholder")
	*/
	GetObsPackageInfo(ctx context.Context, id int64, pipelineId int64) ([]*ObsPackageInfo, error)
}
