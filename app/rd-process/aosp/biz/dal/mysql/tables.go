// Code generated by COMMENTS_BUILD_TOOLS 2.0.77. DO NOT EDIT.
package mysql

import (
	"time"
)

func (AospBranchConfig) TableName() string {
	return "aosp_branch_config"
}

func NewAospBranchConfig() *AospBranchConfig {
	return &AospBranchConfig{}
}

type AospBranchConfig struct {
	Deleted          int8      `gorm:"column:deleted" json:"deleted"`                       // soft delete
	Id               int64     `gorm:"column:id;primary_key" json:"id"`                     // primary key
	AppId            int64     `gorm:"column:app_id" json:"app_id"`                         // application id
	CreateTime       time.Time `gorm:"column:create_time" json:"create_time"`               // create time
	ModifyTime       time.Time `gorm:"column:modify_time" json:"modify_time"`               // modify time
	BranchName       string    `gorm:"column:branch_name" json:"branch_name"`               // branch name
	BranchNameUnique string    `gorm:"column:branch_name_unique" json:"branch_name_unique"` // branch name unique
	BranchUrl        string    `gorm:"column:branch_url" json:"branch_url"`                 // branch init url
	GerritUrl        string    `gorm:"column:gerrit_url" json:"gerrit_url"`                 // Gerrit URL
	CreatorName      string    `gorm:"column:creator_name" json:"creator_name"`             // creator name
	ModifierName     string    `gorm:"column:modifier_name" json:"modifier_name"`           // modifier name
}

func (AospBuildTarget) TableName() string {
	return "aosp_build_target"
}

func NewAospBuildTarget() *AospBuildTarget {
	return &AospBuildTarget{}
}

type AospBuildTarget struct {
	Deleted      int8      `gorm:"column:deleted" json:"deleted"`             // soft delete
	Id           int64     `gorm:"column:id;primary_key" json:"id"`           // primary key
	BranchId     int64     `gorm:"column:branch_id" json:"branch_id"`         // branch id
	PipelineId   int64     `gorm:"column:pipeline_id" json:"pipeline_id"`     // target ci pipeline
	CreateTime   time.Time `gorm:"column:create_time" json:"create_time"`     // create time
	ModifyTime   time.Time `gorm:"column:modify_time" json:"modify_time"`     // modify time
	BuildName    string    `gorm:"column:build_name" json:"build_name"`       // build name
	BuildCommand string    `gorm:"column:build_command" json:"build_command"` // build command
	CreatorName  string    `gorm:"column:creator_name" json:"creator_name"`   // creator name
	ModifierName string    `gorm:"column:modifier_name" json:"modifier_name"` // modifier name
}

func (AospRepoProject) TableName() string {
	return "aosp_repo_project"
}

func NewAospRepoProject() *AospRepoProject {
	return &AospRepoProject{}
}

type AospRepoProject struct {
	Id          int64  `gorm:"column:id;primary_key" json:"id"`         // primary key
	BranchId    int64  `gorm:"column:branch_id" json:"branch_id"`       // branch id
	Revision    string `gorm:"column:revision" json:"revision"`         // project revision
	ProjectPath string `gorm:"column:project_path" json:"project_path"` // project path
	ProjectName string `gorm:"column:project_name" json:"project_name"` // project name
}

func (GerritUnit) TableName() string {
	return "gerrit_unit"
}

func NewGerritUnit() *GerritUnit {
	return &GerritUnit{}
}

type GerritUnit struct {
	IsDeleted      int8      `gorm:"column:is_deleted" json:"is_deleted"`             // is delete
	Id             int64     `gorm:"column:id;primary_key" json:"id"`                 // primary key
	DevId          int64     `gorm:"column:dev_id" json:"dev_id"`                     // dev id
	GerritNumberId int64     `gorm:"column:gerrit_number_id" json:"gerrit_number_id"` // gerrit number id, such as 2106111
	CreateTime     time.Time `gorm:"column:create_time" json:"create_time"`           // create time
	RepoName       string    `gorm:"column:repo_name" json:"repo_name"`               // repo name
	SourceBranch   string    `gorm:"column:source_branch" json:"source_branch"`       // source branch
	TargetBranch   string    `gorm:"column:target_branch" json:"target_branch"`       // target branch
	GerritChangeId string    `gorm:"column:gerrit_change_id" json:"gerrit_change_id"` // for easily call gerrit openapi, such as I15970d4d0059a660343720721c68811c0ba0719a
	WarningReason  string    `gorm:"column:warning_reason" json:"warning_reason"`     // such as create failed
}

func (GerritDevContext) TableName() string {
	return "gerrit_dev_context"
}

func NewGerritDevContext() *GerritDevContext {
	return &GerritDevContext{}
}

type GerritDevContext struct {
	Submit       int8      `gorm:"column:submit" json:"submit"`               // 0:default, 1:already submit
	Id           int64     `gorm:"column:id;primary_key" json:"id"`           // primary key
	AppId        int64     `gorm:"column:app_id" json:"app_id"`               // app id
	DevId        int64     `gorm:"column:dev_id" json:"dev_id"`               // dev id
	CreateTime   time.Time `gorm:"column:create_time" json:"create_time"`     // create time
	ModifyTime   time.Time `gorm:"column:modify_time" json:"modify_time"`     // modify time
	Context      string    `gorm:"column:context" json:"context"`             // context
	CreatorEmail string    `gorm:"column:creator_email" json:"creator_email"` // creator_email
	Status       string    `gorm:"column:status" json:"status"`               // status
}

func (GerritDevPipeline) TableName() string {
	return "gerrit_dev_pipeline"
}

func NewGerritDevPipeline() *GerritDevPipeline {
	return &GerritDevPipeline{}
}

type GerritDevPipeline struct {
	Notified     int8      `gorm:"column:notified" json:"notified"`           // 0:default, 1:already notified
	Id           int64     `gorm:"column:id;primary_key" json:"id"`           // primary key
	DevId        int64     `gorm:"column:dev_id" json:"dev_id"`               // dev id
	PipelineId   int64     `gorm:"column:pipeline_id" json:"pipeline_id"`     // pipeline id
	CreateTime   time.Time `gorm:"column:create_time" json:"create_time"`     // create time
	PipelineType string    `gorm:"column:pipeline_type" json:"pipeline_type"` // pipeline_type shadow/released
}

func (ObsShadowProject) TableName() string {
	return "obs_shadow_project"
}

func NewObsShadowProject() *ObsShadowProject {
	return &ObsShadowProject{}
}

type ObsShadowProject struct {
	Deleted           int8      `gorm:"column:deleted" json:"deleted"`                         // soft delete
	Id                int64     `gorm:"column:id;primary_key" json:"id"`                       // primary key
	DevId             int64     `gorm:"column:dev_id" json:"dev_id"`                           // dev id
	CreateTime        time.Time `gorm:"column:create_time" json:"create_time"`                 // create time
	ProjectName       string    `gorm:"column:project_name" json:"project_name"`               // project name
	ShadowProjectName string    `gorm:"column:shadow_project_name" json:"shadow_project_name"` // shadow project name
}

func (ObsPackageBuild) TableName() string {
	return "obs_package_build"
}

func NewObsPackageBuild() *ObsPackageBuild {
	return &ObsPackageBuild{}
}

type ObsPackageBuild struct {
	Id          int64     `gorm:"column:id;primary_key" json:"id"`         // primary key
	DevId       int64     `gorm:"column:dev_id" json:"dev_id"`             // dev id
	PipelineId  int64     `gorm:"column:pipeline_id" json:"pipeline_id"`   // pipeline id
	JobId       int64     `gorm:"column:job_id" json:"job_id"`             // task job id
	CreateTime  time.Time `gorm:"column:create_time" json:"create_time"`   // create time
	ProjectName string    `gorm:"column:project_name" json:"project_name"` // project name
	Status      string    `gorm:"column:status" json:"status"`             // build status
}

func (ObsPackageInfo) TableName() string {
	return "obs_package_info"
}

func NewObsPackageInfo() *ObsPackageInfo {
	return &ObsPackageInfo{}
}

type ObsPackageInfo struct {
	Id             int64     `gorm:"column:id;primary_key" json:"id"`               // primary key
	DevId          int64     `gorm:"column:dev_id" json:"dev_id"`                   // dev id
	PipelineId     int64     `gorm:"column:pipeline_id" json:"pipeline_id"`         // pipeline id
	CreateTime     time.Time `gorm:"column:create_time" json:"create_time"`         // create time
	PackageUrl     string    `gorm:"column:package_url" json:"package_url"`         // package url
	PackageBranch  string    `gorm:"column:package_branch" json:"package_branch"`   // package branch name
	PackageName    string    `gorm:"column:package_name" json:"package_name"`       // obs package name
	PackageVersion string    `gorm:"column:package_version" json:"package_version"` // obs package version
}

func (AospAppBinary) TableName() string {
	return "aosp_app_binary"
}

func NewAospAppBinary() *AospAppBinary {
	return &AospAppBinary{}
}

type AospAppBinary struct {
	Id           int64     `gorm:"column:id;primary_key" json:"id"`             // primary key
	AppId        int64     `gorm:"column:app_id" json:"app_id"`                 // app id
	CreateTime   time.Time `gorm:"column:create_time" json:"create_time"`       // create time
	Project      string    `gorm:"column:project" json:"project"`               // aosp app project
	CommitId     string    `gorm:"column:commit_id" json:"commit_id"`           // app commitid
	BuildType    string    `gorm:"column:build_type" json:"build_type"`         // app build type
	BinaryUrl    string    `gorm:"column:binary_url" json:"binary_url"`         // apk download url
	BinaryMd5Url string    `gorm:"column:binary_md5_url" json:"binary_md5_url"` // apk download md5 url
}
