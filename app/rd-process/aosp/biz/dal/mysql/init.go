package mysql

import (
	"code.byted.org/gorm/bytedgorm"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/dbresolver"
)

var (
	BitDB             *gorm.DB
	WriteRepoOperator WriteCRUDRepo
	ReadRepoOperator  ReadCRUDRepo
)

func Init() {
	var err error
	BitDB, err = gorm.Open(
		bytedgorm.MySQL("toutiao.mysql.bits", "bits"), // .WithReadReplicas()
		bytedgorm.WithDefaults(),
		bytedgorm.Logger{LogLevel: logger.Info},
	)
	if err != nil {
		panic(err)
	}
	// BitDB = mysql.MustConnect(mysql.NewTCEEndpoint("toutiao.mysql.bits", "bits"), bytedgorm.Logger{LogLevel: logger.Info})
	WriteRepoOperator = NewWriteCRUDRepo(BitDB.Clauses(dbresolver.Write))
	ReadRepoOperator = NewReadCRUDRepo(BitDB)
}
