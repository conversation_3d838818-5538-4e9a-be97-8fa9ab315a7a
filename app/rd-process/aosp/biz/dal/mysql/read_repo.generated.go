// Code generated by COMMENTS_BUILD_TOOLS 2.0.77. DO NOT EDIT.
package mysql

import (
	"context"
	"database/sql"
	"errors"

	"code.byted.org/gopkg/logs"
	"gorm.io/gorm"
)

var GlobalErrReadCRUDRepo = struct {
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
	NothingExecute          error
}{
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
	errors.New("NothingExecuteErr"),
}

func NewReadCRUDRepo(handler *gorm.DB) ReadCRUDRepo {
	return &_ReadCRUDRepoStruct{
		handler: handler,
	}
}

type _ReadCRUDRepoStruct struct {
	handler *gorm.DB
}

func (interstruct *_ReadCRUDRepoStruct) GetBranchConfigById(ctx context.Context, id int64) (AospBranchConfig, error) {
	_result, _retErr := func() (AospBranchConfig, error) {
		_sqlText := "select * from aosp_branch_config where id=?"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return AospBranchConfig{}, _sdb.Error
		}
		var _ret AospBranchConfig
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return AospBranchConfig{}, _sdb.Error
		}
		if _sdb.RowsAffected <= 0 {
			return _ret, gorm.ErrRecordNotFound
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetBranchConfigsByAppId(ctx context.Context, id int64) ([]*AospBranchConfig, error) {
	_result, _retErr := func() ([]*AospBranchConfig, error) {
		_sqlText := "select * from aosp_branch_config where app_id=? and deleted=0 order by id desc"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []*AospBranchConfig
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = new(AospBranchConfig)
			_err = _db.ScanRows(_rows, _ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetBuildTargetById(ctx context.Context, id int64) (AospBuildTarget, error) {
	_result, _retErr := func() (AospBuildTarget, error) {
		_sqlText := "select * from aosp_build_target where id=?"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return AospBuildTarget{}, _sdb.Error
		}
		var _ret AospBuildTarget
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return AospBuildTarget{}, _sdb.Error
		}
		if _sdb.RowsAffected <= 0 {
			return _ret, gorm.ErrRecordNotFound
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetBuildTargetsByBranchId(ctx context.Context, id int64) ([]*AospBuildTarget, error) {
	_result, _retErr := func() ([]*AospBuildTarget, error) {
		_sqlText := "select * from aosp_build_target where branch_id=? and deleted=0"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []*AospBuildTarget
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = new(AospBuildTarget)
			_err = _db.ScanRows(_rows, _ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetObsPackageBuildByJobId(ctx context.Context, id int64) (ObsPackageBuild, error) {
	_result, _retErr := func() (ObsPackageBuild, error) {
		_sqlText := "select * from obs_package_build where job_id=?"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return ObsPackageBuild{}, _sdb.Error
		}
		var _ret ObsPackageBuild
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return ObsPackageBuild{}, _sdb.Error
		}
		if _sdb.RowsAffected <= 0 {
			return _ret, gorm.ErrRecordNotFound
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetObsPackageInfo(ctx context.Context, id int64, pipelineId int64) ([]*ObsPackageInfo, error) {
	_result, _retErr := func() ([]*ObsPackageInfo, error) {
		_sqlText := "select * from obs_package_info where dev_id=? and pipeline_id=?"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, id, pipelineId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []*ObsPackageInfo
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = new(ObsPackageInfo)
			_err = _db.ScanRows(_rows, _ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetObsShadowTask(ctx context.Context, id int64) (ObsShadowProject, error) {
	_result, _retErr := func() (ObsShadowProject, error) {
		_sqlText := "select * from obs_shadow_project where dev_id=? and deleted=0"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return ObsShadowProject{}, _sdb.Error
		}
		var _ret ObsShadowProject
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return ObsShadowProject{}, _sdb.Error
		}
		if _sdb.RowsAffected <= 0 {
			return _ret, gorm.ErrRecordNotFound
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetRepoProjectByBranchId(ctx context.Context, id int64) ([]*AospRepoProject, error) {
	_result, _retErr := func() ([]*AospRepoProject, error) {
		_sqlText := "select * from aosp_repo_project where branch_id=?"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []*AospRepoProject
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = new(AospRepoProject)
			_err = _db.ScanRows(_rows, _ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_ReadCRUDRepoStruct) GetRepoProjectByRevision(ctx context.Context, projectName string, revision string) ([]*AospRepoProject, error) {
	_result, _retErr := func() ([]*AospRepoProject, error) {
		_sqlText := "select * from aosp_repo_project where project_name=? and revision=?"
		_db := interstruct.handler.WithContext(ctx)
		_sdb := _db.Raw(_sqlText, projectName, revision)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []*AospRepoProject
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret = new(AospRepoProject)
			_err = _db.ScanRows(_rows, _ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
