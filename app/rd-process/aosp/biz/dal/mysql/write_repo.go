package mysql

import (
	"context"
	"gorm.io/gorm"
)

/*
import (

	"gorm.io/gorm"

)
*/
type WriteCRUDRepo interface {
	// transaction(action="begin")
	Begin(ctx context.Context) (WriteCRUDRepo, error)
	// transaction(action="commit")
	Commit(ctx context.Context) (*gorm.DB, error)
	// transaction(action="rollback")
	Rollback(ctx context.Context) (*gorm.DB, error)
	// transaction(action="do_transaction")
	DoTransaction(ctx context.Context, fn func(ctx context.Context, inter WriteCRUDRepo) error) error

	/*
		sql(value="update application_info set deleted=1 where id=?", mode="placeholder")
	*/
	DeleteApplicationById(ctx context.Context, id int64) (int64, error)

	/*
		updater(v2="true", skipZero="true")
	*/
	UpdateBranchConfig(ctx context.Context, BranchConfig *AospBranchConfig) (int64, error)

	/*
		inserter()
	*/
	InsertBranchConfig(ctx context.Context, BranchConfig *AospBranchConfig) (int64, error)

	/*
		sql(value="update aosp_branch_config set deleted=1 where id=?",  mode="placeholder")
	*/
	DeleteBranchConfigById(ctx context.Context, id int64) (int64, error)

	/*
		updater(v2="true", skipZero="true")
	*/
	UpdateBuildTarget(ctx context.Context, BuildTarget *AospBuildTarget) (int64, error)

	/*
		inserter()
	*/
	InsertBuildTarget(ctx context.Context, BuildTarget *AospBuildTarget) (int64, error)

	/*
		sql(value="update aosp_build_target set deleted=1 where id=?",  mode="placeholder")
	*/
	DeleteBuildTargetById(ctx context.Context, id int64) (int64, error)

	/*
		bulkInserter(chunkSize="256", options='{"useId":true}')
	*/
	BatchInsertBuildTarget(ctx context.Context, BuildTarget []AospBuildTarget) (int64, error)

	/*
		bulkInserter(chunkSize="256", options='{"useId":true}')
	*/
	BatchInsertRepoProject(ctx context.Context, RepoProject []AospRepoProject) (int64, error)

	/*
		sql(value="delete from aosp_repo_project where branch_id=? and id in (?)",  mode="placeholder")
	*/
	BatchDeleteRepoProject(ctx context.Context, BranchId int64, ids []int64) (int64, error)

	/*
		inserter()
	*/
	InsertGerritDevContext(ctx context.Context, o *GerritDevContext) error

	/*
		bulkInserter(chunkSize="256")
	*/
	BatchInsertGerritUnits(ctx context.Context, o []GerritUnit) (int64, error)

	/*
		sql(value="update gerrit_unit set is_deleted=1 where id in (?)", mode="placeholder")
	*/
	BatchDeleteGerritUnits(ctx context.Context, ids []int64) (int64, error)

	/*
		sql(value="select * from gerrit_dev_context where dev_id=?", mode="placeholder")
	*/
	GetGerritDevContext(ctx context.Context, devId int64) (GerritDevContext, error)

	/*
		sql(value="select id,app_id,dev_id,creator_email,status from gerrit_dev_context where dev_id=?", mode="placeholder")
	*/
	GetGerritDevContextStatus(ctx context.Context, devId int64) (GerritDevContext, error)

	/*
		sql(value="update gerrit_dev_context set context=? where dev_id=?", mode="placeholder")
	*/
	UpdateGerritDevContext(ctx context.Context, context string, devId int64) (int64, error)

	/*
		sql(value="update gerrit_dev_context set status=? where dev_id=?", mode="placeholder")
	*/
	UpdateGerritDevStatus(ctx context.Context, status string, devId int64) (int64, error)

	/*
		sql(value="select * from gerrit_unit where dev_id in (?) and is_deleted=0", mode="placeholder")
	*/
	ListGerritUnits(ctx context.Context, devIds []int64) ([]GerritUnit, error)

	/*
		sql(value="select dev_id from gerrit_unit where repo_name=? and gerrit_number_id=? and is_deleted=0 order by id desc limit 1", mode="placeholder")
	*/
	QueryGerritUnitByNumberId(ctx context.Context, repoName string, numberId int64) (int64, error)

	/*
		sql(value="select * from gerrit_unit where dev_id=? and is_deleted=0 limit 1", mode="placeholder")
	*/
	RandomGetGerritUnit(ctx context.Context, devId int64) (GerritUnit, error)

	/*
		sql(value="select * from gerrit_unit where id in (?) and is_deleted=0", mode="placeholder")
	*/
	ListGerritUnitsWithIds(ctx context.Context, ids []int64) ([]GerritUnit, error)

	/*
		updater(v2="true", skipZero="true", skipFields=["create_time"])
	*/
	UpdateGerritUnit(ctx context.Context, u *GerritUnit) (int64, error)

	/*
		inserter()
	*/
	InsertObsShadowTask(ctx context.Context, obsShadowProject *ObsShadowProject) (int64, error)

	/*
		sql(value="update obs_shadow_project set deleted=1 where dev_id=?",  mode="placeholder")
	*/
	DeleteObsShadowTask(ctx context.Context, id int64) (int64, error)

	/*
		inserter()
	*/
	InsertObsPackageBuild(ctx context.Context, obPackageBuild *ObsPackageBuild) (int64, error)

	/*
		updater(v2="true", skipZero="true")
	*/
	UpdateObsPackageBuild(ctx context.Context, obPackageBuild *ObsPackageBuild) (int64, error)

	/*
		sql(value="select max(pipeline_id) as pipeline_id,pipeline_type from gerrit_dev_pipeline where dev_id=? group by pipeline_type", mode="placeholder")
	*/
	ListGerritDevLatestPipelines(ctx context.Context, devId int64) ([]GerritDevPipeline, error)

	/*
		sql(value="select pipeline_id from gerrit_dev_pipeline where dev_id=? and pipeline_type=? order by id desc limit 1", mode="placeholder")
	*/
	GetGerritDevLatestPipeline(ctx context.Context, devId int64, pipelineType string) (int64, error)

	/*
		sql(value="select app_id from gerrit_dev_context where dev_id=?", mode="placeholder")
	*/
	TransferDevId2AppId(ctx context.Context, devId int64) (appId int64, err error)

	/*
		sql(value="update gerrit_dev_context set submit=? where dev_id=?", mode="placeholder")
	*/
	UpdateSubmit(ctx context.Context, submit bool, devId int64) (err error)

	/*
		sql(value="select submit from gerrit_dev_context where dev_id=?", mode="placeholder")
	*/
	CheckSubmit(ctx context.Context, devId int64) (r bool, err error)

	/*
		bulkInserter(chunkSize="256", options='{"useId":true}')
	*/
	BatchInsertObsPackageInfo(ctx context.Context, obsPackageInfo []ObsPackageInfo) (int64, error)

	/*
		sql(value="delete from obs_package_info where dev_id=? and pipeline_id=?",  mode="placeholder")
	*/
	BatchDeleteObsPackageInfo(ctx context.Context, devId int64, pipelineId int64) (int64, error)

	/*
		sql(value="select * from aosp_app_binary where app_id=? and project=? and commit_id=? and build_type=? order by id desc limit 1", mode="placeholder")
	*/
	GetAOSPAppInfo(ctx context.Context, appId int64, project string, commitId string, buildType string) (binary AospAppBinary, err error)

	/*
		updater(v2="true", skipZero="true", skipFields=["create_time"])
	*/
	UpdateAOSPAppInfo(ctx context.Context, binary *AospAppBinary) (int64, error)

	/*
		inserter()
	*/
	InsertAOSPAppInfo(ctx context.Context, binary *AospAppBinary) (int64, error)
}
