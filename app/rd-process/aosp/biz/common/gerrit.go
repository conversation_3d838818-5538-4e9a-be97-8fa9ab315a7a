package common

import (
	"code.byted.org/gopkg/logs"
	"context"
	"errors"
	"regexp"
	"strconv"
)

// https://review.byted.org/c/codebase/gerrit_test/+/2105626
// https://stone-review.byted.org/c/pico/PUI/app/settings/settings/+/579945
var (
	regexSolveGerritUrl = regexp.MustCompile(`.*?/c/(.*)?/\+/(.*)`) //
)

func SolveGerritUrl(ctx context.Context, url string) (repo string, numberId int64, err error) {
	result := regexSolveGerritUrl.FindStringSubmatch(url)
	if len(result) != 3 {
		err = errors.New("len(result)!=3")
		logs.CtxError(ctx, err.Error())
		return
	}
	repo = result[1]
	numberId, err = strconv.ParseInt(result[2], 10, 64)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	return
}
