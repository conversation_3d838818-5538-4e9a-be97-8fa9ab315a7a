package projectcomponent

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/uniquekeys"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/components/envcomponent"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/functools/concurrent"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/mapper"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	SCM "code.byted.org/devinfra/hagrid/libs/thirdparty-sdks/scm"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gconv"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"golang.org/x/sync/errgroup"
)

func (c Component) Prepare(ctx context.Context, devBasicId int64, projects []*model.DevDeployConfig, relation *PrepareParams) gresult.R[[]*model.DevDeployConfig] {
	// 1. fill dev_basic_id
	gslice.ForEach(projects, func(v *model.DevDeployConfig) { v.DevBasicId = devBasicId })

	// 2. fill git repo id
	projects, err := FillGitRepoId(ctx, projects).Get()
	if err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}

	// 3. 注册 webhook
	projects, err = registerWebhook4DownRepos(ctx, projects).Get()
	if err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}

	// 4. 设置 SCM 分支
	projects, err = resetProjectScmBranch(ctx, projects, relation).Get()
	if err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)

	}

	// 5. 检查是否有重复的项目
	projects, err = checkDuplicated(ctx, projects, relation).Get()
	if err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}

	// 6. 填充 SCM 分支信息
	projects, err = fillScmInfo(ctx, projects).Get()
	if err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}

	// 7. 获取项目环境配置
	params := &envcomponent.BatchAddLanesConfigParams{
		NodeLanes: gslice.ToMap(relation.DevBasicConfig.GetBizConfig().GetEnvConfig(), func(t *dev.DevTaskEnvSettingItem) (int64, []*dev.NodeConfigEnvLane) {
			return t.NodeId, mapper.GetLanesFromDevTaskEnvSettingItem(t)
		}),
		TemplateId:     relation.DevBasicConfig.DevTemplateId,
		WithGdpTag:     relation.WithGdp,
		DevBasicConfig: relation.DevBasicConfig,
	}
	projects, err = c.envComponent.BatchAddLanesConfig(ctx, projects, params).Get()
	if err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}

	return gresult.OK(projects)
}

func FillGitRepoId(ctx context.Context, configs []*model.DevDeployConfig) gresult.R[[]*model.DevDeployConfig] {
	var eg errgroup.Group
	var result = concurrent.NewConcurrentSlice[*model.DevDeployConfig](len(configs))
	for i := range configs {
		j := i
		eg.Go(func() error {
			config := configs[j]
			buildCfg := config.GetBuildConfig()
			for idx, scm := range buildCfg.GetScmDependencies() {
				// 存在非 Codebase 仓库（gerrit）但是还有 repoName 的情况， 且 repoName 不是 Codebase 两段式的结构
				if _, _, err := functools.ParseRepoFromRepoPath(scm.GetGitRepoName()); err != nil || len(scm.GetGitRepoName()) == 0 {
					continue
				}
				// 存在非 Codebase 仓库（gerrit）但是还有 repoName 的情况 | 根据 git server 的返回跳过
				project, err := gitsvr.GetNextCodeRepositoryByPath(ctx, scm.GetGitRepoName()).Get()
				if be := bits_err.ToBizError(err); be != nil && be.Code() == bits_err.GITSERVER.ErrGitlabProjectNotFound.Code() {
					continue
				}
				if err != nil {
					return err
				}
				buildCfg.ScmDependencies[idx].GitRepoId = gptr.Of(project.GetProjectId())
			}

			for idx, repo := range buildCfg.GetRepoDependencies() {
				project, err := gitsvr.GetNextCodeRepositoryByPath(ctx, repo.GetGitRepoName()).Get()
				if err != nil {
					return err
				}
				buildCfg.RepoDependencies[idx].GitRepoId = gptr.Of(project.GetProjectId())
			}
			config.SetBuildConfig(buildCfg)
			result.Append(config)
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}
	log.V2.Info().With(ctx).Str("after").KVs("configs", configs).Emit()
	return gresult.OK(result.ToSlice())
}

// 下游仓库注册 webhook - 为了解决下游仓库分支搜索结果有遗漏的问题 | for 合并编译场景
func registerWebhook4DownRepos(ctx context.Context, configs []*model.DevDeployConfig) gresult.R[[]*model.DevDeployConfig] {
	eg, egCtx := errgroup.WithContext(ctx)
	concurrentSlice := concurrent.NewConcurrentSlice[*model.OptimusProject](len(configs))
	for _, config := range configs {
		hasDownRepos := config.GetBuildConfig() != nil && config.GetBuildConfig().ScmMergeBuildInfo != nil && len(config.GetBuildConfig().ScmMergeBuildInfo.DownRepos) > 0
		if !hasDownRepos {
			continue
		}

		for _, item := range config.GetBuildConfig().GetScmMergeBuildInfo().DownRepos {
			downRepo := item
			eg.Go(func() error {
				codebaseRepo, err := gitsvr.GetNextCodeRepositoryByPath(egCtx, downRepo.GetRepoPath()).Get()
				if err != nil {
					return nil
				}

				// 注册webhook
				projectId := codebaseRepo.GetProjectId()
				codebaseRepoId := gconv.To[int64](codebaseRepo.GetId())
				url := downRepo.GitUrl
				// 判断是否注册过
				err = gitsvr.RegisterWebHookByRepoPath(egCtx, projectId, url)
				if err != nil {
					return err
				}

				// 插入记录
				record := &model.OptimusProject{
					RepoPath:        downRepo.GetRepoPath(),
					GitlabProjectId: projectId,
					CodebaseRepoId:  codebaseRepoId,
				}
				concurrentSlice.Append(record)
				return nil
			})
		}
	}
	if err := eg.Wait(); err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}

	// 查询现有的
	toInsertRecords := concurrentSlice.ToSlice()
	repo := repository.NewOptimusProjectRepository(data.DB)
	codebaseRepoIds := gslice.Map(toInsertRecords, func(t *model.OptimusProject) int64 { return t.CodebaseRepoId })
	existed, err := repo.FindByCodebaseRepoIds(ctx, codebaseRepoIds).Get()
	if err != nil {
		return gresult.OK(configs)
	}
	toInsertRecords = gslice.Filter(toInsertRecords, func(project *model.OptimusProject) bool {
		return gslice.Find(existed, func(t *model.OptimusProject) bool { return t.CodebaseRepoId == project.CodebaseRepoId }).IsNil()
	})
	// 写入新数据
	_ = repo.BatchCreate(ctx, toInsertRecords)
	return gresult.OK(configs)
}

// 为 SCM 填充 commit 信息
func fillScmInfo(ctx context.Context, configs []*model.DevDeployConfig) gresult.R[[]*model.DevDeployConfig] {
	log.V2.Info().With(ctx).Str("before").KVs("configs", configs).Emit()
	var eg errgroup.Group
	var result = concurrent.NewConcurrentSlice[*model.DevDeployConfig](len(configs))
	for i := range configs {
		j := i
		eg.Go(func() error {
			config := configs[j]
			buildCfg := config.GetBuildConfig()
			for idx, scm := range buildCfg.GetScmDependencies() {
				switch scm.GetPubBase() {
				case dev.SCMPubBase_SCM_PUB_BASE_BRANCH:
					if scm.GetGitRepoId() == 0 {
						continue
					}
					buildCfg.ScmDependencies[idx].GitRepoId = gptr.Of(scm.GetGitRepoId())
					gitsvr.GetBranchV2(ctx, scm.GetGitRepoId(), scm.GetRevision()).IfOK(func(codebaseBranch *git_server.GitBranch) {
						buildCfg.ScmDependencies[idx].CommitId = codebaseBranch.GetCommit().GetId()
					})

				case dev.SCMPubBase_SCM_PUB_BASE_VERSION: // scm 版本号这种, 需要从 SCM 平台接口获取 commit id
					rpc.ScmApi.GetRepoVersion(ctx, int64(scm.Id), scm.Revision).IfOK(func(version *SCM.RepoVersion) {
						buildCfg.ScmDependencies[idx].CommitId = version.BaseCommitHash
					})
				}
			}
			config.SetBuildConfig(buildCfg)
			result.Append(config)
			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}

	log.V2.Info().With(ctx).Str("after").KVs("configs", configs).Emit()
	return gresult.OK(result.ToSlice())
}

// 仅设置项目 scm 的 revision
func resetProjectScmBranch(ctx context.Context, configs []*model.DevDeployConfig, relation *PrepareParams) gresult.R[[]*model.DevDeployConfig] {
	params := ScmBranchParams{
		TeamFlowId:    relation.DevBasicConfig.TeamFlowId,
		IntegrationId: relation.IntegrationId,
	}
	changed, err := ResetProjectsScmBranch(ctx, configs, params).Get()
	if err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}
	configs = changed
	return gresult.OK(configs)
}

func checkDuplicated(ctx context.Context, configs []*model.DevDeployConfig, relation *PrepareParams) gresult.R[[]*model.DevDeployConfig] {
	err := CheckDuplicated(configs, relation.ExistedProjects)
	if err != nil {
		return gresult.Err[[]*model.DevDeployConfig](err)
	}
	return gresult.OK(configs)
}

func CheckDuplicated(configs, ExistedProjects []*model.DevDeployConfig) error {
	allProjects := gslice.Concat(configs, ExistedProjects)
	var duplicatedProjects = make([]string, 0)
	var projectMap = make(map[string]*model.DevDeployConfig)
	for _, project := range allProjects {
		key := uniquekeys.UniqueKeyWithControlPlaneOfDeployConfig(project)
		val, ok := projectMap[key]
		if !ok {
			projectMap[key] = project
		} else {
			duplicatedProjects = append(duplicatedProjects, fmt.Sprintf("%v(%v - %v)", val.ProjectName, val.ProjectType, val.ControlPlane))
		}
	}
	if len(duplicatedProjects) == 0 {
		return nil
	} else {
		return bits_err.DEVTASK.ErrDuplicatedProject.AddErrMsg(strings.Join(duplicatedProjects, ","))
	}
}
