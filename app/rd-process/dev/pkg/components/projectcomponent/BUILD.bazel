load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "projectcomponent",
    srcs = [
        "branch.go",
        "component.go",
        "create.go",
        "delete.go",
        "mapper.go",
        "prepare.go",
        "types.go",
        "update.go",
        "util.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/components/projectcomponent",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/dev/kitex_gen/bits/integration/multi",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/dev",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/git_server",
        "//app/rd-process/dev/pkg/common/consts",
        "//app/rd-process/dev/pkg/common/functools",
        "//app/rd-process/dev/pkg/common/uniquekeys",
        "//app/rd-process/dev/pkg/components/envcomponent",
        "//app/rd-process/dev/pkg/dal/mysql/data",
        "//app/rd-process/dev/pkg/dal/mysql/model",
        "//app/rd-process/dev/pkg/dal/mysql/repository",
        "//app/rd-process/dev/pkg/deps/configcenter",
        "//app/rd-process/dev/pkg/deps/env",
        "//app/rd-process/dev/pkg/deps/gatekeepersvr",
        "//app/rd-process/dev/pkg/deps/gitsvr",
        "//app/rd-process/dev/pkg/deps/integrationsvr",
        "//app/rd-process/dev/pkg/deps/meta",
        "//app/rd-process/dev/pkg/deps/releaseticket",
        "//app/rd-process/dev/pkg/functools/concurrent",
        "//app/rd-process/dev/pkg/mapper",
        "//app/rd-process/dev/service/rpc",
        "//idls/byted/devinfra/cd/branching_model_config:branching_model_config_go_proto",
        "//idls/byted/devinfra/cd/change_item:change_item_go_proto",
        "//idls/byted/devinfra/cd/codebase:codebase_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//libs/bits_err",
        "//libs/thirdparty-sdks/scm",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_plugin_dbresolver//:dbresolver",
        "@org_byted_code_bits_hephaestus//pkg/jsons",
        "@org_byted_code_gopkg_logs_v2//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gconv",
        "@org_byted_code_lang_gg//gmap",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "projectcomponent_test",
    srcs = ["update_test.go"],
    embed = [":projectcomponent"],
    deps = [
        "//app/rd-process/dev/pkg/common/functools",
        "//app/rd-process/dev/pkg/dal/mysql/model",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_lang_gg//gresult",
    ],
)
