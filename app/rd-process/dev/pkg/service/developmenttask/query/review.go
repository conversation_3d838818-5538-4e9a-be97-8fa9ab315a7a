package query

import (
	"context"
	"fmt"
	"sync"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/components/changecomponent/codechanges"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/collection/set"
	"code.byted.org/lang/gg/gconv"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"golang.org/x/sync/errgroup"
)

func (s Service) GetCodeReviewInfoByDevBasicId(ctx context.Context, devBasicId int64) gresult.R[[]*ChangeCodeReviewInfo] {
	// 1. 查询 dev_basic_change
	changes, err := s.changeRepository.FindByDevBasicId(ctx, devBasicId).Get()
	if err != nil {
		return gresult.Err[[]*ChangeCodeReviewInfo](err)
	}

	// 2. 查询
	return s.GetCodeReviewInfoByChanges(ctx, changes)
}

func (s Service) GetCodeReviewInfoByChanges(ctx context.Context, changes []*model.DevBasicChange) gresult.R[[]*ChangeCodeReviewInfo] {
	var res = make([]*ChangeCodeReviewInfo, 0)
	if len(changes) == 0 {
		return gresult.OK(res)
	}

	// 1. 查询 空间 ID
	var spaceId int64 = 0
	s.infoRepository.FindById(ctx, changes[0].DevBasicId).IfOK(func(info *model.DevBasicInfo) { spaceId = info.SpaceId })

	// 2. 过滤出有 MR 的变更
	formal := gslice.Filter(changes, func(v *model.DevBasicChange) bool { return !v.IsNotCreated() })
	codeChangeIds := gslice.Map(formal, func(f *model.DevBasicChange) int64 { return f.BizId })

	// 3. 查询 MR
	mrs, err := gitsvr.BatchGetMrsByCodeChangeIds(ctx, codeChangeIds).Get()
	if err != nil {
		return gresult.Err[[]*ChangeCodeReviewInfo](err)
	}

	// 4. 查询 Review 信息
	var eg errgroup.Group
	var lock sync.Mutex
	for idx := range formal {
		change := formal[idx]
		mr, ok := gslice.Find(mrs, func(request *git_server.CodeChangeGitlabMergeRequest) bool {
			return request.CodeChangeID == change.BizId
		}).Get()
		if !ok {
			continue
		}
		element := codechanges.FillManifestWithCodeChangeDetail(change, &git_server.CodeChangeDetail{GitlabMr: mr}, nil).GetCodeElement()
		info := &ChangeCodeReviewInfo{
			RepoName:       element.GetRepoPath(),
			Source:         element.GetSourceBranch(),
			Target:         mr.GetTargetBranch(),
			LatestCommitId: mr.GetLastCommitID(),
			Title:          mr.GetTitle(),
			CodeChangeID:   change.BizId,
			CodeElement:    element,
			DevChangeID:    change.Id,
		}
		eg.Go(func() error {
			resp := info
			var ieg errgroup.Group
			// review info
			ieg.Go(func() error {
				reviewInfo, e := GetCodeReviewInfoByCodeChangeId(ctx, change.BizId, change.Author, spaceId).Get()
				if e != nil {
					return e
				}
				resp.ReviewInfo = reviewInfo
				return nil
			})
			if err = ieg.Wait(); err != nil {
				return err
			}
			lock.Lock()
			defer lock.Unlock()
			res = append(res, resp)
			return nil
		})
	}
	if err = eg.Wait(); err != nil {
		return gresult.Err[[]*ChangeCodeReviewInfo](err)
	}
	return gresult.OK(res)
}

func (s Service) GetDevTaskReviewBasicInfo(ctx context.Context, devBasicId int64, reviewer *string) gresult.R[*dev.DevTaskReviewBasicInfo] {
	resp := &dev.DevTaskReviewBasicInfo{
		DevBasicId: devBasicId,
	}

	// 1. 查询 dev_basic_change
	changes, err := s.changeRepository.FindByDevBasicId(ctx, devBasicId).Get()
	if err != nil {
		return gresult.Err[*dev.DevTaskReviewBasicInfo](err)
	}
	if len(changes) == 0 {
		return gresult.OK(dev.NewDevTaskReviewBasicInfo())
	}

	// 2. 没有 MR 的变更单独处理
	draftChanges := gslice.Filter(changes, func(change *model.DevBasicChange) bool { return change.IsNotCreated() })
	for _, item := range draftChanges {
		changeReviewBasicInfo := &dev.DevCodeChangeReviewBasicInfo{
			DevBasicChangeId: item.Id,
			RepoPath:         item.RepoPath,
		}
		resp.CodeChangeReviewBasicInfos = append(resp.CodeChangeReviewBasicInfos, changeReviewBasicInfo)
	}

	// 3. 有 MR 的变更单独处理
	changes = gslice.Filter(changes, func(change *model.DevBasicChange) bool { return !change.IsNotCreated() })
	changeMap := gslice.ToMap(changes, func(t *model.DevBasicChange) (int64, *model.DevBasicChange) { return t.BizId, t })
	mrs, err := gitsvr.BatchGetMrsByCodeChangeIds(ctx, gmap.Keys(changeMap)).Get()
	if err != nil {
		return gresult.Err[*dev.DevTaskReviewBasicInfo](err)
	}
	req := gslice.Map(mrs, func(mr *git_server.CodeChangeGitlabMergeRequest) *getChangeReviewBasicInfoRequest {
		return &getChangeReviewBasicInfoRequest{
			CodeChangeGitlabMergeRequest: mr,
			changeId:                     changeMap[mr.CodeChangeID].Id,
			username:                     reviewer,
		}
	})
	var spaceId int64 = 0
	s.infoRepository.FindById(ctx, devBasicId).IfOK(func(info *model.DevBasicInfo) { spaceId = info.SpaceId })
	getter := choose.If(tcc.IsUsingNextCodeApi(ctx, spaceId), getChangeReviewBasicInfoV2, getChangeReviewBasicInfo)
	changeReviews, err := utils.MCall(ctx, req, getter)
	if err != nil {
		return gresult.Err[*dev.DevTaskReviewBasicInfo](err)
	}

	for i, changeReview := range changeReviews {
		changeReviews[i].DevBasicChangeId = changeMap[changeReview.CodeChangeId].Id
		changeReviews[i].RepoPath = changeMap[changeReview.CodeChangeId].RepoPath
	}
	// 不阻塞返回，因为 search change 可能会失败，如果直接报错会导致用户看不到变更
	fillNeedReviewField(ctx, reviewer, changeReviews)
	resp.CodeChangeReviewBasicInfos = gslice.Concat(resp.CodeChangeReviewBasicInfos, changeReviews)
	return gresult.OK(resp)
}

func getChangeReviewBasicInfo(ctx context.Context, req *getChangeReviewBasicInfoRequest) (*dev.DevCodeChangeReviewBasicInfo, error) {
	var eg errgroup.Group
	var skipRes *git_server.GetCodebaseChangeSkipDataResponse
	var commentCount int64
	var approvals *git_server.ReviewSummary
	var checkSuites = make([]*git_server.CheckSuite, 0)
	var contributionId int64
	eg.Go(func() error {
		var err error
		checkSuites, err = gitsvr.GetCodebaseChangeCheckSuites(ctx, req.GetCodebaseRepoID(), req.GetCodebaseChangeID(), req.username).Get()
		if err != nil {
			return err
		}
		return nil
	})

	eg.Go(func() error {
		var err error
		approvals, err = gitsvr.GetReviewStatus(ctx, req.GetCodebaseRepoID(), req.GetCodebaseChangeID()).Get()
		if err != nil {
			return err
		}
		return nil
	})

	eg.Go(func() error {
		var err error
		skipRes, err = gitsvr.GetCodebaseChangeSkipData(ctx, req.GetCodebaseRepoID(), req.GetCodebaseChangeID(), req.GetLastCommitID()).Get()
		if err != nil {
			return err
		}
		return nil
	})

	eg.Go(func() error {
		var err error
		commentCount, err = gitsvr.GetCommentCount(ctx, req.GetProjectID(), req.GetIid(), nil).Get()
		if err != nil {
			return err
		}
		return nil
	})

	err := eg.Wait()
	if err != nil {
		return nil, err
	}

	resp := &dev.DevCodeChangeReviewBasicInfo{
		CodeChangeId:     req.GetCodeChangeID(),
		CommentCount:     commentCount,
		ReviewSkipped:    gslice.Find(skipRes.GetSkips(), func(t *git_server.SkipData) bool { return t.Type == git_server.CodebaseSkipType_review }).IsOK(),
		CiCheckSkipped:   gslice.Find(skipRes.GetSkips(), func(t *git_server.SkipData) bool { return t.Type == git_server.CodebaseSkipType_check }).IsOK(),
		RepoId:           req.ProjectID,
		ContributionId:   contributionId,
		Iid:              req.Iid,
		CodebaseRepoId:   req.CodebaseRepoID,
		CodebaseChangeId: req.CodebaseChangeID,
	}

	resp.Status = calculateReviewStatus(approvals, resp.ReviewSkipped)
	resp.CiChckStatus = choose.If(resp.CiCheckSkipped, dev.CodeChangeCheckStatus_succeeded, GetCiCheckStatus(checkSuites))
	return resp, nil
}

func getChangeReviewBasicInfoV2(ctx context.Context, req *getChangeReviewBasicInfoRequest) (*dev.DevCodeChangeReviewBasicInfo, error) {
	var (
		eg       errgroup.Group
		mr       *git_server.MergeRequestV2
		bypasses []*git_server.Bypass
	)

	repoId := req.GetCodebaseRepoID()
	changeId := req.GetCodebaseChangeID()
	commitId := req.GetLastCommitID()
	username := gptr.Indirect(req.username)

	// 1. 查询 Merge Request
	eg.Go(func() error {
		opts := []gitsvr.GetMergeRequestOption{
			gitsvr.WithReviewInfo(),
			gitsvr.WithCommentCount(),
			gitsvr.WithCheckRunSummaryStatus(),
		}
		res := gitsvr.GetMergeRequestV2(ctx, repoId, changeId, opts...)
		res.IfOK(func(v2 *git_server.MergeRequestV2) { mr = v2 })
		return res.Err()
	})

	// 2. 查询跳过情况
	eg.Go(func() error {
		res := gitsvr.ListMergeRequestBypasses(ctx, repoId, changeId, commitId, username)
		res.IfOK(func(list []*git_server.Bypass) { bypasses = list })
		return res.Err()
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	// 3. 组装结果
	resp := &dev.DevCodeChangeReviewBasicInfo{
		DevBasicChangeId: 0,  // 别处填充
		RepoPath:         "", // 别处填充
		Status:           gitServerMergeRequestReviewStatusToDevReviewStatus(mr.GetReviewInfo().GetStatus()),
		CommentCount:     gconv.To[int64](mr.GetThreadCount().GetTotal()),
		CiChckStatus:     gitServerMergeRequestCheckRunSummaryStatusToDevCiCheckStatus(mr.GetCheckRunSummaryStatus()),
		ReviewSkipped:    gslice.Find(bypasses, func(t *git_server.Bypass) bool { return t.GetTargetType() == git_server.BypassTargetType_Review }).IsOK(),
		CodeChangeId:     req.GetCodeChangeID(),
		RepoId:           req.ProjectID,
		Iid:              req.Iid,
		CodebaseRepoId:   req.CodebaseRepoID,
		CodebaseChangeId: req.CodebaseChangeID,
		NeedReview:       nil, // 别处填充
	}
	resp.CiCheckSkipped = resp.CiChckStatus == dev.CodeChangeCheckStatus_succeeded && gslice.Any(bypasses, func(t *git_server.Bypass) bool { return t.GetTargetType() == git_server.BypassTargetType_AppCheck })
	return resp, nil
}

// GetCiCheckStatus 先判断是否正在执行中，在判断结果是否通过
func GetCiCheckStatus(checkSuites []*git_server.CheckSuite) dev.CodeChangeCheckStatus {
	// checkSuite 是总的检查项；checkRun 是检查运行记录
	if len(checkSuites) == 0 {
		return dev.CodeChangeCheckStatus_succeeded
	}

	// 是否在执行中
	if gslice.Find(checkSuites, func(suite *git_server.CheckSuite) bool { return consts.IsCodebaseCheckRunning(suite.GetStatusStr()) }).IsOK() {
		return dev.CodeChangeCheckStatus_running
	}

	// 是否结果不通过
	if gslice.Find(checkSuites, func(suite *git_server.CheckSuite) bool { return consts.IsCodebaseCheckFailed(suite.GetConclusionStr()) }).IsOK() {
		return dev.CodeChangeCheckStatus_failed
	}
	return dev.CodeChangeCheckStatus_succeeded
}

func fillNeedReviewField(ctx context.Context, username *string, reviewInfos []*dev.DevCodeChangeReviewBasicInfo) []*dev.DevCodeChangeReviewBasicInfo {
	if username == nil {
		return reviewInfos
	}

	needReviewMr := set.New[string]()
	// 查询需要 review 的 mr
	req := &git_server.SearchChangeRequest{
		Options: &git_server.SearchChangeOptions{
			PendingReviewUsername: username,
			Status:                git_server.ChangeStatePtr(git_server.ChangeState_open),
		},
	}
	needReviewChanges, searchErr := gitsvr.SearchCodebaseChange(ctx, req).Get()
	if searchErr != nil {
		log.V2.Error().With(ctx).Error(searchErr).Str("failed to search needReviewChanges:").Emit()
		return reviewInfos
	}
	for _, change := range needReviewChanges {
		needReviewMr.Add(fmt.Sprintf("%d_%d", change.Target.Repo.Id, change.Id))
	}
	gslice.ForEach(reviewInfos, func(change *dev.DevCodeChangeReviewBasicInfo) {
		if change.CodebaseRepoId != nil && change.CodebaseChangeId != nil {
			change.NeedReview = gptr.Of(needReviewMr.Contains(fmt.Sprintf("%d_%d", *change.CodebaseRepoId, *change.CodebaseChangeId)))
		} else {
			change.NeedReview = gptr.Of(false)
		}
	})
	return reviewInfos
}

// GetCodeReviewInfoByCodeChangeId 不依赖 yacr , 直接使用 codebase api
func GetCodeReviewInfoByCodeChangeId(ctx context.Context, codeChangeId int64, username string, spaceId int64) gresult.R[*dev.ReviewInfo] {
	var eg errgroup.Group
	var reviews = make([]*git_server.CodebaseReview, 0)
	var reviewers = make([]*git_server.CodebaseUser, 0)
	var approvals = git_server.NewReviewSummary()
	var reviewPassed bool
	var crSkipped bool
	svr := gitsvr.NewGitSvrCodeChange(codeChangeId)
	// 如果开启了，就使用接口，否则使用老逻辑
	if tcc.IsUsingNextCodeApi(ctx, spaceId) {
		detail, err := svr.GetCodeChangeDetail(ctx).Get()
		if err != nil {
			return gresult.Err[*dev.ReviewInfo](err)
		}
		return GetCodeReviewInfo(ctx, detail.GetGitlabMr().GetCodebaseRepoID(), detail.GetGitlabMr().GetCodebaseChangeID(), detail.GetGitlabMr().GetLastCommitID(), username)
	}

	eg.Go(func() error {
		var err error
		reviews, err = svr.GetCodebaseReview(ctx).Get()
		if err != nil {
			return err
		}
		return nil
	})

	eg.Go(func() error {
		var err error
		reviewers, err = svr.GetCodebaseRequestedReview(ctx).Get()
		if err != nil {
			return err
		}
		return nil
	})

	eg.Go(func() error {
		submittable, gErr := svr.GetCodebaseChangeSubmittable(ctx, username).Get()
		if gErr != nil {
			return gErr
		}
		reviewPassed = submittable.GetReviewPassed()
		return nil
	})

	eg.Go(func() error {
		var err error
		approvals, err = svr.GetCodebaseApprovals(ctx).Get()
		if err != nil {
			return err
		}
		return nil
	})

	eg.Go(func() error {
		detail, err := gitsvr.GetCodeChangeDetail(ctx, codeChangeId).Get()
		if err != nil {
			return err
		}

		resp, err := gitsvr.GetCodebaseChangeSkipData(ctx,
			detail.GetGitlabMr().GetCodebaseRepoID(),
			detail.GetGitlabMr().GetCodebaseChangeID(),
			detail.GetGitlabMr().GetLastCommitID()).
			Get()
		if err != nil {
			return err
		}
		crSkipped = gslice.Find(resp.GetSkips(), func(skipData *git_server.SkipData) bool { return skipData.Type == git_server.CodebaseSkipType_review }).IsOK()
		return nil
	})
	err := eg.Wait()
	if err != nil {
		return gresult.Err[*dev.ReviewInfo](err)
	}

	// form resp
	resp := formReviewInfo(reviews, reviewers, approvals, reviewPassed)
	resp.Skipped = gptr.Of(crSkipped)
	return gresult.OK(resp)
}

func formReviewInfo(reviews []*git_server.CodebaseReview, reviewers []*git_server.CodebaseUser, approval *git_server.ReviewSummary, reviewPassed bool) *dev.ReviewInfo {
	skipped := reviewPassed && (ReviewSummaryStatus(approval.Status).ToDevReviewStatus() != dev.ReviewStatus_APPROVED)
	res := &dev.ReviewInfo{
		ReviewersInfo: toDevReviewersInfo(reviews, reviewers),
		ReviewStatus:  calculateReviewStatus(approval, reviewPassed),
		ReviewRules:   toDevReviewRules(approval),
		Skipped:       gptr.Of(skipped),
	}

	getReviewNum := func(status dev.ReviewStatus) int64 {
		filtered := gslice.Filter(res.ReviewersInfo, func(info *dev.ReviewerInfo) bool { return info.Status == status })
		return int64(len(filtered))
	}

	res.ReviewerCount = &dev.ReviewerCount{
		Total:           int64(len(res.ReviewersInfo)),
		PassNumber:      getReviewNum(dev.ReviewStatus_APPROVED),
		RejectionNumber: getReviewNum(dev.ReviewStatus_REJECTED),
	}
	return res
}

type ReviewEvent = string

const (
	ReviewEventApprove    ReviewEvent = "approve"
	ReviewEventDisapprove ReviewEvent = "disapprove"
	ReviewEventRevoke     ReviewEvent = "revoke"
)

func codebaseReviewToDevReviewerInfo(review *git_server.CodebaseReview) *dev.ReviewerInfo {
	res := &dev.ReviewerInfo{
		Username: review.Reviewer.Username,
	}

	switch review.Event {
	case ReviewEventApprove:
		res.Status = dev.ReviewStatus_APPROVED
	case ReviewEventDisapprove:
		res.Status = dev.ReviewStatus_REJECTED
	default:
		res.Status = dev.ReviewStatus_RUNNING
	}

	return res
}

type ReviewSummaryStatus string

const (
	ReviewSummaryStatusPassed  ReviewSummaryStatus = "passed"
	ReviewSummaryStatusPending ReviewSummaryStatus = "pending"
)

func (s ReviewSummaryStatus) ToDevReviewStatus() dev.ReviewStatus {
	switch s {
	case ReviewSummaryStatusPassed:
		return dev.ReviewStatus_APPROVED
	case ReviewSummaryStatusPending:
		return dev.ReviewStatus_RUNNING

	default:
		return dev.ReviewStatus_RUNNING
	}
}

func toDevReviewRules(approvals *git_server.ReviewSummary) *dev.ReviewRules {
	rules := &dev.ReviewRules{
		Status:       approvals.Status,
		Approvals:    make([]*dev.Approval, 0),
		Disapprovers: gslice.Map(approvals.Disapprovers, func(user *git_server.CodebaseUser) string { return user.Username }),
	}
	for _, approval := range approvals.Approvals {
		originRule := approval.ReviewRule
		reviewRule := &dev.ReviewRule{
			RuleType:          originRule.Type.String(),
			Name:              originRule.Name,
			ApprovalsRequired: int64(originRule.ApprovalsRequired),
			Users:             gslice.Map(originRule.Users, func(user *git_server.CodebaseUser) string { return user.Username }),
			IsDefault:         &originRule.Default,
		}
		rules.Approvals = append(rules.Approvals, &dev.Approval{
			ReviewRule: reviewRule,
			Approvers:  gslice.Map(approval.Approvers, func(user *git_server.CodebaseUser) string { return user.Username }),
		})
	}
	return rules
}

func toDevReviewersInfo(reviews []*git_server.CodebaseReview, reviewers []*git_server.CodebaseUser) []*dev.ReviewerInfo {
	answer := make([]*dev.ReviewerInfo, 0)
	for _, reviewer := range reviewers {
		findRes, ok := gslice.Find(reviews, func(review *git_server.CodebaseReview) bool {
			return review.GetReviewer().GetUsername() == reviewer.Username
		}).Get()
		if ok {
			answer = append(answer, codebaseReviewToDevReviewerInfo(findRes))
		} else {
			answer = append(answer, &dev.ReviewerInfo{
				Username: reviewer.GetUsername(),
				Status:   dev.ReviewStatus_RUNNING,
			})
		}
	}
	return answer
}

func calculateReviewStatus(approvals *git_server.ReviewSummary, skipped bool) dev.ReviewStatus {
	status := choose.If(len(approvals.Disapprovers) > 0, dev.ReviewStatus_REJECTED, ReviewSummaryStatus(approvals.Status).ToDevReviewStatus())
	status = choose.If(skipped, dev.ReviewStatus_APPROVED, status)
	return status
}

func GetCodeReviewInfo(ctx context.Context, repoId int64, changeId int64, commitId, username string) gresult.R[*dev.ReviewInfo] {
	var (
		eg       errgroup.Group
		mr       *git_server.MergeRequestV2
		bypasses []*git_server.Bypass
	)

	// 1. 查询 Merge Request
	eg.Go(func() error {
		res := gitsvr.GetMergeRequestV2(ctx, repoId, changeId, gitsvr.WithReviewInfo())
		res.IfOK(func(v2 *git_server.MergeRequestV2) { mr = v2 })
		return res.Err()
	})

	// 2. 查询跳过情况
	eg.Go(func() error {
		res := gitsvr.ListMergeRequestBypasses(ctx, repoId, changeId, commitId, username)
		res.IfOK(func(list []*git_server.Bypass) { bypasses = list })
		return res.Err()
	})
	if err := eg.Wait(); err != nil {
		return gresult.Err[*dev.ReviewInfo](err)
	}

	// 3. 组装结果
	result := buildReviewInfo(mr, bypasses)
	return gresult.OK(result)
}

func buildReviewInfo(mr *git_server.MergeRequestV2, bypasses []*git_server.Bypass) *dev.ReviewInfo {
	if mr == nil || mr.GetReviewInfo() == nil {
		return nil
	}
	res := &dev.ReviewInfo{
		ReviewersInfo:   buildReviewerByMr(mr),
		ReviewStatus:    gitServerMergeRequestReviewStatusToDevReviewStatus(mr.GetReviewInfo().GetStatus()),
		ReviewerCount:   buildReviewCountByMr(mr),
		ReviewRules:     buildReviewRuleByReviewInfo(mr.GetReviewInfo()),
		ReviewRuleItems: nil, // 前后端开发任务不需要
		Skipped:         gptr.Of(gslice.Find(bypasses, func(bypass *git_server.Bypass) bool { return bypass.TargetType == git_server.BypassTargetType_Review }).IsOK()),
	}
	return res
}

// 构建评审人列表（包含评审状态）
func buildReviewerByMr(info *git_server.MergeRequestV2) []*dev.ReviewerInfo {
	if info == nil || info.GetReviewInfo() == nil {
		return nil
	}
	extraUsername := func(reviewer *git_server.NextCodeUser) string { return reviewer.GetUsername() }
	allReviewers := gslice.Map(info.GetReviewers(), func(reviewer *git_server.Reviewer) string { return reviewer.GetUsername() }) // 所有的人
	approvedReviewers := gslice.Map(info.GetReviewInfo().GetApprovedBy(), extraUsername)                                          // 通过的人
	rejectReviewers := gslice.Map(info.GetReviewInfo().GetDisapprovedBy(), extraUsername)                                         // 拒绝的人
	reviewers := gslice.Map(allReviewers, func(f string) *dev.ReviewerInfo {
		reviewer := &dev.ReviewerInfo{Username: f}
		switch {
		case gslice.Contains(approvedReviewers, f):
			reviewer.Status = dev.ReviewStatus_APPROVED

		case gslice.Contains(rejectReviewers, f):
			reviewer.Status = dev.ReviewStatus_REJECTED

		default:
			reviewer.Status = dev.ReviewStatus_RUNNING
		}
		return reviewer
	})
	return reviewers
}

// 构建评审数量
func buildReviewCountByMr(info *git_server.MergeRequestV2) *dev.ReviewerCount {
	if info == nil {
		return nil
	}
	return &dev.ReviewerCount{
		Total:           int64(len(info.GetReviewers())),
		PassNumber:      int64(len(info.GetReviewInfo().GetApprovedBy())),
		RejectionNumber: int64(len(info.GetReviewInfo().GetDisapprovedBy())),
	}
}

// 构建评审规则
func buildReviewRuleByReviewInfo(info *git_server.ReviewInfo) *dev.ReviewRules {
	if info == nil || len(info.GetReviewRuleGroups()) == 0 {
		return nil
	}

	extraUsername := func(reviewer *git_server.NextCodeUser) string { return reviewer.GetUsername() }
	var result = &dev.ReviewRules{
		Status:       info.GetStatus().String(),
		Approvals:    make([]*dev.Approval, 0),
		Disapprovers: gslice.Map(info.GetDisapprovedBy(), extraUsername),
	}
	for _, group := range info.GetReviewRuleGroups() {
		for _, rule := range group.GetReviewRules() {
			item := &dev.Approval{
				Approvers: gslice.Map(rule.GetApprovedBy(), extraUsername),
				ReviewRule: &dev.ReviewRule{
					Name:              rule.GetName(),
					ApprovalsRequired: gconv.To[int64](rule.GetApprovalsRequired()),
				},
			}
			result.Approvals = append(result.Approvals, item)
		}
	}
	return result
}
