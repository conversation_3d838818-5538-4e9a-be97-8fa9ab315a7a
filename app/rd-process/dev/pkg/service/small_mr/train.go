package small_mr

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/devops/trains"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"context"
)

func GetMergeTrainQueueStatus(ctx context.Context, devID int64) (trains.CodeQueueStatus, error) {
	// only dev mr considered
	contribution, err := data.OptimusDB.Slave.UseCache().GetContributionDevMRByDevID(ctx, devID)
	if err != nil {
		return trains.CodeQueueStatus_idle, err
	}
	if contribution.TrainQueueId == 0 {
		return trains.CodeQueueStatus_idle, nil
	}
	resp, err := rpc.TrainsClient.QueryCodeQueue(ctx, &trains.QueryCodeQueueRequest{
		Id: contribution.TrainQueueId,
	})
	if err != nil {
		return trains.CodeQueueStatus_idle, err
	}
	if resp.GetPipelineId() == 0 {
		return trains.CodeQueueStatus_idle, nil
	}
	return resp.GetStatus(), nil
}
