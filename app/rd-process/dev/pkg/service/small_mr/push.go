package small_mr

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/devops/push"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/gopkg/logs"
	"context"
	"github.com/bytedance/sonic"
)

type refreshData struct {
	DevID  int64  `json:"dev_id" msgpack:"dev_id"`
	Action string `json:"action" msgpack:"action"`
	Source string `json:"source" msgpack:"source"`
}

func PushUpdateDevBasic(ctx context.Context, devID int64, source string) error {
	logs.CtxInfo(ctx, "[dev basic] start to refresh because of %s", source)
	data := refreshData{DevID: devID, Action: "refresh", Source: source}
	encoded, err := sonic.Marshal(data)
	if err != nil {
		logs.CtxError(ctx, "marshal failed error=%s", err.Error())
		return err
	}
	_, err = rpc.PushServiceClient.PushEvent(ctx, &push.PushEventRequest{
		Event: &push.Event{
			Biz:    push.BizDevBasic,
			Action: push.Action_Refresh,
			Data:   string(encoded),
			Source: &source,
		},
		Filter: &push.FilterOption{
			PageFilter: &push.PageFilter{
				Page: push.PageDevDetailPage,
				Params: &push.PageParams{
					DevId: &devID,
				},
			},
		},
	})
	if err != nil {
		logs.CtxError(ctx, "failed to push update event:%s", err.Error())
		return err
	}
	return nil
}
func PushUpdateDevConfig(ctx context.Context, devID int64, source string) error {
	logs.CtxInfo(ctx, "[dev config] start to refresh because of %s", source)

	data := refreshData{DevID: devID, Action: "refresh", Source: source}
	encoded, err := sonic.Marshal(data)
	if err != nil {
		logs.CtxError(ctx, "marshal failed error=%s", err.Error())
		return err
	}
	_, err = rpc.PushServiceClient.PushEvent(ctx, &push.PushEventRequest{
		Event: &push.Event{
			Biz:    push.BizDevConfig,
			Action: push.Action_Refresh,
			Data:   string(encoded),
			Source: &source,
		},
		Filter: &push.FilterOption{
			PageFilter: &push.PageFilter{
				Page: push.PageDevDetailPage,
				Params: &push.PageParams{
					DevId: &devID,
				},
			},
		},
	})
	if err != nil {
		logs.CtxError(ctx, "failed to push update event:%s", err.Error())
		return err
	}
	return nil
}

func PushUpdateDevGraph(ctx context.Context, devID int64, source string) error {
	logs.CtxInfo(ctx, "[dev graph] start to refresh because of %s", source)
	data := refreshData{DevID: devID, Action: "refresh", Source: source}
	encoded, err := sonic.Marshal(data)
	if err != nil {
		logs.CtxError(ctx, "marshal failed error=%s", err.Error())
		return err
	}
	_, err = rpc.PushServiceClient.PushEvent(ctx, &push.PushEventRequest{
		Event: &push.Event{
			Biz:    push.BizDevGraph,
			Action: push.Action_Refresh,
			Data:   string(encoded),
			Source: &source,
		},
		Filter: &push.FilterOption{
			PageFilter: &push.PageFilter{
				Page: push.PageDevDetailPage,
				Params: &push.PageParams{
					DevId: &devID,
				},
			},
		},
	})
	if err != nil {
		logs.CtxError(ctx, "failed to push update event:%s", err.Error())
		return err
	}
	return nil
}
