package small_mr

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/gopkg/logs"
	"context"
	"github.com/bytedance/sonic"
	"github.com/pkg/errors"
)

/*
	CreateGitlabMRTaskConfig mr create config
*/
type CreateGitlabMRTaskConfig struct {
	Title              string            `json:"optimize"`
	Description        string            `json:"note"`
	Squash             bool              `json:"squash"`
	RemoveSourceBranch bool              `json:"remove_source"`
	Labels             []string          `json:"labels"`
	Hosts              []GitlabMRHost    `json:"hosts"`
	MrDependencies     []GitlabMrParam   `json:"mr_dependencies"`
	Type               string            `json:"type"`
	Latest             bool              `json:"latest"`
	TestingDetails     []*TestingDetail  `json:"testing_details"`
	MergeTrainConfig   *MergeTrainConfig `json:"merge_train_config"`
}
type GitlabMrParam struct {
	FlutterModule   bool   `json:"flutter_module"`
	Source          string `json:"source"`
	Target          string `json:"target"`
	ProjectGitlabId int64  `json:"project_gitlab_id"`
}
type GitlabMRHost struct {
	GitlabMrParam
	MrDependencies []GitlabMrParam `json:"mr_dependencies"`
}

type TestingDetail struct {
	Emails       []string `json:"emails"`
	GroupName    string   `json:"group_name"`
	IsNeedQa     bool     `json:"is_need_qa"`
	QaReviewMode int8     `json:"qa_review_mode"`
}

type MergeTrainConfig struct {
	TrainID int64 `json:"train_id"`
}

func (c *CreateGitlabMRTaskConfig) GetProjectIDList() []int64 {
	answer := make([]int64, 0)
	if len(c.Hosts) == 0 {
		return answer
	}
	host := c.Hosts[0]
	answer = append(answer, host.ProjectGitlabId)
	for _, item := range host.MrDependencies {
		answer = append(answer, item.ProjectGitlabId)
	}
	return answer
}

func GetConfigMapByCID(ctx context.Context, cid int64, cType dev.ContributionType) (map[string]interface{}, error) {
	config, err := data.OptimusDB.Slave.UseCache().GetDevCommonConfigByCIDAndCTypeLatest(ctx, cid, int(cType))
	if err != nil {
		return nil, err
	}
	answer := make(map[string]interface{}, 0)
	if err := sonic.UnmarshalString(config.Detail, &answer); err != nil {
		logs.CtxError(ctx, "unmarshal failed error=%s", err.Error())
		return nil, err
	}
	return answer, nil
}
func GetCreateMRConfigByCID(ctx context.Context, cid int64, cType dev.ContributionType) (*CreateGitlabMRTaskConfig, error) {
	config, err := data.OptimusDB.Slave.UseCache().GetDevCommonConfigByCIDAndCTypeLatest(ctx, cid, int(cType))
	if err != nil {
		return nil, err
	}
	answer := &CreateGitlabMRTaskConfig{}
	if err := sonic.UnmarshalString(config.Detail, answer); err != nil {
		return nil, err
	}
	return answer, nil
}
func GetCreateMRConfigByDevID(ctx context.Context, devID int64) (*CreateGitlabMRTaskConfig, error) {
	config, err := data.OptimusDB.Slave.UseCache().GetDevCreationConfigByDevID(ctx, devID)
	if err != nil {
		return nil, err
	}
	answer := &CreateGitlabMRTaskConfig{}
	if err := sonic.UnmarshalString(config.Detail, answer); err != nil {
		return nil, err
	}
	return answer, nil
}

func generateContributionMRConfig(ctx context.Context, projectID int64, sourceBranch, targetBranch string, devConfig *CreateGitlabMRTaskConfig) (string, error) {
	if len(devConfig.Hosts) == 0 {
		return "", errors.New("invalid dev config, no host")
	}
	hostMrConfig := devConfig.Hosts[0]
	title := devConfig.Title
	commitInfo, _ := rpc.GitServerClient.GetCommitInfo(ctx, &git_server.GetCommitInfoRequest{
		ProjectId: int32(projectID),
		Sha:       sourceBranch,
	})
	if commitInfo == nil || commitInfo.Commit == nil || len(commitInfo.Commit.Message) == 0 || len(commitInfo.Commit.Message) > 200 {
		logs.CtxError(ctx, "get branch [%s] commit info failed, use default title=%s", sourceBranch, devConfig.Title)
	} else {
		title = commitInfo.Commit.Message
	}
	params := CreateGitlabMRTaskConfig{
		Title:              title,
		Description:        devConfig.Description,
		Type:               devConfig.Type,
		RemoveSourceBranch: true,
		Hosts: []GitlabMRHost{{
			GitlabMrParam: GitlabMrParam{
				FlutterModule:   false,
				Source:          sourceBranch,
				Target:          targetBranch,
				ProjectGitlabId: hostMrConfig.ProjectGitlabId,
			},
			MrDependencies: make([]GitlabMrParam, 0),
		}},
	}
	// if it is dependency project, add creation config
	if projectID != hostMrConfig.ProjectGitlabId {
		params.Hosts[0].MrDependencies = append(params.Hosts[0].MrDependencies, GitlabMrParam{
			FlutterModule:   false,
			Source:          sourceBranch,
			Target:          targetBranch,
			ProjectGitlabId: projectID,
		})
	}

	answer, err := sonic.MarshalString(&params)
	if err != nil {
		logs.CtxError(ctx, "common config detail marshal failed error=%s", err.Error())
		return "", err
	}
	return answer, nil
}
func updateContributionMRConfig(ctx context.Context, projectID int64, sourceBranch, targetBranch string, config *CreateGitlabMRTaskConfig) (string, error) {
	if !functools.Contains(config.GetProjectIDList(), projectID) {
		config.Hosts[0].MrDependencies = append(config.Hosts[0].MrDependencies, GitlabMrParam{
			FlutterModule:   false,
			Source:          sourceBranch,
			Target:          targetBranch,
			ProjectGitlabId: projectID,
		})
	} else if config.Hosts[0].ProjectGitlabId == projectID {
		config.Hosts[0].Source = sourceBranch
		config.Hosts[0].Target = targetBranch
	}
	commitInfo, _ := rpc.GitServerClient.GetCommitInfo(ctx, &git_server.GetCommitInfoRequest{
		ProjectId: int32(projectID),
		Sha:       sourceBranch,
	})
	if commitInfo == nil || commitInfo.Commit == nil || len(commitInfo.Commit.Message) == 0 || len(commitInfo.Commit.Message) > 200 {
		logs.CtxError(ctx, "get branch [%s] commit info failed or commit message invalid, use default title=%s", sourceBranch, config.Title)
	} else {
		config.Title = commitInfo.Commit.Message
	}
	answer, err := sonic.MarshalString(&config)
	if err != nil {
		logs.CtxError(ctx, "common config detail marshal failed error=%s", err.Error())
		return "", err
	}
	return answer, nil
}

func GetMergeTrainConfigByDevID(ctx context.Context, devID int64) (*MergeTrainConfig, error) {
	config, err := data.OptimusDB.Slave.UseCache().GetDevCreationConfigByDevID(ctx, devID)
	if err != nil {
		return nil, err
	}
	answer := &MergeTrainConfig{}
	root, err := sonic.GetFromString(config.Detail, "merge_train_config")
	if err != nil {
		logs.CtxError(ctx, "get from string failed error=%s", err.Error())
		return nil, err
	}
	mtConfigStr, _ := root.Raw()
	if err := sonic.UnmarshalString(mtConfigStr, answer); err != nil {
		logs.CtxError(ctx, "unmarshal failed error=%s", err.Error())
		return nil, err
	}
	return answer, nil
}
