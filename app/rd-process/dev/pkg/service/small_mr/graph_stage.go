package small_mr

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/devops/trains"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/gopkg/logs"
	"context"
	"errors"
)

func GetMrCreateStageInfo(ctx context.Context, cid int64, cType dev.ContributionType) (*dev.MRCreateStageData, error) {
	tasksInfo, err := GetDevCreateEngineTasksInfo(ctx, 0, cid, cType)
	if err != nil {
		return nil, err
	}
	status := GetDevCreateEngineStatus(ctx, tasksInfo)
	config, err := GetCreateMRConfigByCID(ctx, cid, cType)
	if err != nil {
		return nil, err
	}
	projectIDs := config.GetProjectIDList()
	mrStatusList := make([]*dev.MRStatusInfo, 0)

	projectMrMap := make(map[int64]*optimus.MrMainInfo, 0)
	projectConfigMap := make(map[int64]*GitlabMrParam, 0)
	host := config.Hosts[0]
	projectConfigMap[host.ProjectGitlabId] = &host.GitlabMrParam
	if len(host.MrDependencies) > 0 {
		for _, dep := range host.MrDependencies {
			projectConfigMap[dep.ProjectGitlabId] = &dep
		}
	}
	switch cType {
	case dev.ContributionType_dev_mr:
		bitsMR, _ := GetDevMrByCID(ctx, cid)
		if bitsMR != nil {
			if bitsMR.HostMR != nil {
				projectMrMap[bitsMR.HostMR.GetProjectID()] = bitsMR.HostMR
				go func() {
					_ = SyncMrStateToContributionDevMr(ctx, bitsMR.HostMR.GetMrState(), cid, bitsMR.HostMR.GetAuthorName())
				}()
			}
			if len(bitsMR.DepMRs) > 0 {
				for _, item := range bitsMR.DepMRs {
					projectMrMap[item.GetProjectID()] = item
				}
			}
		}
	default:
		logs.CtxError(ctx, "invalid contribution type")
		return nil, errors.New("invalid contribution type")
	}
	for _, pid := range projectIDs {
		item := &dev.MRStatusInfo{
			ProjectID: pid,
			Status:    status,
		}
		if value, ok := projectMrMap[pid]; ok {
			item.GitURL = value.Url
		}
		if value, ok := projectConfigMap[pid]; ok {
			item.SourceBranch = &value.Source
			item.TargetBranch = &value.Target
		}
		mrStatusList = append(mrStatusList, item)
	}
	tasksMsg := make([]*dev.TaskMsg, 0)
	for _, task := range tasksInfo {
		tasksMsg = append(tasksMsg, &dev.TaskMsg{
			Name:   task.GetName(),
			ErrMsg: task.GetErrMsg(),
		})
	}
	answer := &dev.MRCreateStageData{
		MrList:   mrStatusList,
		TasksMsg: tasksMsg,
	}
	return answer, nil
}
func GetMergeTrainPipelineStageInfo(ctx context.Context, devID int64) (*dev.MergeTrainPipelineStageData, error) {
	config, err := GetMergeTrainConfigByDevID(ctx, devID)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "get merge train pipeline list id:%d", config.TrainID)
	trainResp, err := rpc.TrainsClient.QueryCodeQueuesWithCargos(ctx, &trains.QueryCodeQueuesWithCargosRequest{
		TrainId: config.TrainID,
	})
	if err != nil {
		return nil, err
	}
	ppls := make([]*dev.PipelineStatusInfo, 0)
	for _, queue := range trainResp.Queues {
		pplCtb := make([]*dev.PipelineContributionInfo, 0)
		for _, item := range queue.Cargos {
			pplCtb = append(pplCtb, &dev.PipelineContributionInfo{
				Id:     item.GetDevId(),
				Author: item.GetSubmitter(),
			})
		}
		ppls = append(ppls, &dev.PipelineStatusInfo{
			Id:            queue.GetPipelineId(),
			Status:        queue.GetStatus().String(),
			StartTime:     queue.GetCreatedAt(),
			EndTime:       queue.GetFinishedAt(),
			Contributions: pplCtb,
		})
	}
	total := int64(len(trainResp.Queues))
	answer := &dev.MergeTrainPipelineStageData{
		PipelineList: ppls,
		Total:        total,
	}
	return answer, nil
}
