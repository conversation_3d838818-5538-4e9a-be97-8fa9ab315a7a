package small_mr

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/optimus/infra"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/engine"
	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bytedance_bits_qa_review/kitex_gen/bytedance/bits/qa_review"
	"context"
	"errors"
)

func adaptTaskInfoListToIDL(dataList []*engine.TaskInfo) []*dev.TaskInfo {
	answer := make([]*dev.TaskInfo, 0)
	for _, item := range dataList {
		answer = append(answer, &dev.TaskInfo{
			Name:   item.Name,
			Status: item.Status,
			ErrMsg: item.ErrorMsg,
		})

	}
	return answer
}
func adaptStartReviewConfigToIDL(details []*TestingDetail) []*qa_review.QaTesterDetail {
	answer := make([]*qa_review.QaTesterDetail, 0)
	for _, detail := range details {
		item := &qa_review.QaTesterDetail{
			GroupName:    detail.GroupName,
			Emails:       detail.Emails,
			QaReviewMode: detail.QaReviewMode,
			IsNeedQa:     detail.IsNeedQa,
		}
		answer = append(answer, item)
	}
	return answer
}

func adaptIDLToDevCollection(ctx context.Context, config *dev.CreateDevConfig, dev *infra.Dev, devType dev.DevType, groupName string) (*Collection, error) {
	if dev.Id == 0 {
		logs.CtxError(ctx, "invalid dev id")
		return nil, errors.New("invalid dev id")
	}

	BasicInfo := &model.DevBasicInfo{
		DevId:     dev.Id,
		Title:     config.GetTitle(),
		Author:    dev.Author,
		State:     string(consts.DevStateOpened),
		GroupName: groupName,
		Type:      devType.String(),
	}
	Collaborators := make([]*model.DevCollaborator, 0)

	if len(config.Collaborators) > 0 {
		for _, name := range config.Collaborators {
			Collaborators = append(Collaborators, &model.DevCollaborator{
				DevId: dev.Id,
				Name:  name,
			})
		}
	}

	// add author to collaborators
	if !functools.Contains[string](config.Collaborators, dev.Author) {
		Collaborators = append(Collaborators, &model.DevCollaborator{
			DevId: dev.Id,
			Name:  dev.Author,
		})
	}

	dc := &Collection{
		DevType:       devType,
		Dev:           dev,
		BasicInfo:     BasicInfo,
		CommonConfig:  config.GetConfig(),
		Collaborators: Collaborators,
	}
	return dc, nil
}
