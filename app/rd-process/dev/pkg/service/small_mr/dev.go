package small_mr

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/devops/trains"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/optimus/infra"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	optimusService "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gresult"
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"strconv"
)

type Dev struct {
}
type Collection struct {
	DevType        dev.DevType
	CommonConfig   string
	Dev            *infra.Dev
	BasicInfo      *model.DevBasicInfo
	Collaborators  []*model.DevCollaborator
	ContributionID int64
}

func (d *Dev) Create(ctx context.Context, config *dev.CreateDevConfig, devType dev.DevType, userName string, groupName string) gresult.R[*model.DevIndex] {
	if err := CheckCreateDevMrConfig(ctx, config.GetConfig()); err != nil {
		return gresult.Err[*model.DevIndex](err)
	}
	req := &infra.AddDevRequest{
		Info: &infra.AddDevInfo{
			Title:  config.Title,
			Type:   devType.String(),
			Author: userName,
		},
	}
	devResp, err := rpc.OptimusInfraClient.AddDev(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "add dev failed error=%s", err.Error())
		return gresult.Err[*model.DevIndex](err)
	}

	dc, err := adaptIDLToDevCollection(ctx, config, devResp.Dev, devType, groupName)
	if err != nil {
		return gresult.Err[*model.DevIndex](err)
	}
	if err := data.OptimusDB.Master.Transaction(func(repo *data.Repo) error {
		if err := repo.CreateDevBasicInfo(ctx, dc.BasicInfo); err != nil {
			return err
		}
		if err := repo.CreateDevCollaborators(ctx, dc.Collaborators); err != nil {
			return err
		}
		newConfig, err := d.InitTrain(ctx, dc.CommonConfig)
		if err != nil {
			return err
		}
		dc.CommonConfig = newConfig
		mrHandler := NewMRHandler(dc.Dev.GetId(), dc.CommonConfig, userName).WithRepo(repo)
		info, err := mrHandler.CreateDevMR(ctx)
		if err != nil {
			return err
		}
		dc.ContributionID = info.ContributionID
		return nil
	}); err != nil {
		logs.CtxError(ctx, "create dev transaction failed error=%s", err.Error())
		return gresult.Err[*model.DevIndex](err)
	}
	timelineData := "Dev creation succeed"
	if err := optimusService.CreateDevTimeline(ctx, devResp.Dev.Id, "third_event", "primary", timelineData, userName); err != nil {
		logs.CtxError(ctx, "create timeline failed error=%s", err.Error())
	}
	devIndex := &model.DevIndex{
		DevBasicID: dc.BasicInfo.Id,
		DevID:      devResp.GetDev().GetId(),
	}
	return gresult.OK(devIndex)
}

func (d *Dev) InitTrain(ctx context.Context, config string) (string, error) {
	devConfig := &CreateGitlabMRTaskConfig{}
	if err := sonic.UnmarshalString(config, devConfig); err != nil {
		logs.CtxError(ctx, "config unmarshal failed error=%s", err.Error())
		return "", err
	}
	// Check if train initialization is required
	var needInit bool
	var pplTemplateID int64
	mtConfig, err := tcc.GetMergeTrainConfig(ctx)
	if err != nil {
		return "", err
	}
	if v, ok := mtConfig[strconv.FormatInt(devConfig.Hosts[0].ProjectGitlabId, 10)]; ok {
		if functools.Contains(v.BranchList, devConfig.Hosts[0].Target) {
			needInit = true
			pplTemplateID = v.PipelineTemplateID
		}
	}
	if !needInit {
		return config, nil
	}
	//todo get pipeline id from project config
	resp, err := rpc.TrainsClient.InitializeTrain(ctx, &trains.InitializeTrainRequest{
		TargetBranch:       devConfig.Hosts[0].Target,
		ProjectId:          devConfig.Hosts[0].ProjectGitlabId,
		PipelineTemplateId: pplTemplateID,
	})
	if err != nil {
		return "", err
	}
	devConfig.MergeTrainConfig = &MergeTrainConfig{
		TrainID: resp.TrainId,
	}
	devConfigStr, err := sonic.MarshalString(devConfig)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return "", err
	}
	return devConfigStr, nil
}
func (d *Dev) Close(ctx context.Context, devID int64, userName string) error {

	contributionDevMR, err := data.OptimusDB.Slave.UseCache().GetContributionDevMRByDevID(ctx, devID)
	if err != nil {
		return err
	}
	contributionMRs, err := data.OptimusDB.Slave.GetContributionMRByDevIDAndState(ctx, devID, string(consts.ContributionStateOpened))
	if err != nil {
		return err
	}
	if len(contributionMRs) > 0 {
		for _, c := range contributionMRs {
			if c.MrId == 0 {
				continue
			}
			mrHandler := NewMRHandler(devID, "", userName).WithMrInfo(c.MrId).WitContributionInfo(c.Id)
			_ = mrHandler.CloseSmallMR(ctx, false)
		}

		newContribution := &model.BitsContributionMr{
			State: string(consts.ContributionStateClosed),
		}
		err := data.OptimusDB.Master.UpdateContributionMRByDevIDAndState(ctx, newContribution, devID, string(consts.ContributionStateOpened))
		if err != nil {
			return err
		}
	}

	if contributionDevMR.State == consts.MRStateOpened {
		if contributionDevMR.MrId != 0 {
			mrHandler := NewMRHandler(devID, "", userName).WithMrInfo(contributionDevMR.MrId).WitContributionInfo(contributionDevMR.Id)
			err := mrHandler.CloseDevMR(ctx, false)
			if err != nil {
				return err
			}
		}

		newDevContribution := &model.BitsContributionDevMr{
			State: string(consts.ContributionStateClosed),
		}
		err = data.OptimusDB.Master.UpdateContributionDevMRByID(ctx, newDevContribution, contributionDevMR.Id)
		if err != nil {
			return err
		}
	}
	basicInfo := &model.DevBasicInfo{
		State: string(consts.DevStateClosed),
	}
	if err := data.OptimusDB.Master.UpdateDevBasicInfo(ctx, devID, basicInfo); err != nil {
		return err
	}
	go func() {
		_ = PushUpdateDevBasic(ctx, devID, "CloseDev")
	}()
	return nil
}
func (d *Dev) RetryWorkflow(ctx context.Context, devID int64, username string) error {
	contribution, err := data.OptimusDB.Slave.UseCache().GetContributionDevMRByDevID(ctx, devID)
	if err != nil {
		return err
	}
	tasksInfo, err := GetDevCreateEngineTasksInfo(ctx, 0, contribution.Id, dev.ContributionType_dev_mr)
	if err != nil {
		return err
	}
	status := GetDevCreateEngineStatus(ctx, tasksInfo)
	if status != dev.GraphStatus_success {
		devCreateID, err := data.OptimusDB.Slave.UseCache().GetDevContributionCreationConfigIDByCIDAndCType(ctx, contribution.Id, int(dev.ContributionType_dev_mr))
		if err != nil {
			return errors.New("get dev create id failed")
		}
		config, err := data.OptimusDB.Slave.UseCache().GetDevCommonConfigByCIDAndCTypeLatest(ctx, contribution.Id, int(dev.ContributionType_dev_mr))
		if err != nil {
			return err
		}
		mrHandler := NewMRHandler(devID, config.Detail, username).WitContributionInfo(contribution.Id)
		if err := mrHandler.RetryDevMRCreate(ctx, devCreateID); err != nil {
			return err
		}
	} else {
		mrHandler := NewMRHandler(devID, "", username).WithMrInfo(contribution.MrId)
		if err := mrHandler.RetryDevMR(ctx); err != nil {
			return err
		}
	}
	go func() {
		_ = PushUpdateDevGraph(ctx, devID, "RetryDevWorkflow")
		// create timeline
		logs.CtxInfo(ctx, "create timeline after retry")
		operator := username
		timelineData := fmt.Sprintf("Start retry dev workflow")
		_ = optimusService.CreateDevTimeline(ctx, devID, "third_event", "primary", timelineData, operator)
	}()
	return nil
}
func (d *Dev) GetPermission(ctx context.Context, devID int64, username string) gresult.R[*dev.GetDevPermissionResponse] {
	basic, err := data.OptimusDB.Slave.UseCache().GetDevBasicInfoByDevID(ctx, devID)
	if err != nil {
		return gresult.Err[*dev.GetDevPermissionResponse](err)
	}

	canCLose := true
	canEdit := false
	showManualCheckpoint := true

	if basic.State == string(consts.DevStateClosed) {
		canCLose = false
	}
	if basic.Author == username {
		canEdit = true
	}
	answer := &dev.GetDevPermissionResponse{
		CanCLose:             canCLose,
		CanEdit:              &canEdit,
		ShowManualCheckpoint: &showManualCheckpoint,
		MrPermission: &dev.MrPermission{
			CanClose:       false,
			CanDowngradeMr: false,
			CanForceMerge:  false,
			CanMergeBase:   false,
			CanUpgradeMr:   false,
			CanFixConflict: false,
			CanRetry:       true,
			ShowOk:         false,
			ShowCancelOk:   false,
			Status:         "success",
		},
	}
	ctb, err := data.OptimusDB.Slave.UseCache().GetContributionDevMRByDevID(ctx, devID)
	if err != nil {
		return gresult.Err[*dev.GetDevPermissionResponse](err)
	}
	if ctb.MrId == 0 {
		return gresult.OK(answer)
	}
	permissionResp, err := rpc.OptimusClient.GetReviewPermissions(ctx, &optimus.GetReviewPermissionsQuery{
		MrID:           ctb.MrId,
		ActionUsername: username,
	})
	if err != nil {
		logs.CtxError(ctx, "get mr permission failed error=%s", err.Error())
		return gresult.OK(answer)
	}
	mrPermission := &dev.MrPermission{
		CanClose:       permissionResp.GetInfo().GetCanClose(),
		CanDowngradeMr: permissionResp.GetInfo().GetCanDowngradeMr(),
		CanForceMerge:  permissionResp.GetInfo().GetCanForceMerge(),
		CanMergeBase:   permissionResp.GetInfo().GetCanMergeBase(),
		CanUpgradeMr:   permissionResp.GetInfo().GetCanUpgradeMr(),
		CanFixConflict: permissionResp.GetInfo().GetCanFixConflict(),
		CanRetry:       permissionResp.GetInfo().GetCanRetry(),
		ShowOk:         permissionResp.GetInfo().GetShowOk(),
		ShowCancelOk:   permissionResp.GetInfo().GetShowCancelOk(),
		Status:         permissionResp.GetInfo().GetStatus(),
	}
	answer.MrPermission = mrPermission
	return gresult.OK(answer)
}
func (d *Dev) GetConfig(ctx context.Context, devID int64) gresult.R[string] {
	config, err := data.OptimusDB.Slave.UseCache().GetDevCreationConfigByDevID(ctx, devID)
	if err != nil {
		return gresult.Err[string](err)
	}
	return gresult.OK(config.Detail)
}
func GetDevBasicInfo(ctx context.Context, devID int64, withCollaborators bool) (*dev.DevBasicInfo, error) {
	basicInfo, err := data.OptimusDB.Slave.UseCache().GetDevBasicInfoByDevID(ctx, devID)
	if err != nil {
		return nil, err
	}
	count := &dev.ContributionCount{
		Total:      0,
		Unfinished: 0,
	}
	ctbCount, _ := GetDevContributionCount(ctx, devID, true)
	if ctbCount != nil {
		count = ctbCount
	}
	cTime := basicInfo.CreateTime.Unix()
	answer := &dev.DevBasicInfo{
		DevID:             devID,
		Title:             basicInfo.Title,
		Author:            basicInfo.Author,
		State:             basicInfo.State,
		CreateTime:        &cTime,
		ContributionCount: count,
		GroupName:         &basicInfo.GroupName,
	}
	if withCollaborators {
		collaborators, err := data.OptimusDB.Slave.UseCache().GetDevCollaboratorNames(ctx, devID)
		if err != nil {
			return nil, err
		}
		answer.Collaborators = collaborators
	}
	return answer, nil
}

func GetDevBasicInfoByDevSourceBranch(ctx context.Context, branch string) ([]*dev.DevBasicInfo, error) {
	answer := make([]*dev.DevBasicInfo, 0)
	//get mr by branch and opened state
	state := consts.MRStateOpened
	businessType := int64(consts.BusinessTypeDevMR)
	mrs, err := rpc.OptimusClient.GetSmallMrList(ctx, &optimus.GetSmallMrListReq{
		SourceBranch: &branch,
		State:        &state,
		BusinessType: &businessType,
	})
	if err != nil {
		logs.CtxError(ctx, "get mr list failed error=%s", err.Error())
		return nil, err
	}
	if len(mrs.MrIDs) == 0 {
		logs.CtxInfo(ctx, "get mr list empty branch=%s", branch)
		return answer, nil
	}
	contributions, err := data.OptimusDB.Slave.GetContributionDevMRDevIDsByMRIDs(ctx, mrs.MrIDs)
	if err != nil {
		return nil, err
	}
	if len(contributions) == 0 {
		return answer, nil
	}
	for _, item := range contributions {
		basicInfo, err := data.OptimusDB.Slave.UseCache().GetDevBasicInfoByDevID(ctx, item.DevId)
		if err != nil {
			logs.CtxError(ctx, "get dev basic info failed devID=%d error=%s", item.DevId, err.Error())
			continue
		}
		answer = append(answer, &dev.DevBasicInfo{
			DevID:  item.DevId,
			Title:  basicInfo.Title,
			Author: basicInfo.Author,
			State:  basicInfo.State,
		})
	}
	return answer, nil
}

// DevTBD dev with merge train
type DevTBD struct {
}

func (d *DevTBD) Create(ctx context.Context, config *dev.CreateDevConfig, devType dev.DevType, userName string, groupName string) gresult.R[*model.DevIndex] {
	if err := CheckCreateDevMrConfig(ctx, config.GetConfig()); err != nil {
		return gresult.Err[*model.DevIndex](err)
	}
	req := &infra.AddDevRequest{
		Info: &infra.AddDevInfo{
			Title:  config.Title,
			Type:   devType.String(),
			Author: userName,
		},
	}
	devResp, err := rpc.OptimusInfraClient.AddDev(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "add dev failed error=%s", err.Error())
		return gresult.Err[*model.DevIndex](err)
	}

	dc, err := adaptIDLToDevCollection(ctx, config, devResp.Dev, devType, groupName)
	if err != nil {
		return gresult.Err[*model.DevIndex](err)
	}
	if err := data.OptimusDB.Master.Transaction(func(repo *data.Repo) error {
		if err := repo.CreateDevBasicInfo(ctx, dc.BasicInfo); err != nil {
			return err
		}
		if err := repo.CreateDevCollaborators(ctx, dc.Collaborators); err != nil {
			return err
		}
		if err := d.InitTrainAndConfig(ctx, dc.CommonConfig, dc.Dev.GetId(), userName, repo); err != nil {
			return err
		}
		return nil
	}); err != nil {
		logs.CtxError(ctx, "create dev transaction failed error=%s", err.Error())
		return gresult.Err[*model.DevIndex](err)
	}
	timelineData := "Dev creation succeed"
	if err := optimusService.CreateDevTimeline(ctx, devResp.Dev.Id, "third_event", "primary", timelineData, userName); err != nil {
		logs.CtxError(ctx, "create timeline failed error=%s", err.Error())
	}
	devIndex := &model.DevIndex{
		DevBasicID: dc.BasicInfo.Id,
		DevID:      devResp.GetDev().GetId(),
	}
	return gresult.OK(devIndex)
}
func (d *DevTBD) InitTrainAndConfig(ctx context.Context, config string, devID int64, username string, repoOrigin *data.Repo) error {
	repo := &data.OptimusDB.Master
	if repoOrigin != nil {
		logs.CtxInfo(ctx, "init train in transaction")
		repo = repoOrigin
	}
	devConfig := &CreateGitlabMRTaskConfig{}
	if err := sonic.UnmarshalString(config, devConfig); err != nil {
		logs.CtxError(ctx, "config unmarshal failed error=%s", err.Error())
		return err
	}
	//todo get pipeline id from project config
	resp, err := rpc.TrainsClient.InitializeTrain(ctx, &trains.InitializeTrainRequest{
		TargetBranch:       devConfig.Hosts[0].Target,
		ProjectId:          devConfig.Hosts[0].ProjectGitlabId,
		PipelineTemplateId: 2270,
	})
	if err != nil {
		return err
	}
	devConfig.MergeTrainConfig = &MergeTrainConfig{
		TrainID: resp.TrainId,
	}
	devConfigStr, err := sonic.MarshalString(devConfig)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return err
	}
	commonConfig := &model.DevCommonConfig{
		DevId:            devID,
		ContributionId:   0,
		ContributionType: int(dev.ContributionType_none),
		Detail:           devConfigStr,
		Username:         username,
	}
	if err := repo.CreateDevCommonConfig(ctx, commonConfig); err != nil {
		return err
	}
	return nil
}
func (d *DevTBD) Close(ctx context.Context, devID int64, userName string) error {
	//config, err := GetMergeTrainConfigByDevID(ctx, devID, dev.ContributionType_none)
	//if err != nil {
	//	return err
	//}
	//if config.TrainID > 0 {
	//	_, err = rpc.TrainsClient.DestroyTrain(ctx, &trains.DestroyTrainRequest{
	//		TrainId: config.TrainID,
	//	})
	//	if err != nil {
	//		return err
	//	}
	//}
	contributionMRs := make([]*model.BitsContributionMr, 0)
	contributionMRsOpened, err := data.OptimusDB.Slave.GetContributionMRByDevIDAndState(ctx, devID, string(consts.ContributionStateOpened))
	if err != nil {
		return err
	}
	contributionMRs = append(contributionMRs, contributionMRsOpened...)

	contributionMRsOnboard, err := data.OptimusDB.Slave.GetContributionMRByDevIDAndState(ctx, devID, string(consts.ContributionStateOnboard))
	if err != nil {
		return err
	}
	contributionMRs = append(contributionMRs, contributionMRsOnboard...)

	if len(contributionMRs) > 0 {
		for _, c := range contributionMRs {
			if c.MrId == 0 {
				continue
			}
			mrHandler := NewMRHandler(devID, "", userName).WithMrInfo(c.MrId).WitContributionInfo(c.Id)
			_ = mrHandler.CloseSmallMR(ctx, false)
		}

		newContribution := &model.BitsContributionMr{
			State: string(consts.ContributionStateClosed),
		}
		err := data.OptimusDB.Master.UpdateContributionMRByDevIDAndState(ctx, newContribution, devID, string(consts.ContributionStateOpened))
		if err != nil {
			return err
		}
	}

	basicInfo := &model.DevBasicInfo{
		State: string(consts.DevStateClosed),
	}
	if err := data.OptimusDB.Master.UpdateDevBasicInfo(ctx, devID, basicInfo); err != nil {
		return err
	}
	go func() {
		_ = PushUpdateDevBasic(ctx, devID, "CloseDev")
	}()
	return nil
}
func (d *DevTBD) RetryWorkflow(ctx context.Context, devID int64, username string) error {
	logs.CtxInfo(ctx, "retry workflow dev with merge train")
	return nil

}
func (d *DevTBD) GetPermission(ctx context.Context, devID int64, username string) gresult.R[*dev.GetDevPermissionResponse] {
	basic, err := data.OptimusDB.Slave.UseCache().GetDevBasicInfoByDevID(ctx, devID)
	if err != nil {
		return gresult.Err[*dev.GetDevPermissionResponse](err)
	}
	canCLose := true
	canEdit := false
	showManualCheckpoint := false
	if basic.State == string(consts.DevStateClosed) {
		canCLose = false
	}
	if basic.Author == username {
		canEdit = true
	}
	answer := &dev.GetDevPermissionResponse{
		CanCLose:             canCLose,
		CanEdit:              &canEdit,
		ShowManualCheckpoint: &showManualCheckpoint,
	}
	return gresult.OK(answer)
}
func (d *DevTBD) GetConfig(ctx context.Context, devID int64) gresult.R[string] {
	config, err := data.OptimusDB.Slave.UseCache().GetDevCreationConfigByDevID(ctx, devID)
	if err != nil {
		return gresult.Err[string](err)
	}
	return gresult.OK(config.Detail)
}
