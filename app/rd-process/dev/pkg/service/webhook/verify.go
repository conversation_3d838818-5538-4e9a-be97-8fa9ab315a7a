package webhook

import (
	"context"
	"fmt"
	json "github.com/bytedance/sonic"
	"github.com/go-resty/resty/v2"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"net/url"
	"regexp"
	"time"
)

type challengeReq struct {
	Challenge string `json:"challenge"`
	Token     string `json:"token"`
	Type      string `json:"type"`
}

var bytedanceHost = regexp.MustCompile(`((.*feishu.cn)|(.*byted.org)|(.*feishuapp.cn)|(.*bytedance.net)|(.*goofy.app)|(10\.([0-9]{1,3}){1,3}))`)
var blockHost = regexp.MustCompile(`(.*boe.byted.org)`)

func urlSecurityCheck(URL string) bool {
	requestURI, err := url.ParseRequestURI(URL)
	if err != nil {
		return false
	}
	if requestURI.Scheme != "http" && requestURI.Scheme != "https" {
		return false
	}
	if blockHost.MatchString(requestURI.Host) {
		return false
	}
	if !bytedanceHost.MatchString(requestURI.Host) {
		return false
	}
	return true
}

func VerifyUrl(ctx context.Context, verificationToken, url string) (bool, string, error) {
	if !urlSecurityCheck(url) {
		return false, fmt.Sprintf("url is illegal"), nil
	}
	challenge, err := utils.GenerateRandomString(16)
	if err != nil {
		return false, "", err
	}
	res, err := resty.New().SetTimeout(time.Second*2).R().SetContext(ctx).SetHeader("x-bits-verification-token", verificationToken).SetBody(challengeReq{
		Challenge: challenge,
		Token:     verificationToken,
		Type:      "url_verification",
	}).Post(url)
	if err != nil {
		return false, fmt.Sprintf("failed to send request:%s", err.Error()), nil
	}
	if res.StatusCode() != 200 {
		return false, fmt.Sprintf("response code is not 200:%d", res.StatusCode()), nil
	}
	resp := struct {
		Challenge string `json:"challenge"`
	}{}
	if err := json.Unmarshal(res.Body(), &resp); err != nil {
		return false, fmt.Sprintf("failed to unmarshal response:%s", err.Error()), nil
	}
	if resp.Challenge != challenge {
		return false, fmt.Sprintf("challenge is not equal"), nil
	}
	return true, "", nil
}
