package small_mr_v2

import (
	"context"
	"errors"
	"testing"

	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/feature_gate"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/deps"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_task_entity"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/optional"
)

func TestGetStackedChangesBasedOnSpecifiedChange(t *testing.T) {
	t.Skip()
	changeA := &ChangeDetail{
		CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 1}, GitlabMr: nil},
		SourceBranch: "e",
		TargetBranch: "a",
	}
	changeB := &ChangeDetail{
		CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 2}, GitlabMr: nil},
		SourceBranch: "a",
		TargetBranch: "b",
	}
	changeC := &ChangeDetail{
		CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 3}, GitlabMr: nil},
		SourceBranch: "b",
		TargetBranch: "c",
	}
	changeD := &ChangeDetail{
		CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 4}, GitlabMr: nil},
		SourceBranch: "c",
		TargetBranch: "d",
	}
	var changes = []*ChangeDetail{changeA, changeC, changeB, changeD}
	changesRes := NewGetStackedChangesHelper().SetSkipDivergeCommitCheck(true).GetStackedChangesBasedOnSpecifiedChange(context.Background(), changes, changeB, 1)
	stackChangesStr, _ := sonic.MarshalString(changesRes)
	t.Logf("stack: %v", stackChangesStr)
	changesRes = NewGetStackedChangesHelper().SetSkipDivergeCommitCheck(true).GetStackedChangesBasedOnSpecifiedChange(context.Background(), changes, changeB, 2)
	stackChangesStr, _ = sonic.MarshalString(changes)
	t.Logf("stack: %v", stackChangesStr)
	changesRes = NewGetStackedChangesHelper().SetSkipDivergeCommitCheck(true).GetStackedChangesBasedOnSpecifiedChange(context.Background(), changes, changeB, 0)
	stackChangesStr, _ = sonic.MarshalString(changes)
	t.Logf("stack: %v", stackChangesStr)
}

func TestBuildStackedChangesByTrunkBranch(t *testing.T) {
	t.Skip()
	Mock(deps.SendEventFinishPartialMerge).Return(nil).Build()
	changeA := &ChangeDetail{
		CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 1}, GitlabMr: nil},
		SourceBranch: "e",
		TargetBranch: "a",
	}
	changeB := &ChangeDetail{
		CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 2}, GitlabMr: nil},
		SourceBranch: "a",
		TargetBranch: "b",
	}
	changeC := &ChangeDetail{
		CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 3}, GitlabMr: nil},
		SourceBranch: "b",
		TargetBranch: "c",
	}
	changeD := &ChangeDetail{
		CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 4}, GitlabMr: nil},
		SourceBranch: "c",
		TargetBranch: "d",
	}
	var changes = []*ChangeDetail{changeA, changeC, changeB, changeD}
	stackedChanges, unstackedChanges := NewGetStackedChangesHelper().SetSkipDivergeCommitCheck(true).BuildStackedChangesByTrunkBranch(context.Background(), changes, "c")
	stackedChangesStr, _ := sonic.MarshalString(stackedChanges)
	unstackedChangesStr, _ := sonic.MarshalString(unstackedChanges)
	t.Logf("stacked: %v\nunstacked: %v", stackedChangesStr, unstackedChangesStr)
}

func TestGetUpOrDownStackChanges(t *testing.T) {
	changeA := &StackedChange{
		ChangeDetail: &ChangeDetail{
			CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 1}, GitlabMr: nil},
			SourceBranch: "a",
			TargetBranch: "b",
		},
		previous: nil,
	}
	changeB := &StackedChange{
		ChangeDetail: &ChangeDetail{
			CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 2}, GitlabMr: nil},
			SourceBranch: "b",
			TargetBranch: "c",
		},
		previous: changeA,
	}
	changeA.next = changeB
	changeC := &StackedChange{
		previous: changeB,
		ChangeDetail: &ChangeDetail{
			CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 3}, GitlabMr: nil},
			SourceBranch: "c",
			TargetBranch: "d",
		},
	}
	changeB.next = changeC
	changeD := &StackedChange{
		previous: changeC,
		ChangeDetail: &ChangeDetail{
			CodeChange:   &change_entity.CodeChange{Contribution: &model.ContributionCodeChange{Id: 4}, GitlabMr: nil},
			SourceBranch: "d",
			TargetBranch: "e",
		},
	}
	var changes StackedChanges = []*StackedChange{changeA, changeB, changeC, changeD}
	PatchConvey("get stack changes", t, func() {
		PatchConvey("get up stack changes", func() {
			resp := changes.UpStackChanges(changeB.ChangeDetail)
			So(resp.Len(), ShouldEqual, 2)
		})
		PatchConvey("get down stack changes", func() {
			resp := changes.DownStackChanges(changeB.ChangeDetail)
			So(resp.Len(), ShouldEqual, 3)
		})
	})
}

func Test_GetDevStack(t *testing.T) {
	ctx := context.Background()
	devBasicID := int64(1)
	
	PatchConvey("Test GetDevDetail failed", t, func() {
		Mock(GetDevDetail).Return(gresult.Err[*DevDetail](errors.New("GetDevDetail error"))).Build()
		actual, err := GetDevStack(ctx, devBasicID, nil).Get()
		So(actual, ShouldBeNil)
		So(err, ShouldNotBeNil)
	})
	
	PatchConvey("Test GetDevDetail success", t, func() {
		detail := &DevDetail{
			DevTask: &dev_task_entity.DevTask{
				BasicInfo:       &model.DevBasicInfo{},
				Config:          &model.DevSmrConfig{},
				BizConfigEntity: &biz_config.DevBizConfig{},
			},
		}
		Mock(GetDevDetail).Return(gresult.OK(detail)).Build()
		PatchConvey("Test params nil or username empty", func() {
			Mock(feature_gate.IfSpaceUseMergeMRWhenComplete).Return(false).Build()
			Mock(GetSmrChangesByFilterOptions).Return(gresult.Err[[]*ChangeDetail](errors.New("GetSmrChangesByFilterOptions error"))).Build()
			actual, err := GetDevStack(ctx, devBasicID, nil).Get()
			So(actual, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})
	
		PatchConvey("Test GetSmrChangesByFilterOptions failed", func() {
			detail.Config.ProcessMode = dev.ProcessMode_multiplayer_collaboration.String()
			params := &DevStackParams{Username: gptr.Of("user")}
			Mock(feature_gate.IfSpaceUseMergeMRWhenComplete).Return(false).Build()
			Mock(GetSmrChangesByFilterOptions).Return(gresult.Err[[]*ChangeDetail](errors.New("GetSmrChangesByFilterOptions error"))).Build()
			actual, err := GetDevStack(ctx, devBasicID, params).Get()
			So(actual, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})
	
		PatchConvey("Test feature_gate.IfSpaceUseMergeMRWhenComplete is true", func() {
			detail.Config.ProcessMode = dev.ProcessMode_multiplayer_collaboration.String()
			params := &DevStackParams{Username: gptr.Of("user")}
			Mock(feature_gate.IfSpaceUseMergeMRWhenComplete).Return(true).Build()
			openedChanges := []*ChangeDetail{}
			Mock(GetSmrChangesByFilterOptions).Return(gresult.OK(openedChanges)).Build()
			Mock(NewGetStackedChangesHelper().SetSkipDivergeCommitCheck).Return(NewGetStackedChangesHelper()).Build()
			actual, err := GetDevStack(ctx, devBasicID, params).Get()
			So(actual, ShouldNotBeNil)
			So(err, ShouldBeNil)
		})
	
		PatchConvey("Test feature_gate.IfSpaceUseMergeMRWhenComplete is false", func() {
			detail.Config.ProcessMode = dev.ProcessMode_multiplayer_collaboration.String()
			params := &DevStackParams{Username: gptr.Of("user")}
			Mock(feature_gate.IfSpaceUseMergeMRWhenComplete).Return(false).Build()
			openedChanges := []*ChangeDetail{}
			Mock(GetSmrChangesByFilterOptions).Return(gresult.OK(openedChanges)).Build()
			Mock(NewGetStackedChangesHelper().SetSkipDivergeCommitCheck).Return(NewGetStackedChangesHelper()).Build()
			actual, err := GetDevStack(ctx, devBasicID, params).Get()
			So(actual, ShouldNotBeNil)
			So(err, ShouldBeNil)
		})
	})
}

func TestStackedChanges_GetElementByCidAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiverAlias := []*StackedChange{}
			receiver := StackedChanges(receiverAlias)
			var cid int64
			convey.So(func() { _ = receiver.GetElementByCid(cid) }, convey.ShouldNotPanic)
		})
	})

}

func TestStackedChanges_DownStackChangeDetailsAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/optional.O:IsNil()_ret-1 == true
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var oMock optional.O[int]
			mockey.Mock(StackedChanges.Index, mockey.OptUnsafe).Return(oMock).Build()

			var toChangeDetailsRet1Mock []*ChangeDetail
			mockey.Mock(StackedChanges.ToChangeDetails, mockey.OptUnsafe).Return(toChangeDetailsRet1Mock).Build()

			// prepare parameters
			receiverAlias := []*StackedChange{}
			receiver := StackedChanges(receiverAlias)
			var changePtrValueCodeChange change_entity.CodeChange
			changePtrValue := ChangeDetail{CodeChange: &changePtrValueCodeChange}
			change := &changePtrValue

			// run target function and assert
			got1 := receiver.DownStackChangeDetails(change)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestGetStackedChangesHelper_GetStackedChangesBasedOnSpecifiedChangeAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stackedChangesMock StackedChanges
			var getDownStackedChangesInChangesRet2Mock []*ChangeDetail
			mockey.Mock(getDownStackedChangesInChanges).Return(stackedChangesMock, getDownStackedChangesInChangesRet2Mock).Build()

			var getUpStackedChangesInChangesRet1Mock StackedChanges
			var getUpStackedChangesInChangesRet2Mock []*ChangeDetail
			mockey.Mock(getUpStackedChangesInChanges).Return(getUpStackedChangesInChangesRet1Mock, getUpStackedChangesInChangesRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue GetStackedChangesHelper
			receiver := &receiverPtrValue
			ctx := context.Background()
			var changes []*ChangeDetail
			var specifiedChangePtrValueCodeChange change_entity.CodeChange
			specifiedChangePtrValue := ChangeDetail{CodeChange: &specifiedChangePtrValueCodeChange}
			specifiedChange := &specifiedChangePtrValue
			var option dev.RelatedContributionCodeChangeOption

			// run target function and assert
			got1 := receiver.GetStackedChangesBasedOnSpecifiedChange(ctx, changes, specifiedChange, option)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestStackedChange_SetNextAutoGen(t *testing.T) {
	// param1 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue
			var change *StackedChange
			convey.So(func() { receiver.SetNext(change) }, convey.ShouldNotPanic)
		})
	})

	// default condition
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue
			var changePtrValueChangeDetailCodeChange change_entity.CodeChange
			changePtrValueChangeDetail := ChangeDetail{CodeChange: &changePtrValueChangeDetailCodeChange}
			changePtrValue := StackedChange{ChangeDetail: &changePtrValueChangeDetail}
			change := &changePtrValue
			convey.So(func() { receiver.SetNext(change) }, convey.ShouldNotPanic)
		})
	})

}

func TestStackedChange_UpperAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue

			// run target function and assert
			got1 := receiver.Upper()
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestStackedChanges_TopAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiverAlias := []*StackedChange{}
			receiver := StackedChanges(receiverAlias)
			convey.So(func() { _ = receiver.Top() }, convey.ShouldNotPanic)
		})
	})

}

func Test_getUpStackedChangesInChangesAutoGen(t *testing.T) {
	// Verify the function behavior when withSelf is true.
	t.Run("testGetUpStackedChangesInChanges_WithSelfTrue", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var initCanFastForwardRet1Mock error
			mockey.Mock((*ChangeDetail).InitCanFastForward).Return(initCanFastForwardRet1Mock).Build()

			mockey.Mock(InitChangesCanFastForward).Return().Build()

			var changeDetailMockPtrValueCodeChange change_entity.CodeChange
			changeDetailMockPtrValue := ChangeDetail{CodeChange: &changeDetailMockPtrValueCodeChange}
			changeDetailMock := &changeDetailMockPtrValue
			mockey.Mock((*StackedChange).Detail, mockey.OptUnsafe).Return(changeDetailMock).Build()

			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var curChangePtrValueCodeChange change_entity.CodeChange
			curChangePtrValue := ChangeDetail{CodeChange: &curChangePtrValueCodeChange}
			curChange := &curChangePtrValue
			var skipDivergeCommitCheck bool
			withSelf := true
			var useCache bool

			// run target function and assert
			got1, got2 := getUpStackedChangesInChanges(ctx, changes, curChange, skipDivergeCommitCheck, withSelf, useCache)
			convey.So(len(got1), convey.ShouldEqual, 1)
			convey.So(len(got2), convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when certain conditions related to length and values are met.
	t.Run("testGetUpStackedChangesInChanges_LenGreaterThanOne", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var initCanFastForwardRet1Mock error
			mockey.Mock((*ChangeDetail).InitCanFastForward).Return(initCanFastForwardRet1Mock).Build()

			mockey.Mock(InitChangesCanFastForward).Return().Build()

			var changeDetailMockPtrValueCodeChange change_entity.CodeChange
			changeDetailMockPtrValue := ChangeDetail{CodeChange: &changeDetailMockPtrValueCodeChange}
			changeDetailMock := &changeDetailMockPtrValue
			mockey.Mock((*StackedChange).Detail, mockey.OptUnsafe).Return(changeDetailMock).Build()

			// prepare parameters
			var withSelf bool
			var useCache bool
			ctx := context.Background()
			var changes []*ChangeDetail
			var curChangePtrValueCodeChange change_entity.CodeChange
			curChangePtrValue := ChangeDetail{CodeChange: &curChangePtrValueCodeChange}
			curChange := &curChangePtrValue
			var skipDivergeCommitCheck bool

			// run target function and assert
			got1, got2 := getUpStackedChangesInChanges(ctx, changes, curChange, skipDivergeCommitCheck, withSelf, useCache)
			convey.So(len(got2), convey.ShouldEqual, 0)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestStackedChange_LowerAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue

			// run target function and assert
			got1 := receiver.Lower()
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func Test_getDownStackedChangesInChangesAutoGen(t *testing.T) {
	// param5 == true
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var changeDetailMockPtrValueCodeChange change_entity.CodeChange
			changeDetailMockPtrValue := ChangeDetail{CodeChange: &changeDetailMockPtrValueCodeChange}
			changeDetailMock := &changeDetailMockPtrValue
			mockey.Mock((*StackedChange).Detail, mockey.OptUnsafe).Return(changeDetailMock).Build()

			var initCanFastForwardRet1Mock error
			mockey.Mock((*ChangeDetail).InitCanFastForward).Return(initCanFastForwardRet1Mock).Build()

			mockey.Mock(InitChangesCanFastForward).Return().Build()

			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var curChangePtrValueCodeChange change_entity.CodeChange
			curChangePtrValue := ChangeDetail{CodeChange: &curChangePtrValueCodeChange}
			curChange := &curChangePtrValue
			var skipDivergeCommitCheck bool
			withSelf := true
			var useCache bool

			// run target function and assert
			got1, got2 := getDownStackedChangesInChanges(ctx, changes, curChange, skipDivergeCommitCheck, withSelf, useCache)
			convey.So(len(got1), convey.ShouldEqual, 1)
			convey.So(len(got2), convey.ShouldEqual, 0)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.StackedChange:Detail()_ret-1.Contribution != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var initCanFastForwardRet1Mock error
			mockey.Mock((*ChangeDetail).InitCanFastForward).Return(initCanFastForwardRet1Mock).Build()

			mockey.Mock(InitChangesCanFastForward).Return().Build()

			var changeDetailMockPtrValueCodeChange change_entity.CodeChange
			changeDetailMockPtrValue := ChangeDetail{CodeChange: &changeDetailMockPtrValueCodeChange}
			changeDetailMock := &changeDetailMockPtrValue
			mockey.Mock((*StackedChange).Detail, mockey.OptUnsafe).Return(changeDetailMock).Build()

			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var curChangePtrValueCodeChange change_entity.CodeChange
			curChangePtrValue := ChangeDetail{CodeChange: &curChangePtrValueCodeChange}
			curChange := &curChangePtrValue
			var skipDivergeCommitCheck bool
			var withSelf bool
			var useCache bool

			// run target function and assert
			got1, got2 := getDownStackedChangesInChanges(ctx, changes, curChange, skipDivergeCommitCheck, withSelf, useCache)
			convey.So(len(got1), convey.ShouldEqual, 0)
			convey.So(len(got2), convey.ShouldEqual, 0)
		})
	})

}

func TestStackedChanges_DownStackChangesAutoGen(t *testing.T) {
	// Verify the behavior when the index is nil.
	t.Run("testStackedChanges_DownStackChanges_NilIndex", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var lenRet1Mock int
			mockey.Mock(StackedChanges.Len, mockey.OptUnsafe).Return(lenRet1Mock).Build()

			var oMock optional.O[int]
			mockey.Mock(StackedChanges.Index, mockey.OptUnsafe).Return(oMock).Build()

			// prepare parameters
			receiverAlias := []*StackedChange{}
			receiver := StackedChanges(receiverAlias)
			var changePtrValueCodeChange change_entity.CodeChange
			changePtrValue := ChangeDetail{CodeChange: &changePtrValueCodeChange}
			change := &changePtrValue

			// run target function and assert
			got1 := receiver.DownStackChanges(change)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestStackedChange_SetPreviousAutoGen(t *testing.T) {
	// param1 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue
			var change *StackedChange
			convey.So(func() { receiver.SetPrevious(change) }, convey.ShouldNotPanic)
		})
	})

	// param1 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue
			var changePtrValueChangeDetailCodeChange change_entity.CodeChange
			changePtrValueChangeDetail := ChangeDetail{CodeChange: &changePtrValueChangeDetailCodeChange}
			changePtrValue := StackedChange{ChangeDetail: &changePtrValueChangeDetail}
			change := &changePtrValue
			convey.So(func() { receiver.SetPrevious(change) }, convey.ShouldNotPanic)
		})
	})

}

func TestStackedChange_UpStackChangesAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.StackedChange:Lower()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stackedChangeMockPtrValueChangeDetailCodeChange change_entity.CodeChange
			stackedChangeMockPtrValueChangeDetail := ChangeDetail{CodeChange: &stackedChangeMockPtrValueChangeDetailCodeChange}
			stackedChangeMockPtrValue := StackedChange{ChangeDetail: &stackedChangeMockPtrValueChangeDetail}
			stackedChangeMock := &stackedChangeMockPtrValue
			mockey.Mock((*StackedChange).Upper, mockey.OptUnsafe).Return(stackedChangeMock).Build()

			var changeDetailMockPtrValueCodeChange change_entity.CodeChange
			changeDetailMockPtrValue := ChangeDetail{CodeChange: &changeDetailMockPtrValueCodeChange}
			changeDetailMock := &changeDetailMockPtrValue
			mockey.Mock((*StackedChange).Detail, mockey.OptUnsafe).Return(changeDetailMock).Build()

			var lowerRet1MockPtrValueChangeDetailCodeChange change_entity.CodeChange
			lowerRet1MockPtrValueChangeDetail := ChangeDetail{CodeChange: &lowerRet1MockPtrValueChangeDetailCodeChange}
			lowerRet1MockPtrValue := StackedChange{ChangeDetail: &lowerRet1MockPtrValueChangeDetail}
			lowerRet1Mock := &lowerRet1MockPtrValue
			mockey.Mock((*StackedChange).Lower, mockey.OptUnsafe).Return(lowerRet1Mock).Build()

			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.UpStackChanges() }, convey.ShouldPanic)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.StackedChange:Upper()_ret-1 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.StackedChange:Upper()_ret-1 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var lowerRet1MockPtrValueChangeDetailCodeChange change_entity.CodeChange
			lowerRet1MockPtrValueChangeDetail := ChangeDetail{CodeChange: &lowerRet1MockPtrValueChangeDetailCodeChange}
			lowerRet1MockPtrValue := StackedChange{ChangeDetail: &lowerRet1MockPtrValueChangeDetail}
			lowerRet1Mock := &lowerRet1MockPtrValue
			mockey.Mock((*StackedChange).Lower, mockey.OptUnsafe).Return(lowerRet1Mock).Build()

			var stackedChangeMock *StackedChange
			mockey.Mock((*StackedChange).Upper, mockey.OptUnsafe).Return(stackedChangeMock).Build()

			var changeDetailMockPtrValueCodeChange change_entity.CodeChange
			changeDetailMockPtrValue := ChangeDetail{CodeChange: &changeDetailMockPtrValueCodeChange}
			changeDetailMock := &changeDetailMockPtrValue
			mockey.Mock((*StackedChange).Detail, mockey.OptUnsafe).Return(changeDetailMock).Build()

			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue

			// run target function and assert
			got1 := receiver.UpStackChanges()
			convey.So(len(got1), convey.ShouldEqual, 1)
		})
	})

}

func TestDevStack_IsEmptyAutoGen(t *testing.T) {
	// Verify the IsEmpty function under default condition.
	t.Run("testDevStack_IsEmpty", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var lenRet1Mock int
			mockey.Mock(StackedChanges.Len, mockey.OptUnsafe).Return(lenRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue DevStack
			receiver := &receiverPtrValue

			// run target function and assert
			got1 := receiver.IsEmpty()
			convey.So(got1, convey.ShouldEqual, true)
		})
	})

}

func TestDevStack_IsAllStackedAutoGen(t *testing.T) {
	// Verify the IsAllStacked function under default condition.
	t.Run("testDevStack_IsAllStacked", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue DevStack
			receiver := &receiverPtrValue

			// run target function and assert
			got1 := receiver.IsAllStacked()
			convey.So(got1, convey.ShouldEqual, true)
		})
	})

}

func TestStackedChanges_UpStackChangeDetailsAutoGen(t *testing.T) {
	// Verify the function behavior when the index is nil.
	t.Run("testStackedChanges_UpStackChangeDetails_NilIndex", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var toChangeDetailsRet1Mock []*ChangeDetail
			mockey.Mock(StackedChanges.ToChangeDetails, mockey.OptUnsafe).Return(toChangeDetailsRet1Mock).Build()

			var oMock optional.O[int]
			mockey.Mock(StackedChanges.Index, mockey.OptUnsafe).Return(oMock).Build()

			// prepare parameters
			receiverAlias := []*StackedChange{}
			receiver := StackedChanges(receiverAlias)
			var changePtrValueCodeChange change_entity.CodeChange
			changePtrValue := ChangeDetail{CodeChange: &changePtrValueCodeChange}
			change := &changePtrValue

			// run target function and assert
			got1 := receiver.UpStackChangeDetails(change)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestGetStackedChangesHelper_SetUseCacheAutoGen(t *testing.T) {
	// Verify the functionality of SetUseCache method under default condition.
	t.Run("testGetStackedChangesHelper_SetUseCache", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue GetStackedChangesHelper
			receiver := &receiverPtrValue
			var value bool

			// run target function and assert
			got1 := receiver.SetUseCache(value)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestStackedChanges_UpStackChangesAutoGen(t *testing.T) {
	// Verify the UpStackChanges function under default condition.
	t.Run("testStackedChanges_UpStackChanges", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var oMock optional.O[int]
			mockey.Mock(StackedChanges.Index, mockey.OptUnsafe).Return(oMock).Build()

			// prepare parameters
			var changePtrValueCodeChange change_entity.CodeChange
			changePtrValue := ChangeDetail{CodeChange: &changePtrValueCodeChange}
			change := &changePtrValue
			receiverAlias := []*StackedChange{}
			receiver := StackedChanges(receiverAlias)

			// run target function and assert
			got1 := receiver.UpStackChanges(change)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestStackedChange_DownStackChangesAutoGen(t *testing.T) {
	// Verify the functionality of DownStackChanges method with mocked function returns.
	t.Run("testStackedChange_DownStackChanges", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var changeDetailMockPtrValueCodeChange change_entity.CodeChange
			changeDetailMockPtrValue := ChangeDetail{CodeChange: &changeDetailMockPtrValueCodeChange}
			changeDetailMock := &changeDetailMockPtrValue
			mockey.Mock((*StackedChange).Detail, mockey.OptUnsafe).Return(changeDetailMock).Build()

			var stackedChangeMock *StackedChange
			mockey.Mock((*StackedChange).Lower, mockey.OptUnsafe).Return(stackedChangeMock).Build()

			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue

			// run target function and assert
			got1 := receiver.DownStackChanges()
			convey.So(len(got1), convey.ShouldEqual, 1)
		})
	})

	// Verify the panic scenario in DownStackChanges method with mocked function returns.
	t.Run("testStackedChange_DownStackChanges_Panic", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var changeDetailMockPtrValueCodeChange change_entity.CodeChange
			changeDetailMockPtrValue := ChangeDetail{CodeChange: &changeDetailMockPtrValueCodeChange}
			changeDetailMock := &changeDetailMockPtrValue
			mockey.Mock((*StackedChange).Detail, mockey.OptUnsafe).Return(changeDetailMock).Build()

			var stackedChangeMockPtrValueChangeDetailCodeChange change_entity.CodeChange
			stackedChangeMockPtrValueChangeDetail := ChangeDetail{CodeChange: &stackedChangeMockPtrValueChangeDetailCodeChange}
			stackedChangeMockPtrValue := StackedChange{ChangeDetail: &stackedChangeMockPtrValueChangeDetail}
			stackedChangeMock := &stackedChangeMockPtrValue
			mockey.Mock((*StackedChange).Lower, mockey.OptUnsafe).Return(stackedChangeMock).Build()

			// prepare parameters
			var receiverPtrValueChangeDetailCodeChange change_entity.CodeChange
			receiverPtrValueChangeDetail := ChangeDetail{CodeChange: &receiverPtrValueChangeDetailCodeChange}
			receiverPtrValue := StackedChange{ChangeDetail: &receiverPtrValueChangeDetail}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.DownStackChanges() }, convey.ShouldPanic)
		})
	})

}

func TestStackedChanges_IsEmptyAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiverAlias := []*StackedChange{}
			receiver := StackedChanges(receiverAlias)

			// run target function and assert
			got1 := receiver.IsEmpty()
			convey.So(got1, convey.ShouldEqual, true)
		})
	})

}

