package biz_config

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gresult"
	"context"
	"github.com/bytedance/sonic"
)

type BizConfigHandler struct {
	BizConfig string
	NeedSave  bool
}

func (h *BizConfigHandler) ConfigStr() string {
	return h.BizConfig
}

func (h *BizConfigHandler) GetConfigMap(ctx context.Context) gresult.R[map[string]interface{}] {
	answer := make(map[string]interface{}, 0)
	if err := sonic.UnmarshalString(h.BizConfig, &answer); err != nil {
		logs.CtxError(ctx, "get dev smr biz config failed error = %s", err.Error())
		return gresult.Err[map[string]interface{}](err)
	}
	return gresult.OK(answer)
}
