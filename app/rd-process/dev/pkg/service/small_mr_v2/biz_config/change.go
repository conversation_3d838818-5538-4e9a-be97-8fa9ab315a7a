package biz_config

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/value_object"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"context"
	"github.com/bytedance/sonic"
)

type ChangeBizConfig struct {
	Checks              []*value_object.ContributionCheckItem `json:"checks"`
	RemoveSourceBranch  bool                                  `json:"remove_source_branch"`
	MergeTaskBizId      int64                                 `json:"merge_task_biz_id"`
	MergeTaskType       dev.MergeTaskType                     `json:"merge_task_type"`
	AutoMergeEnabled    bool                                  `json:"auto_merge_enabled"`
	MergeTaskOption     dev.ContributionCodeChangeMergeOption `json:"merge_task_option"`
	LatestCommitChecked string                                `json:"latest_commit_checked"`
	MergeTask           *value_object.ChangeMergeTask         `json:"merge_task"`
}

type ChangeBizConfigHandler struct {
	BizConfigHandler
}

func NewChangeBizConfigHandler(str string) *ChangeBizConfigHandler {
	return &ChangeBizConfigHandler{
		BizConfigHandler: BizConfigHandler{
			BizConfig: str,
		},
	}
}

func (h *ChangeBizConfigHandler) GetConfig(ctx context.Context) gresult.R[*ChangeBizConfig] {
	answer := &ChangeBizConfig{}
	if len(h.BizConfig) > 0 && h.BizConfig != "null" {
		if err := sonic.UnmarshalString(h.BizConfig, answer); err != nil {
			logs.CtxError(ctx, "get smr biz config failed error = %s", err.Error())
			return gresult.Err[*ChangeBizConfig](err)
		}
	}
	return gresult.OK(answer)
}

type ChangeBizConfigUpdateOptions struct {
	RemoveSourceBranch   *bool                                  `json:"remove_source_branch"`
	MergeTaskOptimusMrId int64                                  `json:"merge_task_optimus_mr_id"`
	MergeTaskBizId       *int64                                 `json:"merge_task_id"`
	MergeTaskType        *dev.MergeTaskType                     `json:"merge_task_type"`
	AutoMergeEnabled     *bool                                  `json:"auto_merge_enabled"`
	MergeTaskOption      *dev.ContributionCodeChangeMergeOption `json:"merge_task_option"`
	LatestCommitChecked  *string                                `json:"latest_commit_checked"`
	MergeTask            *value_object.ChangeMergeTask          `json:"merge_task"`
}

func (h *ChangeBizConfigHandler) UpdateSelf(ctx context.Context, options *ChangeBizConfigUpdateOptions) (*ChangeBizConfigHandler, error) {
	return h, h.UpdateAndGetConfig(ctx, options).Err()
}

func (h *ChangeBizConfigHandler) UpdateAndGetConfig(ctx context.Context, options *ChangeBizConfigUpdateOptions) gresult.R[*ChangeBizConfig] {
	bizConfig, err := h.GetConfig(ctx).Get()
	if err != nil {
		return gresult.Err[*ChangeBizConfig](err)
	}

	// update biz config
	if options.RemoveSourceBranch != nil {
		bizConfig.RemoveSourceBranch = gptr.Indirect(options.RemoveSourceBranch)
	}
	if options.MergeTaskType != nil {
		bizConfig.MergeTaskType = gptr.Indirect(options.MergeTaskType)
	}
	if options.MergeTaskBizId != nil {
		bizConfig.MergeTaskBizId = gptr.Indirect(options.MergeTaskBizId)
	}
	if options.AutoMergeEnabled != nil {
		bizConfig.AutoMergeEnabled = gptr.Indirect(options.AutoMergeEnabled)
	}
	if options.MergeTaskOption != nil {
		bizConfig.MergeTaskOption = gptr.Indirect(options.MergeTaskOption)
	}
	if options.LatestCommitChecked != nil {
		bizConfig.LatestCommitChecked = gptr.Indirect(options.LatestCommitChecked)
	}
	if options.MergeTask != nil {
		bizConfig.MergeTask = options.MergeTask
	}
	res, err := sonic.MarshalString(bizConfig)
	if err != nil {
		logs.CtxError(ctx, "marshal failed error = %s", err.Error())
		return gresult.Err[*ChangeBizConfig](err)
	}
	h.BizConfig = res
	h.NeedSave = true
	return gresult.OK(bizConfig)
}

func (h *ChangeBizConfigHandler) UpdateAndSave(ctx context.Context, cid int64, options *ChangeBizConfigUpdateOptions) error {
	updateRes := h.UpdateAndGetConfig(ctx, options)
	if updateRes.IsErr() {
		return updateRes.Err()
	}
	return h.Save(ctx, cid)
}

func (h *ChangeBizConfigHandler) Save(ctx context.Context, cid int64) error {
	if err := data.OptimusDB.Master.UpdateContributionCodeChangeByID(ctx, &model.ContributionCodeChange{BizConfig: h.BizConfig}, cid); err != nil {
		logs.CtxError(ctx, err.Error())
		return err
	}
	return nil
}

func (h *ChangeBizConfigHandler) SaveByConfig(ctx context.Context, cid int64, config *ChangeBizConfig) error {
	res, err := sonic.MarshalString(config)
	if err != nil {
		logs.CtxError(ctx, "marshal failed error = %s", err.Error())
		return err
	}
	h.BizConfig = res
	return h.Save(ctx, cid)
}

func (c *ChangeBizConfig) ReplaceChecks(ctx context.Context, checks []*value_object.ContributionCheckItem) gresult.R[string] {
	c.Checks = checks
	bizConfigStrNew, err := sonic.MarshalString(c)
	if err != nil {
		logs.CtxError(ctx, "marshal failed error = %s", err.Error())
		return gresult.Err[string](err)
	}
	return gresult.OK(bizConfigStrNew)
}
