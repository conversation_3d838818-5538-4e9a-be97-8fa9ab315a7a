package small_mr_v2

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils"
	logs "code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
)

func TestStoreDevTaskDraftAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis:Set()_ret-1 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			var token string
			var draftPtrValue DevTaskDraft
			draft := &draftPtrValue
			convey.So(func() { _ = StoreDevTaskDraft(ctx, token, draft) }, convey.ShouldPanic)
		})
	})

}

func TestGetDevDraftByDevTokenAutoGen(t *testing.T) {
	// Verify the behavior when there is an error retrieving data from Redis.
	t.Run("testGetDevDraftByDevToken_RedisError", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var v2MockPtrValue logs.ByteDLogger
			v2Mock := &v2MockPtrValue
			mockey.MockValue(&log.V2).To(v2Mock)

			// prepare parameters
			ctx := context.Background()
			var devToken string
			convey.So(func() { _ = GetDevDraftByDevToken(ctx, devToken) }, convey.ShouldPanic)
		})
	})

}

func TestCreateDevTaskDraftAutoGen(t *testing.T) {
	// param2 != nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	// int:GetSpaceId()_ret-1 == 0
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev.DevSmrDraftParams:GetSpaceID()_ret-1 == 0
	// code.byted.org/gopkg/logs/v2.Log:Str()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.Log:With()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getAppSimpleInfoByGroupNameRet1Mock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(getAppSimpleInfoByGroupNameRet1Mock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*dev.DevSmrDraftParams).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var generateDevCreationTokenRet1Mock string
			mockey.Mock(utils.GenerateDevCreationToken).Return(generateDevCreationTokenRet1Mock).Build()

			var getRepoPathRet1Mock string
			mockey.Mock((*dev.DevSmrDraftParams).GetRepoPath, mockey.OptUnsafe).Return(getRepoPathRet1Mock).Build()

			getSpaceIDRet1Mock := int64(0)
			mockey.Mock((*dev.DevSmrDraftParams).GetSpaceID, mockey.OptUnsafe).Return(getSpaceIDRet1Mock).Build()

			var storeDevTaskDraftRet1Mock error
			mockey.Mock(StoreDevTaskDraft).Return(storeDevTaskDraftRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(optimus.GetGroupNameByRepoPath).Return(rMock).Build()

			var getDevTaskCreationUrlRet1Mock string
			mockey.Mock(utils.GetDevTaskCreationUrl).Return(getDevTaskCreationUrlRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var paramsPtrValue dev.DevSmrDraftParams
			params := &paramsPtrValue
			convey.So(func() { _ = CreateDevTaskDraft(ctx, params) }, convey.ShouldPanic)
		})
	})

}

