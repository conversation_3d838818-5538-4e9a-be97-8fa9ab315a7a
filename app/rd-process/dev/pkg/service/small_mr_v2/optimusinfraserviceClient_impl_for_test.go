package small_mr_v2
import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/optimus/infra"
	"code.byted.org/kite/kitex/client/callopt"
)

type optimusinfraserviceClientImplForTestAutoGen struct{}

func (*optimusinfraserviceClientImplForTestAutoGen) GetDev(param1 context.Context, param2 *infra.GetDevRequest, param3 ...callopt.Option)(*infra.GetDevResponse, error) {
	var ret1PtrValue infra.GetDevResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetDevForDep(param1 context.Context, param2 *infra.GetDevForDepRequest, param3 ...callopt.Option)(*infra.GetDevForDepResponse, error) {
	var ret1PtrValue infra.GetDevForDepResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetDevDetail(param1 context.Context, param2 *infra.GetDevDetailRequest, param3 ...callopt.Option)(*infra.GetDevDetailResponse, error) {
	var ret1PtrValue infra.GetDevDetailResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetDevDetailForDep(param1 context.Context, param2 *infra.GetDevDetailForDepRequest, param3 ...callopt.Option)(*infra.GetDevDetailForDepResponse, error) {
	var ret1PtrValue infra.GetDevDetailForDepResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetDependencies(param1 context.Context, param2 *infra.GetDependenciesRequest, param3 ...callopt.Option)(*infra.GetDependenciesResponse, error) {
	var ret1PtrValue infra.GetDependenciesResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetRelations(param1 context.Context, param2 *infra.GetRelationsRequest, param3 ...callopt.Option)(*infra.GetRelationsResponse, error) {
	var ret1PtrValue infra.GetRelationsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetRelationsForDep(param1 context.Context, param2 *infra.GetRelationsForDepRequest, param3 ...callopt.Option)(*infra.GetRelationsForDepResponse, error) {
	var ret1PtrValue infra.GetRelationsForDepResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetRelationsForType(param1 context.Context, param2 *infra.GetRelationsForTypeRequest, param3 ...callopt.Option)(*infra.GetRelationsForTypeResponse, error) {
	var ret1PtrValue infra.GetRelationsForTypeResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) AddDev(param1 context.Context, param2 *infra.AddDevRequest, param3 ...callopt.Option)(*infra.AddDevResponse, error) {
	var ret1PtrValue infra.AddDevResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) AddDevDepRelations(param1 context.Context, param2 *infra.AddDevDepRelationsRequest, param3 ...callopt.Option)(*infra.AddDevDepRelationsResponse, error) {
	var ret1PtrValue infra.AddDevDepRelationsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) RemoveDev(param1 context.Context, param2 *infra.RemoveDevRequest, param3 ...callopt.Option)(*infra.RemoveDevResponse, error) {
	var ret1PtrValue infra.RemoveDevResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) RemoveDevDependencies(param1 context.Context, param2 *infra.RemoveDevDependenciesRequest, param3 ...callopt.Option)(*infra.RemoveDevDependenciesResponse, error) {
	var ret1PtrValue infra.RemoveDevDependenciesResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) RemoveDevRelations(param1 context.Context, param2 *infra.RemoveDevRelationsRequest, param3 ...callopt.Option)(*infra.RemoveDevRelationsResponse, error) {
	var ret1PtrValue infra.RemoveDevRelationsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetDepsForHost(param1 context.Context, param2 *infra.GetDepsForHostRequest, param3 ...callopt.Option)(*infra.GetDepsForHostResponse, error) {
	var ret1PtrValue infra.GetDepsForHostResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetHostForDep(param1 context.Context, param2 *infra.GetHostForDepRequest, param3 ...callopt.Option)(*infra.GetHostForDepResponse, error) {
	var ret1PtrValue infra.GetHostForDepResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetDevHosts(param1 context.Context, param2 *infra.GetDevHostsRequest, param3 ...callopt.Option)(*infra.GetDevHostsResponse, error) {
	var ret1PtrValue infra.GetDevHostsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetDevPublicDependencies(param1 context.Context, param2 *infra.GetDevPublicDependenciesRequest, param3 ...callopt.Option)(*infra.GetDevPublicDependenciesResponse, error) {
	var ret1PtrValue infra.GetDevPublicDependenciesResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetPublicDependencyHost(param1 context.Context, param2 *infra.GetPublicDependencyHostRequest, param3 ...callopt.Option)(*infra.GetPublicDependencyHostResponse, error) {
	var ret1PtrValue infra.GetPublicDependencyHostResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetFirstHost(param1 context.Context, param2 *infra.GetFirstHostRequest, param3 ...callopt.Option)(*infra.GetFirstHostResponse, error) {
	var ret1PtrValue infra.GetFirstHostResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) SyncMR(param1 context.Context, param2 *infra.SyncMRRequest, param3 ...callopt.Option)(*infra.SyncMRResponse, error) {
	var ret1PtrValue infra.SyncMRResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) DataMigration(param1 context.Context, param2 *infra.DataMigrationRequest, param3 ...callopt.Option)(*infra.DataMigrationResponse, error) {
	var ret1PtrValue infra.DataMigrationResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetBitsId(param1 context.Context, param2 *infra.GetBitsIdRequest, param3 ...callopt.Option)(*infra.GetBitsIdResponse, error) {
	var ret1PtrValue infra.GetBitsIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetBitsDevDetail(param1 context.Context, param2 *infra.GetBitsDevDetailRequest, param3 ...callopt.Option)(*infra.GetBitsDevDetailResponse, error) {
	var ret1PtrValue infra.GetBitsDevDetailResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) GetBitsDevDepId(param1 context.Context, param2 *infra.GetBitsDevDepIdRequest, param3 ...callopt.Option)(*infra.GetBitsDevDepIdResponse, error) {
	var ret1PtrValue infra.GetBitsDevDepIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) AddBitsDevDep(param1 context.Context, param2 *infra.AddBitsDevDepRequest, param3 ...callopt.Option)(*infra.AddBitsDevDepResponse, error) {
	var ret1PtrValue infra.AddBitsDevDepResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*optimusinfraserviceClientImplForTestAutoGen) DelBitsDevDep(param1 context.Context, param2 *infra.DelBitsDevDepRequest, param3 ...callopt.Option)(*infra.DelBitsDevDepResponse, error) {
	var ret1PtrValue infra.DelBitsDevDepResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}
