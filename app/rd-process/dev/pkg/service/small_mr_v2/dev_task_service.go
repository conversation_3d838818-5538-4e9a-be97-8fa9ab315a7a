package small_mr_v2

import (
	"context"
	"errors"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	metaService "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/qa_test"
	"code.byted.org/gopkg/facility/set"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"golang.org/x/sync/errgroup"
)

type DevTaskService interface {
	GetStatusInfo(ctx context.Context, devBasicID int64) gresult.R[*dev.DevSmrStatusInfo]
	CreateDevLarkGroup(ctx context.Context, devBasicID int64, operator string) gresult.R[string]
	DismissDevLarkGroup(ctx context.Context, devBasicID int64, operator string) error
}

type DevTaskServiceImpl struct {
}

func NewDevTaskServiceImpl() *DevTaskServiceImpl {
	return &DevTaskServiceImpl{}
}

func (d *DevTaskServiceImpl) GetStatusInfo(ctx context.Context, devBasicID int64) gresult.R[*dev.DevSmrStatusInfo] {
	conflicted := false
	var newCommitsNum int64
	devDetail, err := GetDevDetail(ctx, devBasicID).Get()
	if err != nil {
		return gresult.Err[*dev.DevSmrStatusInfo](err)
	}
	var eg errgroup.Group
	eg.Go(func() error {
		mergeTask, err := devDetail.GetDevMergeTask(ctx).Get()
		if err != nil && err != MergeTaskNotFoundError {
			return err
		}
		if mergeTask == nil {
			return nil
		}
		optimusMrId := mergeTask.GetBasicModel(ctx).BizID
		mrInfo, err := optimus.GetMrMainInfo(ctx, optimusMrId).Get()
		if err != nil {
			return err
		}
		conflicted = mrInfo.Conflicted == 1
		return nil
	})
	eg.Go(func() error {
		mergeTargetBranch, err := devDetail.GetMergeTargetBranch(ctx).Get()
		if err != nil {
			return err
		}
		helper := dev_config.NewSMRConfigHelper(devDetail.Config)
		if mergeTargetBranch == helper.GetTrunkBranch() {
			return nil
		}
		repo, err := gitsvr.GetNextCodeRepositoryByPath(ctx, devDetail.Config.RepoPath).Get()
		if err != nil {
			return err
		}
		commitsRes, err := gitsvr.GetDivergingCommitsNotBatch(ctx, &git_server.CompareItem{
			ProjectId: repo.GetProjectId(),
			From:      helper.GetTrunkBranch(),
			To:        mergeTargetBranch,
		}).Get()
		if err != nil {
			return err
		}
		newCommitsNum = commitsRes.BehindCount
		return nil
	})
	_ = eg.Wait()
	answer := &dev.DevSmrStatusInfo{
		IfConflictedWithTarget:    conflicted,
		NewCommitsNumberInTarget_: newCommitsNum,
	}
	return gresult.OK(answer)
}

func (d *DevTaskServiceImpl) CreateDevLarkGroup(ctx context.Context, devBasicID int64, operator string) gresult.R[string] {
	devDetail, err := GetDevDetail(ctx, devBasicID).Get()
	if err != nil {
		return gresult.Err[string](err)
	}
	// get lark group id
	devBizConfig, err := devDetail.GetBizConfig(ctx).Get()
	if err != nil {
		return gresult.Err[string](err)
	}
	// invite operator into group if group existed
	if len(devBizConfig.LarkGroupID) > 0 {
		if len(operator) == 0 {
			return gresult.OK(devBizConfig.LarkGroupID)
		}
		log.V2.Info().With(ctx).Str("invite user into lark group").KV("chat id", devBizConfig.LarkGroupID).Emit()
		err := metaService.InviteUserIntoLarkGroup(ctx, devBizConfig.LarkGroupID, operator)
		if err != nil {
			return gresult.Err[string](err)
		} else {
			return gresult.OK(devBizConfig.LarkGroupID)
		}
	}
	// get dev group members
	usersSet := set.NewStringSet(devDetail.BasicInfo.Author)
	openedChanges, err := GetSmrChangesByFilterOptions(ctx, &data.ContributionCodeChangesFilterOptions{
		InDevBasicIDs: []int64{devBasicID},
		Status:        gptr.Of(dev.ContributionCodeChangeStatus_opened.String()),
	}).Get()
	if err != nil {
		return gresult.Err[string](err)
	}
	// get reviewers
	if err := BatchGetChangeReviewInfo(ctx, openedChanges); err != nil {
		return gresult.Err[string](err)
	}
	for _, item := range openedChanges {
		usersSet.AddAll(gslice.Map(item.ReviewInfo.ReviewersInfo, func(s *dev.ReviewerInfo) string {
			return s.Username
		})...)
	}
	// get qa testers
	qaTesters, _ := devDetail.GetQaTesters(ctx).Get()
	if len(qaTesters) > 0 {
		usersSet.AddAll(qa_test.AdaptTestersToUsernames(qaTesters)...)
	}
	// create lark group with users
	if len(operator) > 0 {
		usersSet.Add(operator)
	}
	chatID, err := devDetail.CreateLarkGroupWithUsers(ctx, usersSet.ToList()).Get()
	if err != nil {
		return gresult.Err[string](err)
	}
	return gresult.OK(chatID)
}

func (d *DevTaskServiceImpl) DismissDevLarkGroup(ctx context.Context, devBasicID int64, operator string) error {
	smrConfig, err := data.OptimusDB.Slave.UseCache().GetDevSmrConfigByDevBasicID(ctx, devBasicID).Get()
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return err
	}
	devBizConfig, err := biz_config.NewDevBizConfigHandler(smrConfig.BizConfig).GetConfig(ctx).Get()
	if err != nil {
		return err
	}
	if len(devBizConfig.LarkGroupID) == 0 {
		return nil
	}
	// remove all users
	if err := metaService.DismissLarkGroup(ctx, devBizConfig.LarkGroupID); err != nil {
		return err
	}
	return nil
}

func (d *DevTaskServiceImpl) InviteUserIntoDevLarkGroup(ctx context.Context, devBasicID int64, username string, operator string, memberType *string) error {
	smrConfig, err := data.OptimusDB.Slave.UseCache().GetDevSmrConfigByDevBasicID(ctx, devBasicID).Get()
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return err
	}
	devBizConfig, err := biz_config.NewDevBizConfigHandler(smrConfig.BizConfig).GetConfig(ctx).Get()
	if err != nil {
		return err
	}
	if len(devBizConfig.LarkGroupID) == 0 {
		return errors.New("lark group not existed")
	}
	// remove all users
	if err := metaService.InviteUserIntoLarkGroupWithMemberType(ctx, devBizConfig.LarkGroupID, username, memberType); err != nil {
		return err
	}
	return nil
}
