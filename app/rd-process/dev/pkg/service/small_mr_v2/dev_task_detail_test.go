package small_mr_v2

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/feature_gate"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/messagecenter"
	metaService "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/deps"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_task_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/mq/producer"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/lang/gg/gresult"
)

func TestCreateLarkGroupWithUsers(t *testing.T) {
	PatchConvey("", t, func() {
		Mock(redis.TryLock).Return(true).Build()
		Mock(redis.TryUnlock).Return(true).Build()
		Mock((*data.Repo).GetDevBasicInfoByID).Return(&model.DevBasicInfo{Id: 111, Title: "dev task 111"}, nil).Build()
		Mock(metaService.CreateLarkGroup).Return(gresult.OK(
			"chatidxxx")).Build()
		Mock((*data.Repo).UpdateDevSmrConfigByDevBasicID).Return(nil).Build()
		devDetail := &DevDetail{
			DevTask: &dev_task_entity.DevTask{
				BasicInfo: &model.DevBasicInfo{Id: 111, Title: "dev task 111"},
				Config:    &model.DevSmrConfig{},
			},
		}
		Mock(metaService.InviteUsersIntoLarkGroup).Return(nil).Build()
		Mock((*events.ProducerWrapper).SendDevTaskEvent).Return(gresult.OK("")).Build()
		Mock(metaService.CheckIfUserResigned).Return(gresult.OK(false)).Build()
		users := []string{"k1", "k2"}
		resp, _ := devDetail.CreateLarkGroupWithUsers(context.TODO(), users).Get()
		t.Logf(fmt.Sprintf("resp:%v", resp))
	})
}

func TestDevDetail_GetDevMergeTaskInfoAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*DevDetail).GetBizConfig, mockey.OptUnsafe).Return(rMock).Build()

			var formDevTaskSmrOptimusMergeTaskUrlRet1Mock string
			mockey.Mock(functools.FormDevTaskSmrOptimusMergeTaskUrl, mockey.OptUnsafe).Return(formDevTaskSmrOptimusMergeTaskUrlRet1Mock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.GetDevMergeTaskInfo(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestDevDetail_GetDevTaskStageDevelopmentAutoGen(t *testing.T) {
	// Verify the functionality of GetDevTaskStageDevelopment method under default condition.
	t.Run("testDevDetail_GetDevTaskStageDevelopment", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.GetDevTaskStageDevelopment() }, convey.ShouldPanic)
		})
	})

}

func TestDevDetail_UnSetReadyForLandingAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.DevDetail:IsNotOpened()_ret-1 == true
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var updateDevSmrConfigByDevBasicIDIncludeZeroRet1Mock error
			mockey.Mock((*data.Repo).UpdateDevSmrConfigByDevBasicIDIncludeZero).Return(updateDevSmrConfigByDevBasicIDIncludeZeroRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var rMock gresult.R[int]
			mockey.Mock((*DevDetail).GetBizConfig, mockey.OptUnsafe).Return(rMock).Build()

			isNotOpenedRet1Mock := true
			mockey.Mock((*DevDetail).IsNotOpened, mockey.OptUnsafe).Return(isNotOpenedRet1Mock).Build()

			var getDevMergeTaskRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).GetDevMergeTask).Return(getDevMergeTaskRet1Mock).Build()

			var addUnsetReadyForLandingTimelineRet1Mock error
			mockey.Mock(deps.AddUnsetReadyForLandingTimeline).Return(addUnsetReadyForLandingTimelineRet1Mock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			ctx := context.Background()
			var operator string

			// run target function and assert
			got1 := receiver.UnSetReadyForLanding(ctx, operator)
			convey.So(got1 == nil, convey.ShouldBeFalse)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.DevDetail:IsNotOpened()_ret-1 != true
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var addUnsetReadyForLandingTimelineRet1Mock error
			mockey.Mock(deps.AddUnsetReadyForLandingTimeline).Return(addUnsetReadyForLandingTimelineRet1Mock).Build()

			var updateDevSmrConfigByDevBasicIDIncludeZeroRet1Mock error
			mockey.Mock((*data.Repo).UpdateDevSmrConfigByDevBasicIDIncludeZero).Return(updateDevSmrConfigByDevBasicIDIncludeZeroRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var rMock gresult.R[int]
			mockey.Mock((*DevDetail).GetBizConfig, mockey.OptUnsafe).Return(rMock).Build()

			isNotOpenedRet1Mock := false
			mockey.Mock((*DevDetail).IsNotOpened, mockey.OptUnsafe).Return(isNotOpenedRet1Mock).Build()

			var getDevMergeTaskRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).GetDevMergeTask).Return(getDevMergeTaskRet1Mock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			ctx := context.Background()
			var operator string
			convey.So(func() { _ = receiver.UnSetReadyForLanding(ctx, operator) }, convey.ShouldPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

}

func TestDevDetail_UpdateSelfBizConfigAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config:NewDevBizConfigHandler()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var devBizConfigHandlerMockPtrValue biz_config.DevBizConfigHandler
			devBizConfigHandlerMock := &devBizConfigHandlerMockPtrValue
			var updateSelfRet2Mock error
			mockey.Mock((*biz_config.DevBizConfigHandler).UpdateSelf, mockey.OptUnsafe).Return(devBizConfigHandlerMock, updateSelfRet2Mock).Build()

			var newDevBizConfigHandlerRet1MockPtrValue biz_config.DevBizConfigHandler
			newDevBizConfigHandlerRet1Mock := &newDevBizConfigHandlerRet1MockPtrValue
			mockey.Mock(biz_config.NewDevBizConfigHandler).Return(newDevBizConfigHandlerRet1Mock).Build()

			var configStrRet1Mock string
			mockey.Mock((*biz_config.BizConfigHandler).ConfigStr, mockey.OptUnsafe).Return(configStrRet1Mock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			ctx := context.Background()
			var optionsPtrValue biz_config.DevBizConfigUpdateOptions
			options := &optionsPtrValue
			convey.So(func() { _, _ = receiver.UpdateSelfBizConfig(ctx, options) }, convey.ShouldPanic)
		})
	})

}

func TestDevDetail_GetStageByStageTypeAutoGen(t *testing.T) {
	// Verify the function when param1 equals 1.
	t.Run("testDevDetail_GetStageByStageType_Param1Equals1", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stageGatekeeperMockPtrValue StageGatekeeper
			stageGatekeeperMock := &stageGatekeeperMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageGatekeeper, mockey.OptUnsafe).Return(stageGatekeeperMock).Build()

			var stageDevelopmentMockPtrValue StageDevelopment
			stageDevelopmentMock := &stageDevelopmentMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageDevelopment, mockey.OptUnsafe).Return(stageDevelopmentMock).Build()

			var stageCompletionMockPtrValue StageCompletion
			stageCompletionMock := &stageCompletionMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageCompletion, mockey.OptUnsafe).Return(stageCompletionMock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			stageTypeAlias := int64(1)
			stageType := dev.WorkflowStageType(stageTypeAlias)
			convey.So(func() { _ = receiver.GetStageByStageType(stageType) }, convey.ShouldNotPanic)
		})
	})

	// Verify the function when param1 equals 0.
	t.Run("testDevDetail_GetStageByStageType_Param1Equals0", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var stageGatekeeperMockPtrValue StageGatekeeper
			stageGatekeeperMock := &stageGatekeeperMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageGatekeeper, mockey.OptUnsafe).Return(stageGatekeeperMock).Build()

			var stageDevelopmentMockPtrValue StageDevelopment
			stageDevelopmentMock := &stageDevelopmentMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageDevelopment, mockey.OptUnsafe).Return(stageDevelopmentMock).Build()

			var stageCompletionMockPtrValue StageCompletion
			stageCompletionMock := &stageCompletionMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageCompletion, mockey.OptUnsafe).Return(stageCompletionMock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			stageTypeAlias := int64(0)
			stageType := dev.WorkflowStageType(stageTypeAlias)
			convey.So(func() { _ = receiver.GetStageByStageType(stageType) }, convey.ShouldNotPanic)
		})
	})

	// Verify the function when param1 equals 4.
	t.Run("testDevDetail_GetStageByStageType_Param1Equals4", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var stageGatekeeperMockPtrValue StageGatekeeper
			stageGatekeeperMock := &stageGatekeeperMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageGatekeeper, mockey.OptUnsafe).Return(stageGatekeeperMock).Build()

			var stageDevelopmentMockPtrValue StageDevelopment
			stageDevelopmentMock := &stageDevelopmentMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageDevelopment, mockey.OptUnsafe).Return(stageDevelopmentMock).Build()

			var stageCompletionMockPtrValue StageCompletion
			stageCompletionMock := &stageCompletionMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageCompletion, mockey.OptUnsafe).Return(stageCompletionMock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			stageTypeAlias := int64(4)
			stageType := dev.WorkflowStageType(stageTypeAlias)
			convey.So(func() { _ = receiver.GetStageByStageType(stageType) }, convey.ShouldNotPanic)
		})
	})

}

func TestDevDetail_GetMergeTargetBranchAutoGen(t *testing.T) {
	// Verify the panic situation when getting merge target branch.
	t.Run("testDevDetail_GetMergeTargetBranch_Panic", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*DevDetail).GetBizConfig, mockey.OptUnsafe).Return(rMock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.GetMergeTargetBranch(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestDevDetail_SetReadyForLandingAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.DevDetail:IsNotOpened()_ret-1 == true
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getBizConfigRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).GetBizConfig, mockey.OptUnsafe).Return(getBizConfigRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*DevDetail).GetDevMergeTask).Return(rMock).Build()

			var updateAndSaveBizConfigRet1Mock error
			mockey.Mock((*DevDetail).UpdateAndSaveBizConfig, mockey.OptUnsafe).Return(updateAndSaveBizConfigRet1Mock).Build()

			var addSetReadyForLandingTimelineRet1Mock error
			mockey.Mock(deps.AddSetReadyForLandingTimeline).Return(addSetReadyForLandingTimelineRet1Mock).Build()

			var transToFinishedRet1Mock error
			mockey.Mock((*DevDetail).TransToFinished).Return(transToFinishedRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var runRet1Mock error
			mockey.Mock((*OptimusMrForDevTaskMerge).Run).Return(runRet1Mock).Build()

			var ifUseWIPOptimusMRRet1Mock bool
			mockey.Mock(feature_gate.IfUseWIPOptimusMR).Return(ifUseWIPOptimusMRRet1Mock).Build()

			var canFinishRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).CanFinish).Return(canFinishRet1Mock).Build()

			var optimusMrForDevTaskMergeMockPtrValue OptimusMrForDevTaskMerge
			optimusMrForDevTaskMergeMock := &optimusMrForDevTaskMergeMockPtrValue
			mockey.Mock(NewOptimusMrDevMergeTask).Return(optimusMrForDevTaskMergeMock).Build()

			var updateDevSmrConfigByDevBasicIDIncludeZeroRet1Mock error
			mockey.Mock((*data.Repo).UpdateDevSmrConfigByDevBasicIDIncludeZero).Return(updateDevSmrConfigByDevBasicIDIncludeZeroRet1Mock).Build()

			isNotOpenedRet1Mock := true
			mockey.Mock((*DevDetail).IsNotOpened, mockey.OptUnsafe).Return(isNotOpenedRet1Mock).Build()

			var updateRet1Mock error
			mockey.Mock((*OptimusMrForDevTaskMerge).Update).Return(updateRet1Mock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			ctx := context.Background()
			var operator string

			// run target function and assert
			got1 := receiver.SetReadyForLanding(ctx, operator)
			convey.So(got1 == nil, convey.ShouldBeFalse)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.DevDetail:IsNotOpened()_ret-1 != true
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var transToFinishedRet1Mock error
			mockey.Mock((*DevDetail).TransToFinished).Return(transToFinishedRet1Mock).Build()

			var updateRet1Mock error
			mockey.Mock((*OptimusMrForDevTaskMerge).Update).Return(updateRet1Mock).Build()

			var runRet1Mock error
			mockey.Mock((*OptimusMrForDevTaskMerge).Run).Return(runRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var updateAndSaveBizConfigRet1Mock error
			mockey.Mock((*DevDetail).UpdateAndSaveBizConfig, mockey.OptUnsafe).Return(updateAndSaveBizConfigRet1Mock).Build()

			var ifUseWIPOptimusMRRet1Mock bool
			mockey.Mock(feature_gate.IfUseWIPOptimusMR).Return(ifUseWIPOptimusMRRet1Mock).Build()

			var updateDevSmrConfigByDevBasicIDIncludeZeroRet1Mock error
			mockey.Mock((*data.Repo).UpdateDevSmrConfigByDevBasicIDIncludeZero).Return(updateDevSmrConfigByDevBasicIDIncludeZeroRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*DevDetail).GetDevMergeTask).Return(rMock).Build()

			var addSetReadyForLandingTimelineRet1Mock error
			mockey.Mock(deps.AddSetReadyForLandingTimeline).Return(addSetReadyForLandingTimelineRet1Mock).Build()

			isNotOpenedRet1Mock := false
			mockey.Mock((*DevDetail).IsNotOpened, mockey.OptUnsafe).Return(isNotOpenedRet1Mock).Build()

			var canFinishRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).CanFinish).Return(canFinishRet1Mock).Build()

			var optimusMrForDevTaskMergeMockPtrValue OptimusMrForDevTaskMerge
			optimusMrForDevTaskMergeMock := &optimusMrForDevTaskMergeMockPtrValue
			mockey.Mock(NewOptimusMrDevMergeTask).Return(optimusMrForDevTaskMergeMock).Build()

			var getBizConfigRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).GetBizConfig, mockey.OptUnsafe).Return(getBizConfigRet1Mock).Build()

			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			ctx := context.Background()
			var operator string
			convey.So(func() { _ = receiver.SetReadyForLanding(ctx, operator) }, convey.ShouldPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

}

func TestDevDetail_GetDevTaskStageCompletionAutoGen(t *testing.T) {
	// Verify the functionality of GetDevTaskStageCompletion method.
	t.Run("testDevDetail_GetDevTaskStageCompletion", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.GetDevTaskStageCompletion() }, convey.ShouldPanic)
		})
	})

}

func TestDevDetail_TransToFinishedAutoGen(t *testing.T) {
	// Verify the functionality of TransToFinished method in DevDetail.
	t.Run("testDevDetail_TransToFinished", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var clientMockPtrValue events.ProducerWrapper
			clientMock := &clientMockPtrValue
			mockey.MockValue(&producer.Client).To(clientMock)

			var sendDevTaskEventRet1Mock gresult.R[int]
			mockey.Mock((*events.ProducerWrapper).SendDevTaskEvent, mockey.OptUnsafe).Return(sendDevTaskEventRet1Mock).Build()

			var updateDevBasicInfoByIDRet1Mock error
			mockey.Mock((*data.Repo).UpdateDevBasicInfoByID).Return(updateDevBasicInfoByIDRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*biz_config.DevBizConfigHandler).GetConfig).Return(rMock).Build()

			mockey.Mock(utils.SafeGo, mockey.OptUnsafe).Return().Build()

			var pushUpdateDevTaskWorkflowRet1Mock error
			mockey.Mock(deps.PushUpdateDevTaskWorkflow).Return(pushUpdateDevTaskWorkflowRet1Mock).Build()

			var devBizConfigHandlerMockPtrValue biz_config.DevBizConfigHandler
			devBizConfigHandlerMock := &devBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewDevBizConfigHandler).Return(devBizConfigHandlerMock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var sendMsgToTargetRet1Mock error
			mockey.Mock(messagecenter.SendMsgToTarget).Return(sendMsgToTargetRet1Mock).Build()

			var buildDismissLarkGroupCardDataRet1Mock map[string]string
			mockey.Mock(buildDismissLarkGroupCardData).Return(buildDismissLarkGroupCardDataRet1Mock).Build()

			var createDevTimelineEventRet1Mock error
			mockey.Mock(optimus.CreateDevTimelineEvent).Return(createDevTimelineEventRet1Mock).Build()

			// prepare parameters
			var operator string
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.TransToFinished(ctx, operator) }, convey.ShouldPanic)
		})
	})

}

func TestDevDetail_UpdateAndSaveBizConfigAutoGen(t *testing.T) {
	// Verify the failure scenario of UpdateAndSaveBizConfig function.
	t.Run("testDevDetail_UpdateAndSaveBizConfigFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var updateAndSaveRet1Mock error
			mockey.Mock((*biz_config.DevBizConfigHandler).UpdateAndSave).Return(updateAndSaveRet1Mock).Build()

			var devBizConfigHandlerMockPtrValue biz_config.DevBizConfigHandler
			devBizConfigHandlerMock := &devBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewDevBizConfigHandler).Return(devBizConfigHandlerMock).Build()

			// prepare parameters
			ctx := context.Background()
			var optionsPtrValue biz_config.DevBizConfigUpdateOptions
			options := &optionsPtrValue
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.UpdateAndSaveBizConfig(ctx, options) }, convey.ShouldPanic)
		})
	})

}

func TestNewDevDetailAutoGen(t *testing.T) {
	// Verify the error handling when the input is invalid.
	t.Run("testNewDevDetail_InvalidInput", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var basic *model.DevBasicInfo
			var configPtrValue model.DevSmrConfig
			config := &configPtrValue
			convey.So(func() { _ = NewDevDetail(basic, config) }, convey.ShouldNotPanic)
		})
	})

	// Verify the normal operation when the input is valid.
	t.Run("testNewDevDetail_ValidInput", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			var basicPtrValue model.DevBasicInfo
			basic := &basicPtrValue
			var configPtrValue model.DevSmrConfig
			config := &configPtrValue
			convey.So(func() { _ = NewDevDetail(basic, config) }, convey.ShouldNotPanic)
		})
	})

}

func TestDevDetail_GetDevTaskStageGatekeeperAutoGen(t *testing.T) {
	// Verify the behavior of GetDevTaskStageGatekeeper method under default condition.
	t.Run("testDevDetail_GetDevTaskStageGatekeeper", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueDevTask dev_task_entity.DevTask
			receiverPtrValue := DevDetail{DevTask: &receiverPtrValueDevTask}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.GetDevTaskStageGatekeeper() }, convey.ShouldPanic)
		})
	})

}

