package small_mr_v2

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/optimus/infra"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/feature_gate"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/mq/producer"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	utils2 "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/lang/gg/gresult"
)

func TestNewFailedDevSmrCreationDetailAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var msg string

			// run target function and assert
			got1 := NewFailedDevSmrCreationDetail(msg)
			convey.So(int64((*got1).Status), convey.ShouldEqual, 2)
			convey.So((*got1).DevIndex.DevBasicID, convey.ShouldEqual, 0)
			convey.So((*got1).DevIndex.DevID, convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Url, convey.ShouldBeBlank)
			convey.So((*got1).Msg, convey.ShouldBeBlank)
		})
	})

}

func TestCreateAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils:VerifyCreateDevConfig()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(InitCreationEngine).Return(rMock).Build()

			var devSmrCreationDetailMockPtrValue DevSmrCreationDetail
			devSmrCreationDetailMock := &devSmrCreationDetailMockPtrValue
			mockey.Mock(NewSucceededDevSmrCreationDetail).Return(devSmrCreationDetailMock).Build()

			var getDevBasicUrlRet1Mock string
			mockey.Mock(utils.GetDevBasicUrl).Return(getDevBasicUrlRet1Mock).Build()

			verifyCreateDevConfigRet1Mock := fmt.Errorf("error")
			mockey.Mock(utils.VerifyCreateDevConfig).Return(verifyCreateDevConfigRet1Mock).Build()

			var newFailedDevSmrCreationDetailRet1MockPtrValue DevSmrCreationDetail
			newFailedDevSmrCreationDetailRet1Mock := &newFailedDevSmrCreationDetailRet1MockPtrValue
			mockey.Mock(NewFailedDevSmrCreationDetail).Return(newFailedDevSmrCreationDetailRet1Mock).Build()

			// prepare parameters
			var spaceID int64
			var token string
			ctx := context.Background()
			var configPtrValue dev.CreateDevConfig
			config := &configPtrValue
			var smrConfigPtrValue dev.DevSmrCreationConfig
			smrConfig := &smrConfigPtrValue
			var username string
			var groupName string

			// run target function and assert
			got1 := Create(ctx, config, smrConfig, username, groupName, spaceID, token)
			convey.So((*got1).DevIndex.DevBasicID, convey.ShouldEqual, 0)
			convey.So((*got1).DevIndex.DevID, convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Url, convey.ShouldBeBlank)
			convey.So((*got1).Msg, convey.ShouldBeBlank)
			convey.So(int64((*got1).Status), convey.ShouldEqual, 0)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils:VerifyCreateDevConfig()_ret-1 == nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	// int:DoCreation()_ret-1 == nil
	// int:AfterCreationProcess()_ret-1 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(InitCreationEngine).Return(rMock).Build()

			var devSmrCreationDetailMockPtrValue DevSmrCreationDetail
			devSmrCreationDetailMock := &devSmrCreationDetailMockPtrValue
			mockey.Mock(NewSucceededDevSmrCreationDetail).Return(devSmrCreationDetailMock).Build()

			var getDevBasicUrlRet1Mock string
			mockey.Mock(utils.GetDevBasicUrl).Return(getDevBasicUrlRet1Mock).Build()

			var verifyCreateDevConfigRet1Mock error
			mockey.Mock(utils.VerifyCreateDevConfig).Return(verifyCreateDevConfigRet1Mock).Build()

			var newFailedDevSmrCreationDetailRet1MockPtrValue DevSmrCreationDetail
			newFailedDevSmrCreationDetailRet1Mock := &newFailedDevSmrCreationDetailRet1MockPtrValue
			mockey.Mock(NewFailedDevSmrCreationDetail).Return(newFailedDevSmrCreationDetailRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var configPtrValue dev.CreateDevConfig
			config := &configPtrValue
			var smrConfigPtrValue dev.DevSmrCreationConfig
			smrConfig := &smrConfigPtrValue
			var username string
			var groupName string
			var spaceID int64
			var token string
			convey.So(func() { _ = Create(ctx, config, smrConfig, username, groupName, spaceID, token) }, convey.ShouldPanic)
		})
	})

}

func TestNewSucceededDevSmrCreationDetailAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var devID int64
			var url string
			var devBasicID int64

			// run target function and assert
			got1 := NewSucceededDevSmrCreationDetail(url, devBasicID, devID)
			convey.So((*got1).DevIndex.DevBasicID, convey.ShouldEqual, 0)
			convey.So((*got1).DevIndex.DevID, convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Url, convey.ShouldBeBlank)
			convey.So((*got1).Msg, convey.ShouldEqual, "OK")
			convey.So(int64((*got1).Status), convey.ShouldEqual, 1)
		})
	})

}

func TestInitCreationEngineAutoGen(t *testing.T) {
	// Verify the behavior when the group name is empty.
	t.Run("testInitCreationEngine_GroupNameEmpty", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var initDevSmrBizConfigRet1Mock gresult.R[int]
			mockey.Mock((*DevTaskCreationEngine).InitDevSmrBizConfig).Return(initDevSmrBizConfigRet1Mock).Build()

			var getTitleRet1Mock string
			mockey.Mock((*dev.CreateDevConfig).GetTitle, mockey.OptUnsafe).Return(getTitleRet1Mock).Build()

			var processModeMock dev.ProcessMode
			mockey.Mock((*dev.DevSmrCreationConfig).GetProcessMode, mockey.OptUnsafe).Return(processModeMock).Build()

			var getRepoPathRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetRepoPath, mockey.OptUnsafe).Return(getRepoPathRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(config.GetProjectSmrConfigByRepoPath).Return(rMock).Build()

			var getGroupNameByRepoPathRet1Mock gresult.R[int]
			mockey.Mock(optimus.GetGroupNameByRepoPath).Return(getGroupNameByRepoPathRet1Mock).Build()

			var getAppSimpleInfoByGroupNameRet1Mock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(getAppSimpleInfoByGroupNameRet1Mock).Build()

			var getGroupSmrConfigRet1Mock gresult.R[int]
			mockey.Mock(config.GetGroupSmrConfig).Return(getGroupSmrConfigRet1Mock).Build()

			var devOperationLevelMock dev.DevOperationLevel
			mockey.Mock((*dev.DevSmrCreationConfig).GetDevOperationLevel, mockey.OptUnsafe).Return(devOperationLevelMock).Build()

			var stringRet1Mock string
			mockey.Mock(dev.ProcessMode.String).Return(stringRet1Mock).Build()

			var getTargetBranchRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			var getTrunkBranchRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetTrunkBranch, mockey.OptUnsafe).Return(getTrunkBranchRet1Mock).Build()

			var getDescriptionRet1Mock string
			mockey.Mock((*dev.CreateDevConfig).GetDescription, mockey.OptUnsafe).Return(getDescriptionRet1Mock).Build()

			var optimusInfraClientMockValueImpl optimusinfraserviceClientImplForTestAutoGen
			optimusInfraClientMockValue := &optimusInfraClientMockValueImpl
			optimusInfraClientMock := optimusInfraClientMockValue
			mockey.MockValue(&rpc.OptimusInfraClient).To(optimusInfraClientMock)

			var getIdRet1Mock int64
			mockey.Mock((*infra.Dev).GetId, mockey.OptUnsafe).Return(getIdRet1Mock).Build()

			var ret6T1 string
			mockey.Mock(dev.DevSmrType.String).Return(ret6T1).Build()

			var addDevResponseMockPtrValue infra.AddDevResponse
			addDevResponseMock := &addDevResponseMockPtrValue
			var addDevRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusInfraClient, "AddDev"), mockey.OptUnsafe).Return(addDevResponseMock, addDevRet2Mock).Build()

			// prepare parameters
			var smrConfigPtrValue dev.DevSmrCreationConfig
			smrConfig := &smrConfigPtrValue
			var username string
			groupName := ""
			var spaceID int64
			var token string
			ctx := context.Background()
			var createDevConfigPtrValue dev.CreateDevConfig
			createDevConfig := &createDevConfigPtrValue
			convey.So(func() { _ = InitCreationEngine(ctx, createDevConfig, smrConfig, username, groupName, spaceID, token) }, convey.ShouldPanic)
		})
	})

}

func TestDevTaskCreationEngine_DoCreationAutoGen(t *testing.T) {
	// Verify the successful execution of the DoCreation method in the DevTaskCreationEngine.
	t.Run("testDevTaskCreationEngine_DoCreationSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var runRet1Mock error
			mockey.Mock((*OptimusMrForDevTaskMerge).Run).Return(runRet1Mock).Build()

			var getMergeTaskBranchIncludedAllDiffRet1Mock string
			mockey.Mock(GetMergeTaskBranchIncludedAllDiff, mockey.OptUnsafe).Return(getMergeTaskBranchIncludedAllDiffRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var optimusMrForDevTaskMergeMockPtrValue OptimusMrForDevTaskMerge
			optimusMrForDevTaskMergeMock := &optimusMrForDevTaskMergeMockPtrValue
			mockey.Mock(NewOptimusMrDevMergeTask).Return(optimusMrForDevTaskMergeMock).Build()

			var ifUseWIPOptimusMRRet1Mock bool
			mockey.Mock(feature_gate.IfUseWIPOptimusMR).Return(ifUseWIPOptimusMRRet1Mock).Build()

			var devBizConfigHandlerMockPtrValue biz_config.DevBizConfigHandler
			devBizConfigHandlerMock := &devBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewDevBizConfigHandler).Return(devBizConfigHandlerMock).Build()

			var configStrRet1Mock string
			mockey.Mock((*biz_config.BizConfigHandler).ConfigStr, mockey.OptUnsafe).Return(configStrRet1Mock).Build()

			var updateSelfRet1MockPtrValue biz_config.DevBizConfigHandler
			updateSelfRet1Mock := &updateSelfRet1MockPtrValue
			var updateSelfRet2Mock error
			mockey.Mock((*biz_config.DevBizConfigHandler).UpdateSelf, mockey.OptUnsafe).Return(updateSelfRet1Mock, updateSelfRet2Mock).Build()

			var transactionRet1Mock error
			mockey.Mock((*data.Repo).Transaction, mockey.OptUnsafe).Return(transactionRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue DevTaskCreationEngine
			receiver := &receiverPtrValue
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.DoCreation(ctx)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestDevTaskCreationEngine_AfterCreationProcessAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus:CreateDevTimelineEvent()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			var storeDevTaskDraftRet1Mock error
			mockey.Mock(StoreDevTaskDraft).Return(storeDevTaskDraftRet1Mock).Build()

			createDevTimelineEventRet1Mock := fmt.Errorf("error")
			mockey.Mock(optimus.CreateDevTimelineEvent).Return(createDevTimelineEventRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*events.ProducerWrapper).SendDevTaskEvent, mockey.OptUnsafe).Return(rMock).Build()

			mockey.Mock(utils2.SafeGo, mockey.OptUnsafe).Return().Build()

			var clientMockPtrValue events.ProducerWrapper
			clientMock := &clientMockPtrValue
			mockey.MockValue(&producer.Client).To(clientMock)

			// prepare parameters
			var receiverPtrValue DevTaskCreationEngine
			receiver := &receiverPtrValue
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.AfterCreationProcess(ctx)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestDevTaskCreationEngine_InitDevSmrBizConfigAutoGen(t *testing.T) {
	// receiver.CreationSmrConfig != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var smrMultiModeUserWhiteListMockPtrValue tcc.SmrMultiModeUserWhiteList
			smrMultiModeUserWhiteListMock := &smrMultiModeUserWhiteListMockPtrValue
			var getSmrDifferentModeUserWhiteListRet2Mock error
			mockey.Mock(tcc.GetSmrDifferentModeUserWhiteList).Return(smrMultiModeUserWhiteListMock, getSmrDifferentModeUserWhiteListRet2Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var getFeatureTrunkReviewEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetFeatureTrunkReviewEnabled, mockey.OptUnsafe).Return(getFeatureTrunkReviewEnabledRet1Mock).Build()

			var getChangeAutoMergeEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetChangeAutoMergeEnabled, mockey.OptUnsafe).Return(getChangeAutoMergeEnabledRet1Mock).Build()

			var getRemoveSourceBranchRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetRemoveSourceBranch, mockey.OptUnsafe).Return(getRemoveSourceBranchRet1Mock).Build()

			var getCustomPipelineEnvRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetCustomPipelineEnv, mockey.OptUnsafe).Return(getCustomPipelineEnvRet1Mock).Build()

			var getTargetBranchRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			// prepare parameters
			var processMode string
			var receiverPtrValue DevTaskCreationEngine
			receiver := &receiverPtrValue
			var receiverCreationSmrConfigPtrValue dev.DevSmrCreationConfig
			receiver.CreationSmrConfig = &receiverCreationSmrConfigPtrValue
			ctx := context.Background()
			var projectSmrConfigPtrValue config.ProjectSmrConfig
			projectSmrConfig := &projectSmrConfigPtrValue
			var groupSmrConfigPtrValue config.GroupSmrConfig
			groupSmrConfig := &groupSmrConfigPtrValue
			convey.So(func() { _ = receiver.InitDevSmrBizConfig(ctx, projectSmrConfig, groupSmrConfig, processMode) }, convey.ShouldNotPanic)
		})
	})

	// receiver.CreationSmrConfig != nil
	// receiver.CreationSmrConfig.CustomPipelineEnv != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var smrMultiModeUserWhiteListMockPtrValue tcc.SmrMultiModeUserWhiteList
			smrMultiModeUserWhiteListMock := &smrMultiModeUserWhiteListMockPtrValue
			var getSmrDifferentModeUserWhiteListRet2Mock error
			mockey.Mock(tcc.GetSmrDifferentModeUserWhiteList).Return(smrMultiModeUserWhiteListMock, getSmrDifferentModeUserWhiteListRet2Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var getFeatureTrunkReviewEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetFeatureTrunkReviewEnabled, mockey.OptUnsafe).Return(getFeatureTrunkReviewEnabledRet1Mock).Build()

			var getChangeAutoMergeEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetChangeAutoMergeEnabled, mockey.OptUnsafe).Return(getChangeAutoMergeEnabledRet1Mock).Build()

			var getRemoveSourceBranchRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetRemoveSourceBranch, mockey.OptUnsafe).Return(getRemoveSourceBranchRet1Mock).Build()

			var getCustomPipelineEnvRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetCustomPipelineEnv, mockey.OptUnsafe).Return(getCustomPipelineEnvRet1Mock).Build()

			var getTargetBranchRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var projectSmrConfigPtrValue config.ProjectSmrConfig
			projectSmrConfig := &projectSmrConfigPtrValue
			var groupSmrConfigPtrValue config.GroupSmrConfig
			groupSmrConfig := &groupSmrConfigPtrValue
			var processMode string
			var receiverPtrValue DevTaskCreationEngine
			receiver := &receiverPtrValue
			var receiverCreationSmrConfigPtrValue dev.DevSmrCreationConfig
			receiver.CreationSmrConfig = &receiverCreationSmrConfigPtrValue
			var receiverCreationSmrConfigCustomPipelineEnvPtrValue string
			receiver.CreationSmrConfig.CustomPipelineEnv = &receiverCreationSmrConfigCustomPipelineEnvPtrValue
			convey.So(func() { _ = receiver.InitDevSmrBizConfig(ctx, projectSmrConfig, groupSmrConfig, processMode) }, convey.ShouldNotPanic)
		})
	})

	// receiver.CreationSmrConfig != nil
	// param3 != nil
	// github.com/bytedance/sonic:MarshalString()_ret-2 != nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var getRemoveSourceBranchRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetRemoveSourceBranch, mockey.OptUnsafe).Return(getRemoveSourceBranchRet1Mock).Build()

			var getCustomPipelineEnvRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetCustomPipelineEnv, mockey.OptUnsafe).Return(getCustomPipelineEnvRet1Mock).Build()

			var getTargetBranchRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			var smrMultiModeUserWhiteListMockPtrValue tcc.SmrMultiModeUserWhiteList
			smrMultiModeUserWhiteListMock := &smrMultiModeUserWhiteListMockPtrValue
			var getSmrDifferentModeUserWhiteListRet2Mock error
			mockey.Mock(tcc.GetSmrDifferentModeUserWhiteList).Return(smrMultiModeUserWhiteListMock, getSmrDifferentModeUserWhiteListRet2Mock).Build()

			var marshalStringRet1Mock string
			marshalStringRet2Mock := fmt.Errorf("error")
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var getFeatureTrunkReviewEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetFeatureTrunkReviewEnabled, mockey.OptUnsafe).Return(getFeatureTrunkReviewEnabledRet1Mock).Build()

			var getChangeAutoMergeEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetChangeAutoMergeEnabled, mockey.OptUnsafe).Return(getChangeAutoMergeEnabledRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var projectSmrConfigPtrValue config.ProjectSmrConfig
			projectSmrConfig := &projectSmrConfigPtrValue
			var groupSmrConfigPtrValue config.GroupSmrConfig
			groupSmrConfig := &groupSmrConfigPtrValue
			var processMode string
			var receiverPtrValue DevTaskCreationEngine
			receiver := &receiverPtrValue
			var receiverCreationSmrConfigPtrValue dev.DevSmrCreationConfig
			receiver.CreationSmrConfig = &receiverCreationSmrConfigPtrValue
			convey.So(func() { _ = receiver.InitDevSmrBizConfig(ctx, projectSmrConfig, groupSmrConfig, processMode) }, convey.ShouldNotPanic)
		})
	})

	// receiver.CreationSmrConfig != nil
	// param3 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev.DevSmrCreationConfig:GetFeatureTrunkReviewEnabled()_ret-1 == true
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var getTargetBranchRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			var smrMultiModeUserWhiteListMockPtrValue tcc.SmrMultiModeUserWhiteList
			smrMultiModeUserWhiteListMock := &smrMultiModeUserWhiteListMockPtrValue
			var getSmrDifferentModeUserWhiteListRet2Mock error
			mockey.Mock(tcc.GetSmrDifferentModeUserWhiteList).Return(smrMultiModeUserWhiteListMock, getSmrDifferentModeUserWhiteListRet2Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			getFeatureTrunkReviewEnabledRet1Mock := true
			mockey.Mock((*dev.DevSmrCreationConfig).GetFeatureTrunkReviewEnabled, mockey.OptUnsafe).Return(getFeatureTrunkReviewEnabledRet1Mock).Build()

			var getChangeAutoMergeEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetChangeAutoMergeEnabled, mockey.OptUnsafe).Return(getChangeAutoMergeEnabledRet1Mock).Build()

			var getRemoveSourceBranchRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetRemoveSourceBranch, mockey.OptUnsafe).Return(getRemoveSourceBranchRet1Mock).Build()

			var getCustomPipelineEnvRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetCustomPipelineEnv, mockey.OptUnsafe).Return(getCustomPipelineEnvRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue DevTaskCreationEngine
			receiver := &receiverPtrValue
			var receiverCreationSmrConfigPtrValue dev.DevSmrCreationConfig
			receiver.CreationSmrConfig = &receiverCreationSmrConfigPtrValue
			ctx := context.Background()
			var projectSmrConfigPtrValue config.ProjectSmrConfig
			projectSmrConfig := &projectSmrConfigPtrValue
			var groupSmrConfigPtrValue config.GroupSmrConfig
			groupSmrConfig := &groupSmrConfigPtrValue
			var processMode string
			convey.So(func() { _ = receiver.InitDevSmrBizConfig(ctx, projectSmrConfig, groupSmrConfig, processMode) }, convey.ShouldNotPanic)
		})
	})

	// receiver.CreationSmrConfig != nil
	// param3 != nil
	// len(param2.MergeTaskMode) > 0
	// param2 != nil
	t.Run("case_4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var getFeatureTrunkReviewEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetFeatureTrunkReviewEnabled, mockey.OptUnsafe).Return(getFeatureTrunkReviewEnabledRet1Mock).Build()

			var getChangeAutoMergeEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetChangeAutoMergeEnabled, mockey.OptUnsafe).Return(getChangeAutoMergeEnabledRet1Mock).Build()

			var getRemoveSourceBranchRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetRemoveSourceBranch, mockey.OptUnsafe).Return(getRemoveSourceBranchRet1Mock).Build()

			var getCustomPipelineEnvRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetCustomPipelineEnv, mockey.OptUnsafe).Return(getCustomPipelineEnvRet1Mock).Build()

			var getTargetBranchRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			var smrMultiModeUserWhiteListMockPtrValue tcc.SmrMultiModeUserWhiteList
			smrMultiModeUserWhiteListMock := &smrMultiModeUserWhiteListMockPtrValue
			var getSmrDifferentModeUserWhiteListRet2Mock error
			mockey.Mock(tcc.GetSmrDifferentModeUserWhiteList).Return(smrMultiModeUserWhiteListMock, getSmrDifferentModeUserWhiteListRet2Mock).Build()

			// prepare parameters
			var projectSmrConfigPtrValue config.ProjectSmrConfig
			projectSmrConfig := &projectSmrConfigPtrValue
			projectSmrConfig.MergeTaskMode = "a"
			var groupSmrConfigPtrValue config.GroupSmrConfig
			groupSmrConfig := &groupSmrConfigPtrValue
			var processMode string
			var receiverPtrValue DevTaskCreationEngine
			receiver := &receiverPtrValue
			var receiverCreationSmrConfigPtrValue dev.DevSmrCreationConfig
			receiver.CreationSmrConfig = &receiverCreationSmrConfigPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.InitDevSmrBizConfig(ctx, projectSmrConfig, groupSmrConfig, processMode) }, convey.ShouldNotPanic)
		})
	})

	// receiver.CreationSmrConfig != nil
	// param3 != nil
	// receiver.CreationSmrConfig.ChangeAutoMergeEnabled != nil
	t.Run("case_5", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// mock function returns or global values
			var unmarshalStringRet1Mock error
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var getFeatureTrunkReviewEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetFeatureTrunkReviewEnabled, mockey.OptUnsafe).Return(getFeatureTrunkReviewEnabledRet1Mock).Build()

			var getChangeAutoMergeEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetChangeAutoMergeEnabled, mockey.OptUnsafe).Return(getChangeAutoMergeEnabledRet1Mock).Build()

			var getRemoveSourceBranchRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetRemoveSourceBranch, mockey.OptUnsafe).Return(getRemoveSourceBranchRet1Mock).Build()

			var getCustomPipelineEnvRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetCustomPipelineEnv, mockey.OptUnsafe).Return(getCustomPipelineEnvRet1Mock).Build()

			var getTargetBranchRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			var smrMultiModeUserWhiteListMockPtrValue tcc.SmrMultiModeUserWhiteList
			smrMultiModeUserWhiteListMock := &smrMultiModeUserWhiteListMockPtrValue
			var getSmrDifferentModeUserWhiteListRet2Mock error
			mockey.Mock(tcc.GetSmrDifferentModeUserWhiteList).Return(smrMultiModeUserWhiteListMock, getSmrDifferentModeUserWhiteListRet2Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue DevTaskCreationEngine
			receiver := &receiverPtrValue
			var receiverCreationSmrConfigPtrValue dev.DevSmrCreationConfig
			receiver.CreationSmrConfig = &receiverCreationSmrConfigPtrValue
			var receiverCreationSmrConfigChangeAutoMergeEnabledPtrValue bool
			receiver.CreationSmrConfig.ChangeAutoMergeEnabled = &receiverCreationSmrConfigChangeAutoMergeEnabledPtrValue
			ctx := context.Background()
			var projectSmrConfigPtrValue config.ProjectSmrConfig
			projectSmrConfig := &projectSmrConfigPtrValue
			var groupSmrConfigPtrValue config.GroupSmrConfig
			groupSmrConfig := &groupSmrConfigPtrValue
			var processMode string
			convey.So(func() { _ = receiver.InitDevSmrBizConfig(ctx, projectSmrConfig, groupSmrConfig, processMode) }, convey.ShouldNotPanic)
		})
	})

	// receiver.CreationSmrConfig != nil
	// receiver.CreationSmrConfig.CustomPipelineEnv != nil
	// github.com/bytedance/sonic:UnmarshalString()_ret-1 != nil
	t.Run("case_6", func(t *testing.T) {
		mockey.PatchConvey("case_6", t, func() {
			// mock function returns or global values
			var smrMultiModeUserWhiteListMockPtrValue tcc.SmrMultiModeUserWhiteList
			smrMultiModeUserWhiteListMock := &smrMultiModeUserWhiteListMockPtrValue
			var getSmrDifferentModeUserWhiteListRet2Mock error
			mockey.Mock(tcc.GetSmrDifferentModeUserWhiteList).Return(smrMultiModeUserWhiteListMock, getSmrDifferentModeUserWhiteListRet2Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			unmarshalStringRet1Mock := fmt.Errorf("error")
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			var getFeatureTrunkReviewEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetFeatureTrunkReviewEnabled, mockey.OptUnsafe).Return(getFeatureTrunkReviewEnabledRet1Mock).Build()

			var getChangeAutoMergeEnabledRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetChangeAutoMergeEnabled, mockey.OptUnsafe).Return(getChangeAutoMergeEnabledRet1Mock).Build()

			var getRemoveSourceBranchRet1Mock bool
			mockey.Mock((*dev.DevSmrCreationConfig).GetRemoveSourceBranch, mockey.OptUnsafe).Return(getRemoveSourceBranchRet1Mock).Build()

			var getCustomPipelineEnvRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetCustomPipelineEnv, mockey.OptUnsafe).Return(getCustomPipelineEnvRet1Mock).Build()

			var getTargetBranchRet1Mock string
			mockey.Mock((*dev.DevSmrCreationConfig).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			// prepare parameters
			var groupSmrConfigPtrValue config.GroupSmrConfig
			groupSmrConfig := &groupSmrConfigPtrValue
			var processMode string
			var receiverPtrValue DevTaskCreationEngine
			receiver := &receiverPtrValue
			var receiverCreationSmrConfigPtrValue dev.DevSmrCreationConfig
			receiver.CreationSmrConfig = &receiverCreationSmrConfigPtrValue
			var receiverCreationSmrConfigCustomPipelineEnvPtrValue string
			receiver.CreationSmrConfig.CustomPipelineEnv = &receiverCreationSmrConfigCustomPipelineEnvPtrValue
			ctx := context.Background()
			var projectSmrConfigPtrValue config.ProjectSmrConfig
			projectSmrConfig := &projectSmrConfigPtrValue
			convey.So(func() { _ = receiver.InitDevSmrBizConfig(ctx, projectSmrConfig, groupSmrConfig, processMode) }, convey.ShouldNotPanic)
		})
	})

}

