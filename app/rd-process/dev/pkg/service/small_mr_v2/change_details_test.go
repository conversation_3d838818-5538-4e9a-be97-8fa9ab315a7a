package small_mr_v2

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/lang/gg/gresult"
)

func TestBatchGetChangeReviewInfo(t *testing.T) {
	PatchConvey("change get if can ff", t, func() {
		change := &ChangeDetail{
			CodeChange: &change_entity.CodeChange{
				Contribution: &model.ContributionCodeChange{Id: 11},
				GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
			},
		}
		Mock((*change_entity.CodeChange).GetReviewInfo).Return(gresult.OK(&dev.ReviewInfo{})).Build()
		err := BatchGetChangeReviewInfo(context.TODO(), []*ChangeDetail{change})
		So(err, ShouldBeNil)
	})
}

func TestGetChangesChecksStatusAutoGen(t *testing.T) {
	// len(param2) == 0
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*change_check.ChangeCheckHelper).GetCiCheckRes).Return(rMock).Build()

			var changeCheckHelperMockPtrValue change_check.ChangeCheckHelper
			changeCheckHelperMock := &changeCheckHelperMockPtrValue
			mockey.Mock(change_check.NewChangeCheckHelper, mockey.OptUnsafe).Return(changeCheckHelperMock).Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			// prepare parameters
			ctx := context.Background()
			changes := []*ChangeDetail{}
			var checkResSource consts.CheckResSource
			var useCache bool

			// run target function and assert
			got1 := GetChangesChecksStatus(ctx, changes, checkResSource, useCache)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

	// len(param2) != 0
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*change_check.ChangeCheckHelper).GetCiCheckRes).Return(rMock).Build()

			var changeCheckHelperMockPtrValue change_check.ChangeCheckHelper
			changeCheckHelperMock := &changeCheckHelperMockPtrValue
			mockey.Mock(change_check.NewChangeCheckHelper, mockey.OptUnsafe).Return(changeCheckHelperMock).Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			// prepare parameters
			var useCache bool
			ctx := context.Background()
			var changesItem0PtrValueCodeChange change_entity.CodeChange
			changesItem0PtrValue := ChangeDetail{CodeChange: &changesItem0PtrValueCodeChange}
			changesItem0 := &changesItem0PtrValue
			changes := []*ChangeDetail{changesItem0,}
			var checkResSource consts.CheckResSource

			// run target function and assert
			got1 := GetChangesChecksStatus(ctx, changes, checkResSource, useCache)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestBatchGetChangeReviewInfoAutoGen(t *testing.T) {
	// len(param2) != 0
	// golang.org/x/sync/errgroup.Group:Wait()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var rMock gresult.R[int]
			mockey.Mock((*change_entity.CodeChange).GetReviewInfo).Return(rMock).Build()

			waitRet1Mock := fmt.Errorf("error")
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var changesItem0PtrValueCodeChange change_entity.CodeChange
			changesItem0PtrValue := ChangeDetail{CodeChange: &changesItem0PtrValueCodeChange}
			changesItem0 := &changesItem0PtrValue
			changes := []*ChangeDetail{changesItem0,}

			// run target function and assert
			got1 := BatchGetChangeReviewInfo(ctx, changes)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestGetChangeDetailsByContributionsAutoGen(t *testing.T) {
	// Verify the function behavior with given conditions.
	t.Run("testGetChangeDetailsByContributions", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getCodeChangeIDRet1Mock int64
			mockey.Mock((*git_server.CodeChangeGitlabMergeRequest).GetCodeChangeID, mockey.OptUnsafe).Return(getCodeChangeIDRet1Mock).Build()

			var getTargetBranchRet1Mock string
			mockey.Mock((*git_server.CodeChangeGitlabMergeRequest).GetTargetBranch, mockey.OptUnsafe).Return(getTargetBranchRet1Mock).Build()

			var getSourceBranchRet1Mock string
			mockey.Mock((*git_server.CodeChangeGitlabMergeRequest).GetSourceBranch, mockey.OptUnsafe).Return(getSourceBranchRet1Mock).Build()

			var gitServerClientMockImpl gitserviceClientImplForTestAutoGen
			gitServerClientMock := &gitServerClientMockImpl
			mockey.MockValue(&rpc.GitServerClient).To(gitServerClientMock)

			var batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMockPtrValue git_server.BatchGetCodeChangeGitlabMrByCodeChangeIDsResponse
			batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMock := &batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMockPtrValue
			batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMock.Mrs = []*git_server.CodeChangeGitlabMergeRequest{}
			var batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMockMrs0ItemPtrValue git_server.CodeChangeGitlabMergeRequest
			batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMockMrs0Item := &batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMockMrs0ItemPtrValue
			batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMock.Mrs = append(batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMock.Mrs, batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMockMrs0Item)
			var batchGetCodeChangeGitlabMrByCodeChangeIDsRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.GitServerClient, "BatchGetCodeChangeGitlabMrByCodeChangeIDs"), mockey.OptUnsafe).Return(batchGetCodeChangeGitlabMrByCodeChangeIDsResponseMock, batchGetCodeChangeGitlabMrByCodeChangeIDsRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var contributions []*model.ContributionCodeChange
			convey.So(func() { _ = GetChangeDetailsByContributions(ctx, contributions) }, convey.ShouldNotPanic)
		})
	})

}

func TestInitChangesCanFastForwardAutoGen(t *testing.T) {
	// Verify the function behavior when there are no changes.
	t.Run("testInitChangesCanFastForward_EmptyChanges", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var initCanFastForwardRet1Mock error
			mockey.Mock((*ChangeDetail).InitCanFastForward).Return(initCanFastForwardRet1Mock).Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			// prepare parameters
			var useCache bool
			ctx := context.Background()
			changes := []*ChangeDetail{}
			convey.So(func() { InitChangesCanFastForward(ctx, changes, useCache) }, convey.ShouldNotPanic)
		})
	})

	// Verify the function behavior when there are changes.
	t.Run("testInitChangesCanFastForward_NonEmptyChanges", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var initCanFastForwardRet1Mock error
			mockey.Mock((*ChangeDetail).InitCanFastForward).Return(initCanFastForwardRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var changesItem0PtrValueCodeChange change_entity.CodeChange
			changesItem0PtrValue := ChangeDetail{CodeChange: &changesItem0PtrValueCodeChange}
			changesItem0 := &changesItem0PtrValue
			changes := []*ChangeDetail{changesItem0,}
			var useCache bool
			convey.So(func() { InitChangesCanFastForward(ctx, changes, useCache) }, convey.ShouldNotPanic)
		})
	})

	// Verify the function behavior when a change has CanFastForward set.
	t.Run("testInitChangesCanFastForward_ChangeWithFastForwardSet", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var initCanFastForwardRet1Mock error
			mockey.Mock((*ChangeDetail).InitCanFastForward).Return(initCanFastForwardRet1Mock).Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			// prepare parameters
			ctx := context.Background()
			changes := []*ChangeDetail{}
			var changes0ItemPtrValueCodeChange change_entity.CodeChange
			changes0ItemPtrValue := ChangeDetail{CodeChange: &changes0ItemPtrValueCodeChange}
			changes0Item := &changes0ItemPtrValue
			changes = append(changes, changes0Item)
			var changes0CanFastForwardPtrValue bool
			changes[0].CanFastForward = &changes0CanFastForwardPtrValue
			var useCache bool
			convey.So(func() { InitChangesCanFastForward(ctx, changes, useCache) }, convey.ShouldNotPanic)
		})
	})

}

