package small_mr_v2

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	optimusService "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_task_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/value_object"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/mq/producer"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
)

func TestChangeGetMergeTask(t *testing.T) {
	PatchConvey("get change merge task", t, func() {
		PatchConvey("get change merge task with origin biz config and type engine task", func() {
			Mock(dev_task_entity.GetDevTaskWithOpt).Return(gresult.OK(&dev_task_entity.DevTask{
				BasicInfo: &model.DevBasicInfo{Id: 121},
				Config:    &model.DevSmrConfig{BizConfig: "{\"partial_merge_task_type\":\"engine_task\"}"},
			})).Build()
			Mock(GetDevDetail).Return(gresult.OK(&DevDetail{
				DevTask: &dev_task_entity.DevTask{
					BasicInfo: &model.DevBasicInfo{Id: 121},
					Config:    &model.DevSmrConfig{BizConfig: "{\"partial_merge_task_type\":\"engine_task\"}"},
				},
			})).Build()
			change := NewChangeDetail(&model.ContributionCodeChange{BizConfig: "{\"auto_merge_enabled\":true}"}, &git_server.CodeChangeGitlabMergeRequest{}).Value()
			res, err := change.GetMergeTask(context.Background()).Get()
			So(err, ShouldBeNil)
			So(res.GetBasicModel(context.Background()).BizType, ShouldEqual, dev.MergeTaskType_engine_task)
			t.Logf("res: %v err: %v", res, err)
		})
		PatchConvey("get change merge task with origin biz config and type optimus mr", func() {
			Mock(dev_task_entity.GetDevTaskWithOpt).Return(gresult.OK(&dev_task_entity.DevTask{
				BasicInfo: &model.DevBasicInfo{Id: 121},
				Config:    &model.DevSmrConfig{BizConfig: "{\"partial_merge_task_type\":\"optimus_mr\"}"},
			})).Build()
			Mock(GetDevDetail).Return(gresult.OK(&DevDetail{
				DevTask: &dev_task_entity.DevTask{
					BasicInfo: &model.DevBasicInfo{Id: 121},
					Config:    &model.DevSmrConfig{BizConfig: "{\"partial_merge_task_type\":\"optimus_mr\"}"},
				},
			})).Build()
			Mock(optimusService.GetMrMainInfo).Return(gresult.OK(&optimus.MrMainInfo{})).Build()
			change := NewChangeDetail(&model.ContributionCodeChange{BizConfig: "{\"auto_merge_enabled\":true,\"merge_task_biz_id\":111}"}, &git_server.CodeChangeGitlabMergeRequest{}).Value()
			res, err := change.GetMergeTask(context.Background()).Get()
			So(err, ShouldBeNil)
			So(res.GetBasicModel(context.Background()).BizType, ShouldEqual, dev.MergeTaskType_optimus_mr)
			t.Logf("res: %v err: %v", res, err)
		})
		PatchConvey("get change merge task with new change biz config and type optimus mr", func() {
			Mock(dev_task_entity.GetDevTaskWithOpt).Return(gresult.OK(&dev_task_entity.DevTask{
				BasicInfo: &model.DevBasicInfo{Id: 121},
				Config:    &model.DevSmrConfig{},
			})).Build()
			Mock(optimusService.GetMrMainInfo).Return(gresult.OK(&optimus.MrMainInfo{})).Build()
			change := NewChangeDetail(&model.ContributionCodeChange{BizConfig: "{\"merge_task\":{\"biz_id\":111,\"biz_type\":1,\"contribution_ids\":[112]}}"}, &git_server.CodeChangeGitlabMergeRequest{}).Value()
			res, err := change.GetMergeTask(context.Background()).Get()
			t.Logf("res: %v err: %v", res, err)
			So(err, ShouldBeNil)
			So(res.GetBasicModel(context.Background()).BizType, ShouldEqual, dev.MergeTaskType_optimus_mr)
		})
		PatchConvey("get change merge task with new change biz config and type engine task", func() {
			Mock(dev_task_entity.GetDevTaskWithOpt).Return(gresult.OK(&dev_task_entity.DevTask{
				BasicInfo: &model.DevBasicInfo{Id: 121},
				Config:    &model.DevSmrConfig{},
			})).Build()
			change := NewChangeDetail(&model.ContributionCodeChange{BizConfig: "{\"merge_task\":{\"biz_id\":111,\"biz_type\":4,\"contribution_ids\":[112]}}"}, &git_server.CodeChangeGitlabMergeRequest{}).Value()
			res, err := change.GetMergeTask(context.Background()).Get()
			t.Logf("res: %v err: %v", res, err)
			So(err, ShouldBeNil)
			So(res.GetBasicModel(context.Background()).BizType, ShouldEqual, dev.MergeTaskType_engine_task)
		})
	})
}

func TestChangeInitCanFastForward(t *testing.T) {
	PatchConvey("change get if can ff", t, func() {
		change := &ChangeDetail{
			CodeChange: &change_entity.CodeChange{
				Contribution: &model.ContributionCodeChange{Id: 11},
				GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
			},
		}
		Mock(redis.SetCacheVal[bool]).Return(nil).Build()
		PatchConvey("git server err", func() {
			Mock(gitsvr.CheckIfTwoBranchesAreLinearNoCache).Return(gresult.OK(true)).Build()
			err := change.InitCanFastForward(context.TODO(), false)
			So(err, ShouldBeNil)
			So(change.CanFastForward, ShouldEqual, gptr.Of(true))
		})
	})
}

func TestChangeDetail_SetSpaceIDAutoGen(t *testing.T) {
	// Verify the normal operation of setting the SpaceID in ChangeDetail.
	t.Run("testChangeDetail_SetSpaceID", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			var spaceID int64
			convey.So(func() { receiver.SetSpaceID(spaceID) }, convey.ShouldNotPanic)
		})
	})

}

func TestChangeDetail_GetUpStackChangeDetailsAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetRelatedStackedChanges).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.GetUpStackChangeDetails(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_RemoveSourceBranchAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config:NewChangeBizConfigHandler()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var hasPrefixRet1Mock bool
			mockey.Mock(strings.HasPrefix, mockey.OptUnsafe).Return(hasPrefixRet1Mock).Build()

			var deleteBranchRet1Mock error
			mockey.Mock(gitsvr.DeleteBranch).Return(deleteBranchRet1Mock).Build()

			var getProjectIDRet1Mock int64
			mockey.Mock((*git_server.CodeChangeGitlabMergeRequest).GetProjectID, mockey.OptUnsafe).Return(getProjectIDRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*biz_config.ChangeBizConfigHandler).GetConfig).Return(rMock).Build()

			var changeBizConfigHandlerMockPtrValue biz_config.ChangeBizConfigHandler
			changeBizConfigHandlerMock := &changeBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewChangeBizConfigHandler).Return(changeBizConfigHandlerMock).Build()

			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.RemoveSourceBranch(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_GetDownStackChangeDetailsAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetRelatedStackedChanges).Return(rMock).Build()

			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.GetDownStackChangeDetails(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestGetChangeDetailAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:UseCache()_ret-1 != nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetContributionCodeChangeByID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var getChangeDetailByContributionRet1Mock gresult.R[int]
			mockey.Mock(GetChangeDetailByContribution).Return(getChangeDetailByContributionRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			// prepare parameters
			ctx := context.Background()
			var cid int64
			convey.So(func() { _ = GetChangeDetail(ctx, cid) }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_IsOpenedAutoGen(t *testing.T) {
	// Verify the IsOpened function behavior under default condition.
	t.Run("testChangeDetail_IsOpened", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.IsOpened() }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_GetDownStackedChangesAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetRelatedStackedChanges).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.GetDownStackedChanges(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_CheckIfLastCommitCheckedByPipelinesAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config:NewChangeBizConfigHandler()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var changeBizConfigHandlerMockPtrValue biz_config.ChangeBizConfigHandler
			changeBizConfigHandlerMock := &changeBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewChangeBizConfigHandler).Return(changeBizConfigHandlerMock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*ChangeDetail).CheckIfLastCommitCheckedByPipeline).Return(rMock).Build()

			var getConfigRet1Mock gresult.R[int]
			mockey.Mock((*biz_config.ChangeBizConfigHandler).GetConfig).Return(getConfigRet1Mock).Build()

			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			ctx := context.Background()
			var pipelineNames []string
			var rangeDiffMode bool
			convey.So(func() { _ = receiver.CheckIfLastCommitCheckedByPipelines(ctx, pipelineNames, rangeDiffMode) }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_GetUpStackedChangesAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetRelatedStackedChanges).Return(rMock).Build()

			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.GetUpStackedChanges(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_AfterMergeProcessAutoGen(t *testing.T) {
	// Verify the functionality of AfterMergeProcess method under default condition.
	t.Run("testChangeDetail_AfterMergeProcess", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var updateContributionCodeChangeByIDRet1Mock error
			mockey.Mock((*data.Repo).UpdateContributionCodeChangeByID).Return(updateContributionCodeChangeByIDRet1Mock).Build()

			var createDevTimelineEventRet1Mock error
			mockey.Mock(optimusService.CreateDevTimelineEvent).Return(createDevTimelineEventRet1Mock).Build()

			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*events.ProducerWrapper).SendDevChangeEvent, mockey.OptUnsafe).Return(rMock).Build()

			var removeSourceBranchRet1Mock error
			mockey.Mock((*ChangeDetail).RemoveSourceBranch).Return(removeSourceBranchRet1Mock).Build()

			var clientMockPtrValue events.ProducerWrapper
			clientMock := &clientMockPtrValue
			mockey.MockValue(&producer.Client).To(clientMock)

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			ctx := context.Background()
			var username string
			var removeSourceBranch bool
			convey.So(func() { _ = receiver.AfterMergeProcess(ctx, username, removeSourceBranch) }, convey.ShouldPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

}

func TestChangeDetail_GetMergeTaskByBizConfigAutoGen(t *testing.T) {
	// param2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetMergeTaskEngineTaskByBizConfig).Return(rMock).Build()

			var getMergeTaskOptimusMRByBizConfigRet1Mock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetMergeTaskOptimusMRByBizConfig).Return(getMergeTaskOptimusMRByBizConfigRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var taskPtrValue value_object.ChangeMergeTask
			task := &taskPtrValue
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.GetMergeTaskByBizConfig(ctx, task) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetChangeDetailByContributionAutoGen(t *testing.T) {
	// Verify the behavior when the contribution input is null.
	t.Run("testGetChangeDetailByContribution_InvalidInput", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(gitsvr.GetCodeChangeDetailGitlabMr).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var contribution *model.ContributionCodeChange
			convey.So(func() { _ = GetChangeDetailByContribution(ctx, contribution) }, convey.ShouldNotPanic)
		})
	})

	// Verify the behavior when the contribution input is not null.
	t.Run("testGetChangeDetailByContribution_ValidInput", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(gitsvr.GetCodeChangeDetailGitlabMr).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var contributionPtrValue model.ContributionCodeChange
			contribution := &contributionPtrValue
			convey.So(func() { _ = GetChangeDetailByContribution(ctx, contribution) }, convey.ShouldPanic)
		})
	})

}

func TestNewChangeDetailAutoGen(t *testing.T) {
	// Verify the behavior when the input model is invalid.
	t.Run("testNewChangeDetail_InvalidInput", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var ctb *model.ContributionCodeChange
			var mrPtrValue git_server.CodeChangeGitlabMergeRequest
			mr := &mrPtrValue
			convey.So(func() { _ = NewChangeDetail(ctb, mr) }, convey.ShouldNotPanic)
		})
	})

}

func TestChangeDetail_GetContributionMergeTaskAutoGen(t *testing.T) {
	// Verify the error handling when getting contribution merge task fails.
	t.Run("testChangeDetail_GetContributionMergeTaskFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getMergeTaskOptimusMrURLRet1Mock string
			mockey.Mock(utils.GetMergeTaskOptimusMrURL, mockey.OptUnsafe).Return(getMergeTaskOptimusMrURLRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetDevDetail).Return(rMock).Build()

			var getMrMainInfoRet1Mock gresult.R[int]
			mockey.Mock(optimusService.GetMrMainInfo).Return(getMrMainInfoRet1Mock).Build()

			var getMergeTaskRet1Mock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetMergeTask).Return(getMergeTaskRet1Mock).Build()

			var adaptSmrChangesToContributionInfoRet1Mock []*dev.ContributionCodeChangeInfo
			mockey.Mock(adaptSmrChangesToContributionInfo).Return(adaptSmrChangesToContributionInfoRet1Mock).Build()

			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			ctx := context.Background()
			var withRelatedChanges bool
			var spaceID *int64
			convey.So(func() { _ = receiver.GetContributionMergeTask(ctx, withRelatedChanges, spaceID) }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_GetDevDetailAutoGen(t *testing.T) {
	// Verify the error handling when GetDevDetail fails.
	t.Run("testChangeDetail_GetDevDetailFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(GetDevDetail).Return(rMock).Build()

			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.GetDevDetail(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_IsMergedAutoGen(t *testing.T) {
	// Verify the IsMerged function of ChangeDetail.
	t.Run("testChangeDetail_IsMerged", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.IsMerged() }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_GetChecksSummaryStatusByBizConfigAutoGen(t *testing.T) {
	// Verify the behavior of the function when there is an error in getting the biz config.
	t.Run("testChangeDetail_GetChecksSummaryStatusByBizConfig", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*biz_config.ChangeBizConfigHandler).GetConfig).Return(rMock).Build()

			var changeBizConfigHandlerMockPtrValue biz_config.ChangeBizConfigHandler
			changeBizConfigHandlerMock := &changeBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewChangeBizConfigHandler).Return(changeBizConfigHandlerMock).Build()

			// prepare parameters
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.GetChecksSummaryStatusByBizConfig(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestChangeDetail_UpdateAndSaveBizConfigAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var configStrRet1Mock string
			mockey.Mock((*biz_config.BizConfigHandler).ConfigStr, mockey.OptUnsafe).Return(configStrRet1Mock).Build()

			var changeBizConfigHandlerMockPtrValue biz_config.ChangeBizConfigHandler
			changeBizConfigHandlerMock := &changeBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewChangeBizConfigHandler).Return(changeBizConfigHandlerMock).Build()

			var updateAndSaveRet1Mock error
			mockey.Mock((*biz_config.ChangeBizConfigHandler).UpdateAndSave).Return(updateAndSaveRet1Mock).Build()

			// prepare parameters
			var optionsPtrValue biz_config.ChangeBizConfigUpdateOptions
			options := &optionsPtrValue
			var receiverPtrValueCodeChange change_entity.CodeChange
			receiverPtrValue := ChangeDetail{CodeChange: &receiverPtrValueCodeChange}
			receiver := &receiverPtrValue
			ctx := context.Background()
			convey.So(func() { _ = receiver.UpdateAndSaveBizConfig(ctx, options) }, convey.ShouldPanic)
		})
	})

}

