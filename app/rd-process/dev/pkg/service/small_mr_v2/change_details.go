package small_mr_v2

import (
	"context"
	"errors"
	"sync"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"golang.org/x/sync/errgroup"
)

func InitChangesCanFastForward(ctx context.Context, changes []*ChangeDetail, useCache bool) {
	if len(changes) == 0 {
		return
	}
	var eg errgroup.Group
	for _, change := range changes {
		change := change
		if change.CanFastForward != nil {
			continue
		}
		eg.Go(func() error {
			_ = change.InitCanFastForward(ctx, useCache)
			return nil
		})
	}
	_ = eg.Wait()
}

func GetChangeDetailsByCodeChangesMrs(ctx context.Context, mrs []*git_server.CodeChangeGitlabMergeRequest) gresult.R[[]*ChangeDetail] {
	answer := make([]*ChangeDetail, 0, len(mrs))
	ccids := gslice.Filter(gslice.Map(mrs, func(mr *git_server.CodeChangeGitlabMergeRequest) int64 {
		return mr.CodeChangeID
	}), func(id int64) bool {
		return id > 0
	})
	if len(ccids) == 0 {
		return gresult.OK(answer)
	}
	contributions, err := data.OptimusDB.Slave.BatchGetContributionCodeChangesByCodeChangeIDs(ctx, ccids, 20).Get()
	if err != nil {
		log.V2.Error().With(ctx).Str("batch get contributions failed").Error(err).Emit()
		return gresult.Err[[]*ChangeDetail](err)
	}
	for _, item := range contributions {
		mrRes := gslice.Find(mrs, func(mr *git_server.CodeChangeGitlabMergeRequest) bool {
			return mr.GetCodeChangeID() == item.CodeChangeId
		})
		if !mrRes.IsOK() {
			log.V2.Error().With(ctx).Str("changes and contributions match failed").Emit()
			return gresult.Err[[]*ChangeDetail](errors.New("get change failed"))
		}
		answer = append(answer, &ChangeDetail{
			CodeChange:   &change_entity.CodeChange{Contribution: item, GitlabMr: mrRes.Value()},
			SourceBranch: mrRes.Value().GetSourceBranch(),
			TargetBranch: mrRes.Value().GetTargetBranch(),
		})
	}
	return gresult.OK(answer)
}

func GetChangeDetailsByContributions(ctx context.Context, contributions []*model.ContributionCodeChange) gresult.R[[]*ChangeDetail] {
	answer := make([]*ChangeDetail, 0, len(contributions))
	changeIDs := gslice.Filter(gslice.Map(contributions, func(c *model.ContributionCodeChange) int64 {
		return c.CodeChangeId
	}), func(id int64) bool {
		return id > 0
	})
	if len(changeIDs) == 0 {
		return gresult.OK(answer)
	}
	changeResp, err := rpc.GitServerClient.BatchGetCodeChangeGitlabMrByCodeChangeIDs(ctx, &git_server.BatchGetCodeChangeGitlabMrByCodeChangeIdsRequest{
		Cids: changeIDs,
	})
	if err != nil {
		log.V2.Error().With(ctx).Str("batch get code changes failed").Error(err).Emit()
		return gresult.Err[[]*ChangeDetail](err)
	}
	for _, item := range changeResp.Mrs {
		ctbRes := gslice.Find(contributions, func(c *model.ContributionCodeChange) bool {
			return c.CodeChangeId == item.GetCodeChangeID()
		})
		if !ctbRes.IsOK() {
			log.V2.Error().With(ctx).Str("changes and contributions match failed").Emit()
			return gresult.Err[[]*ChangeDetail](errors.New("get change code change failed"))
		}
		answer = append(answer, &ChangeDetail{
			CodeChange:   &change_entity.CodeChange{Contribution: ctbRes.Value(), GitlabMr: item},
			SourceBranch: item.GetSourceBranch(),
			TargetBranch: item.GetTargetBranch(),
		})
	}
	return gresult.OK(answer)
}

func GetChangesChecksStatus(ctx context.Context, changes []*ChangeDetail, checkResSource consts.CheckResSource, useCache bool) []dev.CheckItemStatus {
	answer := make([]dev.CheckItemStatus, 0)
	if len(changes) == 0 {
		return answer
	}
	var eg errgroup.Group
	lock := sync.Mutex{}
	for _, change := range changes {
		change := change
		eg.Go(func() error {
			status := dev.CheckItemStatus_failed
			checkRes := change_check.NewChangeCheckHelper(change.CodeChange).GetCiCheckRes(ctx, useCache, checkResSource)
			if checkRes.IsOK() {
				status = checkRes.Value()
			}
			lock.Lock()
			answer = append(answer, status)
			lock.Unlock()
			return nil
		})
	}
	_ = eg.Wait()
	return answer
}

func BatchGetChangeReviewInfo(ctx context.Context, changes []*ChangeDetail) error {
	if len(changes) == 0 {
		return nil
	}
	var eg errgroup.Group
	for _, change := range changes {
		change := change
		eg.Go(func() error {
			reviewInfo, err := change.GetReviewInfo(ctx).Get()
			if err != nil {
				return err
			}
			change.ReviewInfo = reviewInfo
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return err
	}
	return nil
}
