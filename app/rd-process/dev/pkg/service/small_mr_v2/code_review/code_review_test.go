package code_review

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	resty "github.com/go-resty/resty/v2"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/lock"
	"code.byted.org/lang/gg/gresult"
)

func TestGetReviewInfo(t *testing.T) {
	PatchConvey("get review info", t, func() {
		Mock(gitsvr.GetCodebaseRequestedReviewers).Return(gresult.OK([]*git_server.CodebaseUser{{
			Username: "userX"}, {Username: "userY"}, {Username: "userZ"}})).Build()
		Mock(gitsvr.GetReviewStatusWithoutCache).Return(gresult.OK(&git_server.ReviewSummary{
			Status: "passed",
			Disapprovers: []*git_server.CodebaseUser{{
				Username: "userX",
			}},
			Approvals: []*git_server.ReviewRuleApprovalInfo{
				{
					Approvers: []*git_server.CodebaseUser{{
						Username: "userY",
					}},
					ReviewRule: &git_server.ReviewRule2{
						Name:              "xx",
						ApprovalsRequired: 1,
						Users: []*git_server.CodebaseUser{{
							Username: "userY",
						}, {
							Username: "userZ",
						}},
					},
				},
			},
		})).Build()
		_, err := GetReviewInfo(context.TODO(), 1, 1, "userX").Get()
		So(err, ShouldBeNil)
	})
}

func TestGetReviewInfoV2(t *testing.T) {
	t.Skip()
	PatchConvey("get review info", t, func() {
		Mock(gitsvr.GetCodebaseRequestedReviewers).Return(gresult.OK([]*git_server.CodebaseUser{{
			Username: "userX"}, {Username: "userY"}, {Username: "userZ"}})).Build()
		Mock(gitsvr.GetReviewStatusWithoutCache).Return(gresult.OK(&git_server.ReviewSummary{
			Status: "passed",
			Approvals: []*git_server.ReviewRuleApprovalInfo{
				{
					Approvers: []*git_server.CodebaseUser{{
						Username: "userY",
					}, {
						Username: "userZ",
					}},
					ReviewRule: &git_server.ReviewRule2{
						Name:              "xx",
						ApprovalsRequired: 1,
						Users: []*git_server.CodebaseUser{
							{
								Username: "userY",
							}, {
								Username: "userZ",
							},
						},
					},
				},
			},
		})).Build()
		Mock(gitsvr.GetCodeChangeReview).Return(gresult.OK([]*git_server.CodebaseReview{
			{
				Reviewer: &git_server.CodebaseUser{
					Username: "userX",
				},
				Event: "approve",
			},
		})).Build()
		Mock(meta.CheckIfUserResigned).Return(gresult.OK(false)).Build()
		_, err := GetReviewInfoV2(context.TODO(), 1, 1, "userX").Get()
		So(err, ShouldBeNil)
	})
}

func TestGetReviewMode(t *testing.T) {
	PatchConvey("get review mode", t, func() {
		Mock(tcc.GetSmrSpaceCodeReviewConfig).Return(&tcc.SmrSpaceCodeReviewConfig{
			CodeReviewConfig: []*tcc.CodeReviewConfig{{
				SpaceID:    11,
				RemindMode: consts.SmrReviewRemindModeReviewRule,
			}},
		}, nil).Build()
		mode := GetRemindMode(context.TODO(), 11)
		So(mode, ShouldEqual, consts.SmrReviewRemindModeReviewRule)
	})
}

func TestGetNeedRemindReviewUsers(t *testing.T) {
	PatchConvey("get review mode", t, func() {
		reviewInfo := &dev.ReviewInfo{
			ReviewersInfo: []*dev.ReviewerInfo{{
				Username: "X",
				Status:   dev.ReviewStatus_RUNNING,
			}},
			ReviewStatus: dev.ReviewStatus_RUNNING,
			ReviewRuleItems: []*dev.ReviewRuleItem{{
				IfPass: false,
				ReviewersInfo: []*dev.ReviewerInfo{{
					Username: "X",
					Status:   dev.ReviewStatus_RUNNING,
				}, {
					Username: "Y",
					Status:   dev.ReviewStatus_RUNNING,
				}},
				ApprovalsRequired: 1,
			}},
		}
		reviewers := GetNeedRemindReviewUsers(reviewInfo, consts.SmrReviewRemindModeRequestedReviewer)
		So(reviewers, ShouldHaveLength, 1)
		reviewers = GetNeedRemindReviewUsers(reviewInfo, consts.SmrReviewRemindModeReviewRule)
		So(reviewers, ShouldHaveLength, 2)
	})
}

func TestFormReviewInfoByReviewSummaryAndReviewsV2(t *testing.T) {
	t.Skip()
	type args struct {
		ctx                context.Context
		reviewSummary      *git_server.ReviewSummary
		reviewStatus       []*git_server.CodebaseReview
		requestedReviewers []*git_server.CodebaseUser
	}
	var requestedReviewers []*git_server.CodebaseUser
	//reviewSummaryString :=
	requestedReviewersString := "[{\"id\":2575085,\"username\":\"lizhuopeng\",\"name\":\"lizhuopeng\",\"email\":\"<EMAIL>\"},{\"id\":2582525,\"username\":\"chenshixing\",\"name\":\"chenshixing\",\"email\":\"<EMAIL>\"},{\"id\":5255105,\"username\":\"chengjiaming.1212\",\"name\":\"chengjiaming.1212\",\"email\":\"<EMAIL>\"},{\"id\":8820537,\"username\":\"jiangpengyun.rd\",\"name\":\"jiangpengyun.rd\",\"email\":\"<EMAIL>\"},{\"id\":9502812,\"username\":\"yanzhiwei.arida\",\"name\":\"yanzhiwei.arida\",\"email\":\"<EMAIL>\"}]"
	sonic.Unmarshal([]byte(requestedReviewersString), &requestedReviewers)

	var reviewSummary git_server.ReviewSummary
	reviewSummaryString := "{\"status\":\"pending\",\"disapprovers\":[{\"id\":8820537,\"username\":\"jiangpengyun.rd\",\"name\":\"jiangpengyun.rd\",\"email\":\"<EMAIL>\"}],\"approvals\":[{\"approvers\":[{\"id\":2575085,\"username\":\"lizhuopeng\",\"name\":\"lizhuopeng\",\"email\":\"<EMAIL>\"}],\"review_rule\":{\"id\":0,\"parent_id\":0,\"default\":false,\"type\":1,\"app_id\":36,\"group_name\":\"bits-scm\",\"name\":\"bits_openapi_removable_rule\",\"approvals_required\":4,\"users\":[{\"id\":5255105,\"username\":\"chengjiaming.1212\",\"name\":\"chengjiaming.1212\",\"email\":\"<EMAIL>\"},{\"id\":9502812,\"username\":\"yanzhiwei.arida\",\"name\":\"yanzhiwei.arida\",\"email\":\"<EMAIL>\"},{\"id\":2575085,\"username\":\"lizhuopeng\",\"name\":\"lizhuopeng\",\"email\":\"<EMAIL>\"},{\"id\":8820537,\"username\":\"jiangpengyun.rd\",\"name\":\"jiangpengyun.rd\",\"email\":\"<EMAIL>\"}],\"groups\":[],\"user_reasons\":{\"8820537\":\"[[架构 BP]] 添加架构 BP 同学 review（若 Reviewer 未快速响应，请移除）\",\"9502812\":\"[[分组 review 对应的 reviewer]] 命中分组 review 规则，添加对应同学 review（若 Reviewer 未快速响应，请移除）\",\"2575085\":\"[[开发任务关注人]] Small MR Reviewer（若 Reviewer 未快速响应，请移除）\",\"5255105\":\"[[分组 review 对应的 reviewer]] 命中分组 review 规则，添加对应同学 review（若 Reviewer 未快速响应，请移除）\"},\"user_operators\":{}}},{\"approvers\":[],\"review_rule\":{\"id\":0,\"parent_id\":0,\"default\":false,\"type\":1,\"app_id\":36,\"group_name\":\"bits-scm\",\"name\":\"file:Module/TTGeneralVideo/OWNERS.bits.yml:.*\",\"approvals_required\":1,\"users\":[{\"id\":5961286,\"username\":\"lvhan.dpp\",\"name\":\"lvhan.dpp\",\"email\":\"<EMAIL>\"},{\"id\":2582525,\"username\":\"chenshixing\",\"name\":\"chenshixing\",\"email\":\"<EMAIL>\"},{\"id\":8295058,\"username\":\"yuyifeng.capt\",\"name\":\"yuyifeng.capt\",\"email\":\"<EMAIL>\"}],\"groups\":[],\"user_reasons\":{\"2582525\":\"- `Module/TTGeneralVideo/Source/Classes/Player/Part/TTVPictureInPictureButtonPart.h`\\n  - `Module/TTGeneralVideo/Source/Classes/Player/Part/TTVPictureInPictureButtonPart.m`\",\"5961286\":\"- `Module/TTGeneralVideo/Source/Classes/Player/Part/TTVPictureInPictureButtonPart.h`\\n  - `Module/TTGeneralVideo/Source/Classes/Player/Part/TTVPictureInPictureButtonPart.m`\",\"8295058\":\"- `Module/TTGeneralVideo/Source/Classes/Player/Part/TTVPictureInPictureButtonPart.h`\\n  - `Module/TTGeneralVideo/Source/Classes/Player/Part/TTVPictureInPictureButtonPart.m`\"},\"user_operators\":{}}},{\"approvers\":[],\"review_rule\":{\"id\":0,\"parent_id\":0,\"default\":false,\"type\":1,\"app_id\":36,\"group_name\":\"bits-scm\",\"name\":\"file:Module/WKBrowserMovieBiz/OWNERS.bits.yml:.*\",\"approvals_required\":1,\"users\":[{\"id\":9502812,\"username\":\"yanzhiwei.arida\",\"name\":\"yanzhiwei.arida\",\"email\":\"<EMAIL>\"},{\"id\":6335906,\"username\":\"zhanghuipei\",\"name\":\"zhanghuipei\",\"email\":\"<EMAIL>\"},{\"id\":6077526,\"username\":\"huangzhenshu\",\"name\":\"huangzhenshu\",\"email\":\"<EMAIL>\"}],\"groups\":[],\"user_reasons\":{\"6077526\":\"- `Module/WKBrowserMovieBiz/Source/IndividualPage/Player/WKBIndividualPagePlayerLayout.m`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Plugin/Core/UI/WKBIndPlayerViewPlugin.m`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Plugin/WKBIndividualPagePlayerPlugin.m`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Setting/WKBIndividualPageSettings.h`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Setting/WKBIndividualPageSettings.m`\",\"6335906\":\"- `Module/WKBrowserMovieBiz/Source/IndividualPage/Player/WKBIndividualPagePlayerLayout.m`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Plugin/Core/UI/WKBIndPlayerViewPlugin.m`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Plugin/WKBIndividualPagePlayerPlugin.m`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Setting/WKBIndividualPageSettings.h`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Setting/WKBIndividualPageSettings.m`\",\"9502812\":\"- `Module/WKBrowserMovieBiz/Source/IndividualPage/Player/WKBIndividualPagePlayerLayout.m`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Plugin/Core/UI/WKBIndPlayerViewPlugin.m`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Plugin/WKBIndividualPagePlayerPlugin.m`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Setting/WKBIndividualPageSettings.h`\\n  - `Module/WKBrowserMovieBiz/Source/IndividualPage/Setting/WKBIndividualPageSettings.m`\"},\"user_operators\":{}}},{\"approvers\":[],\"review_rule\":{\"id\":0,\"parent_id\":0,\"default\":false,\"type\":1,\"app_id\":36,\"group_name\":\"bits-scm\",\"name\":\"file:external/BDMediaPlayer/OWNERS.bits.yml:.*\",\"approvals_required\":1,\"users\":[{\"id\":2582525,\"username\":\"chenshixing\",\"name\":\"chenshixing\",\"email\":\"<EMAIL>\"},{\"id\":8295058,\"username\":\"yuyifeng.capt\",\"name\":\"yuyifeng.capt\",\"email\":\"<EMAIL>\"}],\"groups\":[],\"user_reasons\":{\"8295058\":\"- `external/BDMediaPlayer/BDMediaPlayer/Classes/Define/BDMediaPlayerPartControlKeyDefine.h`\",\"2582525\":\"- `external/BDMediaPlayer/BDMediaPlayer/Classes/Define/BDMediaPlayerPartControlKeyDefine.h`\"},\"user_operators\":{}}}]}"
	sonic.Unmarshal([]byte(reviewSummaryString), &reviewSummary)

	var reviewStatus []*git_server.CodebaseReview
	reviewStatusString := "[{\"id\":0,\"sha\":\"6cdbd02fa5ed4cfbc94cde692a4980ac059f419e\",\"reviewer\":{\"id\":8820537,\"username\":\"jiangpengyun.rd\",\"name\":\"jiangpengyun.rd\",\"email\":\"<EMAIL>\"},\"event\":\"disapprove\"},{\"id\":0,\"sha\":\"6cdbd02fa5ed4cfbc94cde692a4980ac059f419e\",\"reviewer\":{\"id\":2575085,\"username\":\"lizhuopeng\",\"name\":\"lizhuopeng\",\"email\":\"<EMAIL>\"},\"event\":\"approve\"}]"
	sonic.Unmarshal([]byte(reviewStatusString), &reviewStatus)

	tests := []struct {
		name string
		args args
		want *dev.ReviewInfo
	}{
		{
			name: "test 1",
			args: args{
				ctx:                context.Background(),
				reviewSummary:      &reviewSummary,
				reviewStatus:       reviewStatus,
				requestedReviewers: requestedReviewers,
			},
			want: &dev.ReviewInfo{
				ReviewStatus: dev.ReviewStatus_REJECTED,
			},
		},
	}
	for _, tt := range tests {
		Mock(meta.CheckIfUserResigned).Return(gresult.OK(false)).Build()
		t.Run(tt.name, func(t *testing.T) {
			got := FormReviewInfoByReviewSummaryAndReviewsV2(tt.args.ctx, tt.args.reviewSummary, tt.args.reviewStatus, tt.args.requestedReviewers)
			if !reflect.DeepEqual(got.ReviewStatus, tt.want.ReviewStatus) {
				t.Errorf("FormReviewInfoByReviewSummaryAndReviewsV2() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAdaptGitReviewSummaryStatusToDevReviewStatusAutoGen(t *testing.T) {
	// param1 != "passed"
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			status := "not passed"

			// run target function and assert
			got1 := AdaptGitReviewSummaryStatusToDevReviewStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 3)
		})
	})

}

func TestAdaptReviewEventToReviewStatusAutoGen(t *testing.T) {
	// Verify the function returns the correct status when the event is 'approve'.
	t.Run("testAdaptReviewEventToReviewStatus_Approve", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			event := "approve"

			// run target function and assert
			got1 := AdaptReviewEventToReviewStatus(event)
			convey.So(int64(got1), convey.ShouldEqual, 4)
		})
	})

	// Verify the function returns the correct status when the event is 'disapprove'.
	t.Run("testAdaptReviewEventToReviewStatus_Disapprove", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			event := "disapprove"

			// run target function and assert
			got1 := AdaptReviewEventToReviewStatus(event)
			convey.So(int64(got1), convey.ShouldEqual, 5)
		})
	})

	// Verify the function returns the correct status in the default case.
	t.Run("testAdaptReviewEventToReviewStatus_Default", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// prepare parameters
			var event string

			// run target function and assert
			got1 := AdaptReviewEventToReviewStatus(event)
			convey.So(int64(got1), convey.ShouldEqual, 3)
		})
	})

}

func TestFormReviewInfoByReviewSummaryAndReviewsV2AutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:IsErr()_ret-1 == true
	// code.byted.org/gopkg/logs/v2.Log:KVs()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(meta.CheckIfUserResigned).Return(rMock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*git_server.CodebaseUser).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var adaptGitReviewSummaryStatusToDevReviewStatusRet1Mock dev.ReviewStatus
			mockey.Mock(AdaptGitReviewSummaryStatusToDevReviewStatus).Return(adaptGitReviewSummaryStatusToDevReviewStatusRet1Mock).Build()

			var reviewStatusMock dev.ReviewStatus
			mockey.Mock(AdaptReviewEventToReviewStatus).Return(reviewStatusMock).Build()

			// prepare parameters
			ctx := context.Background()
			var reviewSummaryPtrValue git_server.ReviewSummary
			reviewSummary := &reviewSummaryPtrValue
			var reviewStatus []*git_server.CodebaseReview
			var requestedReviewers []*git_server.CodebaseUser

			// run target function and assert
			got1 := FormReviewInfoByReviewSummaryAndReviewsV2(ctx, reviewSummary, reviewStatus, requestedReviewers)
			convey.So((*got1).ReviewerCount == nil, convey.ShouldBeFalse)
			convey.So((*got1).ReviewRules == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).ReviewRuleItems), convey.ShouldEqual, 0)
			convey.So((*got1).Skipped == nil, convey.ShouldBeTrue)
			convey.So(int64((*got1).ReviewStatus), convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).ReviewersInfo), convey.ShouldEqual, 0)
		})
	})

	// code.byted.org/lang/gg/gresult.R:IsErr()_ret-1 == true
	// code.byted.org/gopkg/logs/v2.Log:KVs()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.Log:Str()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.Log:With()_ret-1 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getUsernameRet1Mock string
			mockey.Mock((*git_server.CodebaseUser).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var adaptGitReviewSummaryStatusToDevReviewStatusRet1Mock dev.ReviewStatus
			mockey.Mock(AdaptGitReviewSummaryStatusToDevReviewStatus).Return(adaptGitReviewSummaryStatusToDevReviewStatusRet1Mock).Build()

			var reviewStatusMock dev.ReviewStatus
			mockey.Mock(AdaptReviewEventToReviewStatus).Return(reviewStatusMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.CheckIfUserResigned).Return(rMock).Build()

			// prepare parameters
			var reviewSummaryPtrValue git_server.ReviewSummary
			reviewSummary := &reviewSummaryPtrValue
			var reviewStatus []*git_server.CodebaseReview
			var requestedReviewers []*git_server.CodebaseUser
			ctx := context.Background()

			// run target function and assert
			got1 := FormReviewInfoByReviewSummaryAndReviewsV2(ctx, reviewSummary, reviewStatus, requestedReviewers)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).ReviewersInfo), convey.ShouldEqual, 0)
			convey.So((*got1).ReviewerCount == nil, convey.ShouldBeFalse)
			convey.So((*got1).ReviewRules == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).ReviewRuleItems), convey.ShouldEqual, 0)
			convey.So((*got1).Skipped == nil, convey.ShouldBeTrue)
			convey.So(int64((*got1).ReviewStatus), convey.ShouldEqual, 0)
		})
	})

	// code.byted.org/lang/gg/gresult.R:IsErr()_ret-1 != true
	// param2 != nil
	// len(param2.Disapprovers) > 0
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var reviewStatusMock dev.ReviewStatus
			mockey.Mock(AdaptReviewEventToReviewStatus).Return(reviewStatusMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.CheckIfUserResigned).Return(rMock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*git_server.CodebaseUser).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var adaptGitReviewSummaryStatusToDevReviewStatusRet1Mock dev.ReviewStatus
			mockey.Mock(AdaptGitReviewSummaryStatusToDevReviewStatus).Return(adaptGitReviewSummaryStatusToDevReviewStatusRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reviewSummaryPtrValue git_server.ReviewSummary
			reviewSummary := &reviewSummaryPtrValue
			var reviewSummaryDisapproversItem0PtrValue git_server.CodebaseUser
			reviewSummaryDisapproversItem0 := &reviewSummaryDisapproversItem0PtrValue
			reviewSummary.Disapprovers = []*git_server.CodebaseUser{reviewSummaryDisapproversItem0}
			var reviewStatus []*git_server.CodebaseReview
			var requestedReviewers []*git_server.CodebaseUser

			// run target function and assert
			got1 := FormReviewInfoByReviewSummaryAndReviewsV2(ctx, reviewSummary, reviewStatus, requestedReviewers)
			convey.So((*got1).ReviewerCount == nil, convey.ShouldBeFalse)
			convey.So((*got1).ReviewRules == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).ReviewRuleItems), convey.ShouldEqual, 0)
			convey.So((*got1).Skipped == nil, convey.ShouldBeTrue)
			convey.So(int64((*got1).ReviewStatus), convey.ShouldEqual, 5)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).ReviewersInfo), convey.ShouldEqual, 0)
		})
	})

	// code.byted.org/lang/gg/gresult.R:IsErr()_ret-1 != true
	// len(param2.Approvals) > 0
	// param2.Approvals[0].ReviewRule != nil
	// param2.Approvals[0] != nil
	// len(param2.Approvals[0].ReviewRule.Users) > 0
	// param2.Approvals[0].ReviewRule.Users[0] != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var adaptGitReviewSummaryStatusToDevReviewStatusRet1Mock dev.ReviewStatus
			mockey.Mock(AdaptGitReviewSummaryStatusToDevReviewStatus).Return(adaptGitReviewSummaryStatusToDevReviewStatusRet1Mock).Build()

			var reviewStatusMock dev.ReviewStatus
			mockey.Mock(AdaptReviewEventToReviewStatus).Return(reviewStatusMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.CheckIfUserResigned).Return(rMock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*git_server.CodebaseUser).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reviewSummaryPtrValue git_server.ReviewSummary
			reviewSummary := &reviewSummaryPtrValue
			reviewSummary.Approvals = []*git_server.ReviewRuleApprovalInfo{}
			var reviewSummaryApprovals0ItemPtrValue git_server.ReviewRuleApprovalInfo
			reviewSummaryApprovals0Item := &reviewSummaryApprovals0ItemPtrValue
			reviewSummary.Approvals = append(reviewSummary.Approvals, reviewSummaryApprovals0Item)
			var reviewSummaryApprovals0ReviewRulePtrValue git_server.ReviewRule2
			reviewSummary.Approvals[0].ReviewRule = &reviewSummaryApprovals0ReviewRulePtrValue
			reviewSummary.Approvals[0].ReviewRule.Users = []*git_server.CodebaseUser{}
			var reviewSummaryApprovals0ReviewRuleUsers0ItemPtrValue git_server.CodebaseUser
			reviewSummaryApprovals0ReviewRuleUsers0Item := &reviewSummaryApprovals0ReviewRuleUsers0ItemPtrValue
			reviewSummary.Approvals[0].ReviewRule.Users = append(reviewSummary.Approvals[0].ReviewRule.Users, reviewSummaryApprovals0ReviewRuleUsers0Item)
			var reviewStatus []*git_server.CodebaseReview
			var requestedReviewers []*git_server.CodebaseUser

			// run target function and assert
			got1 := FormReviewInfoByReviewSummaryAndReviewsV2(ctx, reviewSummary, reviewStatus, requestedReviewers)
			convey.So((*got1).ReviewerCount == nil, convey.ShouldBeFalse)
			convey.So((*got1).ReviewRules == nil, convey.ShouldBeTrue)
			convey.So(len((*got1).ReviewRuleItems), convey.ShouldEqual, 1)
			convey.So((*got1).Skipped == nil, convey.ShouldBeTrue)
			convey.So(int64((*got1).ReviewStatus), convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).ReviewersInfo), convey.ShouldEqual, 0)
		})
	})

}

func TestGetRemindModeAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc:GetSmrSpaceCodeReviewConfig()_ret-1 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var smrSpaceCodeReviewConfigMock *tcc.SmrSpaceCodeReviewConfig
			var getSmrSpaceCodeReviewConfigRet2Mock error
			mockey.Mock(tcc.GetSmrSpaceCodeReviewConfig).Return(smrSpaceCodeReviewConfigMock, getSmrSpaceCodeReviewConfigRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var spaceID int64

			// run target function and assert
			got1 := GetRemindMode(ctx, spaceID)
			convey.So(got1, convey.ShouldEqual, "requested_reviewer")
		})
	})

	// default condition
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var smrSpaceCodeReviewConfigMockPtrValue tcc.SmrSpaceCodeReviewConfig
			smrSpaceCodeReviewConfigMock := &smrSpaceCodeReviewConfigMockPtrValue
			var getSmrSpaceCodeReviewConfigRet2Mock error
			mockey.Mock(tcc.GetSmrSpaceCodeReviewConfig).Return(smrSpaceCodeReviewConfigMock, getSmrSpaceCodeReviewConfigRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var spaceID int64

			// run target function and assert
			got1 := GetRemindMode(ctx, spaceID)
			convey.So(got1, convey.ShouldEqual, "requested_reviewer")
		})
	})

}

func TestGetDevSmrReviewInfoAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis:TTL()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getDevTaskReviewRemindLockKeyRet1Mock string
			mockey.Mock(lock.GetDevTaskReviewRemindLockKey, mockey.OptUnsafe).Return(getDevTaskReviewRemindLockKeyRet1Mock).Build()

			var tTLRet1Mock time.Duration
			tTLRet2Mock := fmt.Errorf("error")
			mockey.Mock(redis.TTL).Return(tTLRet1Mock, tTLRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			convey.So(func() { _ = GetDevSmrReviewInfo(ctx, devBasicID) }, convey.ShouldNotPanic)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis:TTL()_ret-2 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var tTLRet1Mock time.Duration
			var tTLRet2Mock error
			mockey.Mock(redis.TTL).Return(tTLRet1Mock, tTLRet2Mock).Build()

			var getDevTaskReviewRemindLockKeyRet1Mock string
			mockey.Mock(lock.GetDevTaskReviewRemindLockKey, mockey.OptUnsafe).Return(getDevTaskReviewRemindLockKeyRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			convey.So(func() { _ = GetDevSmrReviewInfo(ctx, devBasicID) }, convey.ShouldNotPanic)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis:TTL()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis:TTL()_ret-1 < 0
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var getDevTaskReviewRemindLockKeyRet1Mock string
			mockey.Mock(lock.GetDevTaskReviewRemindLockKey, mockey.OptUnsafe).Return(getDevTaskReviewRemindLockKeyRet1Mock).Build()

			tTLRet1MockAlias := int64(-1)
			tTLRet1Mock := time.Duration(tTLRet1MockAlias)
			var tTLRet2Mock error
			mockey.Mock(redis.TTL).Return(tTLRet1Mock, tTLRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			convey.So(func() { _ = GetDevSmrReviewInfo(ctx, devBasicID) }, convey.ShouldNotPanic)
		})
	})

}

func TestSyncReviewConfigAutoGen(t *testing.T) {
	// Verify the scenario in which SyncReviewConfig executes successfully.
	t.Run("testSyncReviewConfig_Success", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var postRet1MockPtrValue resty.Response
			postRet1Mock := &postRet1MockPtrValue
			postRet2Mock := fmt.Errorf("error")
			mockey.Mock((*functools.CommonClient).Post).Return(postRet1Mock, postRet2Mock).Build()

			var commonClientMockPtrValue functools.CommonClient
			commonClientMock := &commonClientMockPtrValue
			mockey.Mock(functools.NewSMRClient, mockey.OptUnsafe).Return(commonClientMock).Build()

			// prepare parameters
			ctx := context.Background()
			var codebaseRepoID int64
			var mriid int64
			convey.So(func() { _ = SyncReviewConfig(ctx, codebaseRepoID, mriid) }, convey.ShouldNotPanic)
		})
	})

	// Verify the scenario in which SyncReviewConfig executes successfully.
	t.Run("testSyncReviewConfig_Success", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var postRet1MockPtrValue resty.Response
			postRet1Mock := &postRet1MockPtrValue
			var postRet2Mock error
			mockey.Mock((*functools.CommonClient).Post).Return(postRet1Mock, postRet2Mock).Build()

			var commonClientMockPtrValue functools.CommonClient
			commonClientMock := &commonClientMockPtrValue
			mockey.Mock(functools.NewSMRClient, mockey.OptUnsafe).Return(commonClientMock).Build()

			// prepare parameters
			ctx := context.Background()
			var codebaseRepoID int64
			var mriid int64
			convey.So(func() { _ = SyncReviewConfig(ctx, codebaseRepoID, mriid) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetIfReviewPassAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 != nil
	// code.byted.org/gopkg/logs/v2.Log:Error()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.Log:With()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(gitsvr.GetReviewStatus).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var codebaseRepoID int64
			var codebaseChangeID int64
			var spaceId int64
			convey.So(func() { _ = GetIfReviewPass(ctx, codebaseRepoID, codebaseChangeID, spaceId) }, convey.ShouldPanic)
		})
	})

}
