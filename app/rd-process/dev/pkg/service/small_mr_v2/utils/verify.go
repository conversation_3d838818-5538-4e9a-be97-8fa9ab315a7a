package utils

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"context"
)

func VerifyCreateDevConfig(config *dev.CreateDevConfig, smrConfig *dev.DevSmrCreationConfig) error {
	if config == nil {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("config null")
	}
	if len(smrConfig.TargetBranch) == 0 {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("target branch null")
	}
	if len(smrConfig.RepoPath) == 0 {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("repo path null")
	}
	return nil
}

func VerifyContributionCodeChangeCreationConfig(config *dev.ContributionCodeChangeCreationConfig) error {
	if config == nil {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("config null")
	}
	if len(config.Title) == 0 || len(config.RepoPath) == 0 || len(config.SourceBranch) == 0 || len(config.TargetBranch) == 0 {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("lack of params now")
	}
	return nil
}

func VerifyContributionCodeChangePipelineCreationConfig(config *dev.ContributionCodeChangePipelineCreationConfig) error {
	if config == nil {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("config null")
	}
	if config.ContributionID == 0 || config.PipelineID == 0 {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid id")
	}
	return nil
}

func VerifyUserOperationLevel(ctx context.Context, username string, operationLevel dev.DevOperationLevel, devID int64, devBasicAuthor string) bool {
	switch operationLevel {
	case dev.DevOperationLevel_all:
		return true
	case dev.DevOperationLevel_collaborators:
		collaborators, err := data.OptimusDB.Slave.UseCache().GetDevCollaboratorNames(ctx, devID)
		if err != nil {
			logs.CtxError(ctx, "get collaborators failed error  = %s", err.Error())
			return false
		}
		collaborators = append(collaborators, devBasicAuthor)
		if gslice.Contains(collaborators, username) {
			return true
		}
	case dev.DevOperationLevel_author:
		if username == devBasicAuthor {
			return true
		}
	}
	return false
}

func VerifyIfCodeChangeNeedToUpdate(options *dev.ContributionCodeChangeUpdateOptions, ccid int64) bool {
	return ccid > 0 &&
		(options.Title != nil ||
			options.Description != nil ||
			options.TargetBranch != nil ||
			options.Wip != nil ||
			options.Draft != nil)
}
