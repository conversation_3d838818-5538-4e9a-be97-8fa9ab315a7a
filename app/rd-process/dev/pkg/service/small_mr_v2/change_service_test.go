package small_mr_v2

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_info_pack"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_task_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/lark_group"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/user"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils"
	"code.byted.org/lang/gg/gresult"
)

func TestGetChangeRelatedChangesInfo(t *testing.T) {
	PatchConvey("user admin role", t, func() {
		Mock(GetChangeDetail).Return(gresult.OK(&ChangeDetail{
			CodeChange: &change_entity.CodeChange{
				Contribution: &model.ContributionCodeChange{},
				GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
			},
			SourceBranch: "",
			TargetBranch: "",
		})).Build()
		sc := StackedChanges{{
			previous: nil,
			next:     nil,
			ChangeDetail: &ChangeDetail{CodeChange: &change_entity.CodeChange{
				Contribution: &model.ContributionCodeChange{},
				GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
			},
			}}}
		Mock((*ChangeDetail).GetRelatedStackedChanges).Return(gresult.OK(
			sc)).Build()
		Mock(GetDevDetail).Return(gresult.OK(&DevDetail{
			DevTask: &dev_task_entity.DevTask{
				BasicInfo: &model.DevBasicInfo{},
				Config:    &model.DevSmrConfig{},
			},
		})).Build()
		Mock((*change_info_pack.ChangeInfoPackHelper).PackChanges).Return(gresult.OK(
			[]*change_info_pack.ChangeInfo{})).Build()
		resp, _ := NewChangeServiceImpl().GetChangeRelatedChangesInfo(
			context.TODO(),
			1,
			dev.RelatedContributionCodeChangeOption_whole_stack,
			true,
			false,
			false,
			false,
		).Get()
		t.Logf(fmt.Sprintf("resp:%v", resp))
	})
}

func TestCreateChangeLarkGroup(t *testing.T) {
	PatchConvey("create change review group", t, func() {
		Mock(change_entity.GetCodeChange).Return(gresult.OK(&change_entity.CodeChange{
			Contribution: &model.ContributionCodeChange{},
			GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
		})).Build()
		Mock(lark_group.CreateChangeReviewLarkGroup).Return(gresult.OK(
			"idxxx")).Build()
		resp, _ := NewChangeServiceImpl().CreateChangeLarkGroup(context.TODO(), 1, "xxx").Get()
		t.Logf(fmt.Sprintf("resp:%v", resp))
	})
}

func TestGetContributionInfo(t *testing.T) {
	PatchConvey("get contribution info", t, func() {
		PatchConvey("get contribution info by cid", func() {
			Mock(change_entity.GetCodeChange).Return(gresult.OK(&change_entity.CodeChange{
				Contribution: &model.ContributionCodeChange{},
				GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
			})).Build()
			Mock((*change_info_pack.ChangeInfoPackHelper).PackChange).Return(gresult.OK(&change_info_pack.ChangeInfo{
				Change: &dev.ContributionCodeChangeInfo{},
			})).Build()
			resp, err := NewChangeServiceImpl().GetContributionInfoByCid(context.TODO(), 1).Get()
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			t.Logf(fmt.Sprintf("resp:%v", resp))
		})
		PatchConvey("get contribution index by dev id", func() {
			Mock((*data.Repo).GetContributionIndex).Return(gresult.OK(&model.ContributionCodeChangeIndex{
				DevID: 1,
				ID:    33,
			})).Build()
			resp, err := NewChangeServiceImpl().GetContributionIndexByDevID(context.TODO(), 1).Get()
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.ID, ShouldEqual, 33)
			t.Logf(fmt.Sprintf("resp:%v", resp))
		})
		PatchConvey("get contribution info by ccid", func() {
			Mock(change_entity.GetCodeChangeByCodeChangeID).Return(gresult.OK(&change_entity.CodeChange{
				Contribution: &model.ContributionCodeChange{},
				GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
			})).Build()
			Mock((*change_info_pack.ChangeInfoPackHelper).PackChange).Return(gresult.OK(&change_info_pack.ChangeInfo{
				Change: &dev.ContributionCodeChangeInfo{},
			})).Build()
			resp, err := NewChangeServiceImpl().GetContributionInfoByCodeChangeID(context.TODO(), 1).Get()
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			t.Logf(fmt.Sprintf("resp:%v", resp))
		})
	})
}

func TestChangeServiceImpl_UpdateChangeCheckStatusAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var changeCheckHelperMockPtrValue change_check.ChangeCheckHelper
			changeCheckHelperMock := &changeCheckHelperMockPtrValue
			mockey.Mock(change_check.NewChangeCheckHelper, mockey.OptUnsafe).Return(changeCheckHelperMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(change_entity.GetCodeChange).Return(rMock).Build()

			var updateCheckStatusRet1Mock error
			mockey.Mock((*change_check.ChangeCheckHelper).UpdateCheckStatus).Return(updateCheckStatusRet1Mock).Build()

			// prepare parameters
			var checkName string
			var checkType dev.ContributionCheckType
			var status dev.ContributionCodeChangeCheckStatus
			var receiverPtrValue ChangeServiceImpl
			receiver := &receiverPtrValue
			ctx := context.Background()
			var cid int64
			convey.So(func() { _ = receiver.UpdateChangeCheckStatus(ctx, cid, checkName, checkType, status) }, convey.ShouldPanic)
		})
	})

}

func TestChangeServiceImpl_TriggerSyncChecksStatusAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check:NewChangeCheckHelper()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var changeCheckHelperMockPtrValue change_check.ChangeCheckHelper
			changeCheckHelperMock := &changeCheckHelperMockPtrValue
			mockey.Mock(change_check.NewChangeCheckHelper, mockey.OptUnsafe).Return(changeCheckHelperMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(change_entity.GetCodeChange).Return(rMock).Build()

			var triggerSyncChecksStatusRet1Mock error
			mockey.Mock((*change_check.ChangeCheckHelper).TriggerSyncChecksStatus).Return(triggerSyncChecksStatusRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue ChangeServiceImpl
			receiver := &receiverPtrValue
			ctx := context.Background()
			var cid int64
			convey.So(func() { _ = receiver.TriggerSyncChecksStatus(ctx, cid) }, convey.ShouldPanic)
		})
	})

}

func TestChangeServiceImpl_DismissChangeLarkGroupAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:UseCache()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var dismissLarkGroupRet1Mock error
			mockey.Mock(meta.DismissLarkGroup).Return(dismissLarkGroupRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetContributionCodeChangeByID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			// prepare parameters
			var receiverPtrValue ChangeServiceImpl
			receiver := &receiverPtrValue
			ctx := context.Background()
			var cid int64
			convey.So(func() { _ = receiver.DismissChangeLarkGroup(ctx, cid) }, convey.ShouldPanic)
		})
	})

}

func TestChangeServiceImpl_TriggerChangeNewPipelineCheckAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils:VerifyContributionCodeChangePipelineCreationConfig()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.Log:Error()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(change_entity.GetCodeChange).Return(rMock).Build()

			var getContributionIDRet1Mock int64
			mockey.Mock((*dev.ContributionCodeChangePipelineCreationConfig).GetContributionID, mockey.OptUnsafe).Return(getContributionIDRet1Mock).Build()

			var triggerNewPipelineCheckRet1Mock gresult.R[int]
			mockey.Mock((*change_check.ChangeCheckHelper).TriggerNewPipelineCheck).Return(triggerNewPipelineCheckRet1Mock).Build()

			verifyContributionCodeChangePipelineCreationConfigRet1Mock := fmt.Errorf("error")
			mockey.Mock(utils.VerifyContributionCodeChangePipelineCreationConfig).Return(verifyContributionCodeChangePipelineCreationConfigRet1Mock).Build()

			var changeCheckHelperMockPtrValue change_check.ChangeCheckHelper
			changeCheckHelperMock := &changeCheckHelperMockPtrValue
			mockey.Mock(change_check.NewChangeCheckHelper, mockey.OptUnsafe).Return(changeCheckHelperMock).Build()

			// prepare parameters
			var receiverPtrValue ChangeServiceImpl
			receiver := &receiverPtrValue
			ctx := context.Background()
			var configPtrValue dev.ContributionCodeChangePipelineCreationConfig
			config := &configPtrValue
			convey.So(func() { _ = receiver.TriggerChangeNewPipelineCheck(ctx, config) }, convey.ShouldNotPanic)
		})
	})

	// default condition
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var verifyContributionCodeChangePipelineCreationConfigRet1Mock error
			mockey.Mock(utils.VerifyContributionCodeChangePipelineCreationConfig).Return(verifyContributionCodeChangePipelineCreationConfigRet1Mock).Build()

			var changeCheckHelperMockPtrValue change_check.ChangeCheckHelper
			changeCheckHelperMock := &changeCheckHelperMockPtrValue
			mockey.Mock(change_check.NewChangeCheckHelper, mockey.OptUnsafe).Return(changeCheckHelperMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(change_entity.GetCodeChange).Return(rMock).Build()

			var getContributionIDRet1Mock int64
			mockey.Mock((*dev.ContributionCodeChangePipelineCreationConfig).GetContributionID, mockey.OptUnsafe).Return(getContributionIDRet1Mock).Build()

			var triggerNewPipelineCheckRet1Mock gresult.R[int]
			mockey.Mock((*change_check.ChangeCheckHelper).TriggerNewPipelineCheck).Return(triggerNewPipelineCheckRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue ChangeServiceImpl
			receiver := &receiverPtrValue
			ctx := context.Background()
			var configPtrValue dev.ContributionCodeChangePipelineCreationConfig
			config := &configPtrValue
			convey.So(func() { _ = receiver.TriggerChangeNewPipelineCheck(ctx, config) }, convey.ShouldPanic)
		})
	})

}

func TestChangeServiceImpl_GetChangePermissionAutoGen(t *testing.T) {
	// Verify the panic situation when getting change permission.
	t.Run("testChangeServiceImpl_GetChangePermission_Panic", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getUserAuthorizationRoleForDevBasicRet1Mock gresult.R[int]
			mockey.Mock(user.GetUserAuthorizationRoleForDevBasic).Return(getUserAuthorizationRoleForDevBasicRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(GetChangeDetail).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var contributionID int64
			var username string
			var receiverPtrValue ChangeServiceImpl
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.GetChangePermission(ctx, contributionID, username) }, convey.ShouldPanic)
		})
	})

}

func TestChangeServiceImpl_InviteUserIntoChangeLarkGroupAutoGen(t *testing.T) {
	// Verify the behavior when there is a database error during the invitation process.
	t.Run("testChangeServiceImpl_InviteUserIntoChangeLarkGroup_DBError", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var inviteUserIntoLarkGroupRet1Mock error
			mockey.Mock(meta.InviteUserIntoLarkGroup).Return(inviteUserIntoLarkGroupRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetContributionCodeChangeByID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			// prepare parameters
			var receiverPtrValue ChangeServiceImpl
			receiver := &receiverPtrValue
			ctx := context.Background()
			var username string
			var cid int64
			convey.So(func() { _ = receiver.InviteUserIntoChangeLarkGroup(ctx, username, cid) }, convey.ShouldPanic)
		})
	})

}

func TestChangeServiceImpl_GetChangeLatestPipelineCheckInfoAutoGen(t *testing.T) {
	// Verify the behavior of GetChangeLatestPipelineCheckInfo function under default condition.
	t.Run("testChangeServiceImpl_GetChangeLatestPipelineCheckInfoAutoGen", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(change_check.GetLatestPipelineCheckInfoByType).Return(rMock).Build()

			// prepare parameters
			var receiverPtrValue ChangeServiceImpl
			receiver := &receiverPtrValue
			ctx := context.Background()
			var cid int64
			var pipelineType dev.ContributionPipelineType
			convey.So(func() { _ = receiver.GetChangeLatestPipelineCheckInfo(ctx, cid, pipelineType) }, convey.ShouldPanic)
		})
	})

}

func TestChangeServiceImpl_RemoveUserFromChangeLarkGroupAutoGen(t *testing.T) {
	// Verify the error handling when there are failures in related functions.
	t.Run("testChangeServiceImpl_RemoveUserFromChangeLarkGroup_Failures", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var removeUserFromLarkGroupRet1Mock error
			mockey.Mock(meta.RemoveUserFromLarkGroup).Return(removeUserFromLarkGroupRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetContributionCodeChangeByID).Return(rMock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			// prepare parameters
			var receiverPtrValue ChangeServiceImpl
			receiver := &receiverPtrValue
			ctx := context.Background()
			var username string
			var cid int64
			convey.So(func() { _ = receiver.RemoveUserFromChangeLarkGroup(ctx, username, cid) }, convey.ShouldPanic)
		})
	})

}

