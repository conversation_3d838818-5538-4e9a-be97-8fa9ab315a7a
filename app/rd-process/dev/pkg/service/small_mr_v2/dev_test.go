package small_mr_v2

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	optimus2 "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/lang/gg/gresult"
)

func TestGetDevWorkflowStageStatusAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(GetDevDetail).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			var stageType dev.WorkflowStageType
			convey.So(func() { _ = GetDevWorkflowStageStatus(ctx, devBasicID, stageType) }, convey.ShouldPanic)
		})
	})

}

func TestGetMergeTaskOptimusMrInfoAutoGen(t *testing.T) {
	// Verify the function behavior when GetMergeTaskOptimusMrInDevBasic returns 0.
	t.Run("testGetMergeTaskOptimusMrInfo_GetMrInDevBasicZero", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getDevBasicProjectIDRet1Mock int64
			mockey.Mock(GetDevBasicProjectID).Return(getDevBasicProjectIDRet1Mock).Build()

			var optimusClientMockValueImpl optimusserviceClientImplForTestAutoGen
			optimusClientMockValue := &optimusClientMockValueImpl
			optimusClientMock := optimusClientMockValue
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			getMergeTaskOptimusMrInDevBasicRet1Mock := int64(0)
			mockey.Mock(GetMergeTaskOptimusMrInDevBasic).Return(getMergeTaskOptimusMrInDevBasicRet1Mock).Build()

			var getMrMainInfoResponseMockPtrValue optimus.GetMrMainInfoResponse
			getMrMainInfoResponseMock := &getMrMainInfoResponseMockPtrValue
			var getMainMrInfoRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetMainMrInfo"), mockey.OptUnsafe).Return(getMrMainInfoResponseMock, getMainMrInfoRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64

			// run target function and assert
			got1 := GetMergeTaskOptimusMrInfo(ctx, devBasicID)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).MrID, convey.ShouldEqual, 0)
			convey.So((*got1).ProjectID, convey.ShouldEqual, 0)
			convey.So((*got1).Iid, convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when GetMergeTaskOptimusMrInDevBasic returns a non-zero value.
	t.Run("testGetMergeTaskOptimusMrInfo_GetMrInDevBasicNonZero", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			getMergeTaskOptimusMrInDevBasicRet1Mock := int64(1)
			mockey.Mock(GetMergeTaskOptimusMrInDevBasic).Return(getMergeTaskOptimusMrInDevBasicRet1Mock).Build()

			var getDevBasicProjectIDRet1Mock int64
			mockey.Mock(GetDevBasicProjectID).Return(getDevBasicProjectIDRet1Mock).Build()

			var optimusClientMockImpl optimusserviceClientImplForTestAutoGen
			optimusClientMock := &optimusClientMockImpl
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			var getMrMainInfoResponseMockPtrValue optimus.GetMrMainInfoResponse
			getMrMainInfoResponseMock := &getMrMainInfoResponseMockPtrValue
			var getMainMrInfoRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetMainMrInfo"), mockey.OptUnsafe).Return(getMrMainInfoResponseMock, getMainMrInfoRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64

			// run target function and assert
			got1 := GetMergeTaskOptimusMrInfo(ctx, devBasicID)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).MrID, convey.ShouldEqual, 1)
			convey.So((*got1).ProjectID, convey.ShouldEqual, 0)
			convey.So((*got1).Iid, convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when GetMainMrInfo returns an error.
	t.Run("testGetMergeTaskOptimusMrInfo_GetMainMrInfoError", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var optimusClientMockImpl optimusserviceClientImplForTestAutoGen
			optimusClientMock := &optimusClientMockImpl
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			getMergeTaskOptimusMrInDevBasicRet1Mock := int64(1)
			mockey.Mock(GetMergeTaskOptimusMrInDevBasic).Return(getMergeTaskOptimusMrInDevBasicRet1Mock).Build()

			var getDevBasicProjectIDRet1Mock int64
			mockey.Mock(GetDevBasicProjectID).Return(getDevBasicProjectIDRet1Mock).Build()

			var getMrMainInfoResponseMockPtrValue optimus.GetMrMainInfoResponse
			getMrMainInfoResponseMock := &getMrMainInfoResponseMockPtrValue
			getMainMrInfoRet2Mock := fmt.Errorf("error")
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetMainMrInfo"), mockey.OptUnsafe).Return(getMrMainInfoResponseMock, getMainMrInfoRet2Mock).Build()

			// prepare parameters
			var devBasicID int64
			ctx := context.Background()

			// run target function and assert
			got1 := GetMergeTaskOptimusMrInfo(ctx, devBasicID)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).MrID, convey.ShouldEqual, 1)
			convey.So((*got1).ProjectID, convey.ShouldEqual, 0)
			convey.So((*got1).Iid, convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when GetMainMrInfo returns successfully.
	t.Run("testGetMergeTaskOptimusMrInfo_GetMainMrInfoSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			getMergeTaskOptimusMrInDevBasicRet1Mock := int64(1)
			mockey.Mock(GetMergeTaskOptimusMrInDevBasic).Return(getMergeTaskOptimusMrInDevBasicRet1Mock).Build()

			var getDevBasicProjectIDRet1Mock int64
			mockey.Mock(GetDevBasicProjectID).Return(getDevBasicProjectIDRet1Mock).Build()

			var optimusClientMockImpl optimusserviceClientImplForTestAutoGen
			optimusClientMock := &optimusClientMockImpl
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			var getMrMainInfoResponseMockPtrValue optimus.GetMrMainInfoResponse
			getMrMainInfoResponseMock := &getMrMainInfoResponseMockPtrValue
			var getMrMainInfoResponseMockInfoPtrValue optimus.MrMainInfo
			getMrMainInfoResponseMock.Info = &getMrMainInfoResponseMockInfoPtrValue
			var getMainMrInfoRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetMainMrInfo"), mockey.OptUnsafe).Return(getMrMainInfoResponseMock, getMainMrInfoRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64

			// run target function and assert
			got1 := GetMergeTaskOptimusMrInfo(ctx, devBasicID)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).MrID, convey.ShouldEqual, 1)
			convey.So((*got1).ProjectID, convey.ShouldEqual, 0)
			convey.So((*got1).Iid, convey.ShouldEqual, 0)
		})
	})

}

func TestMergeTargetAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*biz_config.DevBizConfigHandler).GetConfig).Return(rMock).Build()

			var devBizConfigHandlerMockPtrValue biz_config.DevBizConfigHandler
			devBizConfigHandlerMock := &devBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewDevBizConfigHandler).Return(devBizConfigHandlerMock).Build()

			var mergeBranchRet1Mock gresult.R[int]
			mockey.Mock(gitsvr.MergeBranch).Return(mergeBranchRet1Mock).Build()

			var getDevDetailRet1Mock gresult.R[int]
			mockey.Mock(GetDevDetail).Return(getDevDetailRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			var username string
			convey.So(func() { _ = MergeTarget(ctx, devBasicID, username) }, convey.ShouldPanic)
		})
	})

}

func TestGetDevSmrConfigAutoGen(t *testing.T) {
	// Verify the behavior when Repo UseCache fails.
	t.Run("testGetDevSmrConfig_RepoUseCacheFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var mergeTaskModeMock dev.MergeTaskMode
			var mergeTaskModeFromStringRet2Mock error
			mockey.Mock(dev.MergeTaskModeFromString).Return(mergeTaskModeMock, mergeTaskModeFromStringRet2Mock).Build()

			var processModeMock dev.ProcessMode
			var processModeFromStringRet2Mock error
			mockey.Mock(dev.ProcessModeFromString).Return(processModeMock, processModeFromStringRet2Mock).Build()

			var devBizConfigHandlerMockPtrValue biz_config.DevBizConfigHandler
			devBizConfigHandlerMock := &devBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewDevBizConfigHandler).Return(devBizConfigHandlerMock).Build()

			var mergeTaskTypeMock dev.MergeTaskType
			var mergeTaskTypeFromStringRet2Mock error
			mockey.Mock(dev.MergeTaskTypeFromString).Return(mergeTaskTypeMock, mergeTaskTypeFromStringRet2Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var getDevSmrConfigByDevBasicIDRet1Mock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(getDevSmrConfigByDevBasicIDRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*biz_config.DevBizConfigHandler).GetConfig).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicId int64
			convey.So(func() { _ = GetDevSmrConfig(ctx, devBasicId) }, convey.ShouldPanic)
		})
	})

}

func TestGetDevDetailWithOptAutoGen(t *testing.T) {
	// param3 == 2
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			optionAlias := 2
			option := consts.GetDBDataOption(optionAlias)
			convey.So(func() { _ = GetDevDetailWithOpt(ctx, devBasicID, option) }, convey.ShouldPanic)
		})
	})

	// param3 != 2
	// param3 == 1
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			optionAlias := 1
			option := consts.GetDBDataOption(optionAlias)
			convey.So(func() { _ = GetDevDetailWithOpt(ctx, devBasicID, option) }, convey.ShouldPanic)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:UseCache()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:GetDevBasicInfoByID()_ret-2 != nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			getDevBasicInfoByIDRet2Mock := fmt.Errorf("error")
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			// prepare parameters
			var devBasicID int64
			var option consts.GetDBDataOption
			ctx := context.Background()
			convey.So(func() { _ = GetDevDetailWithOpt(ctx, devBasicID, option) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetMergeTaskOptimusMrInDevBasicAutoGen(t *testing.T) {
	// Verify the behavior when the Repo operation fails.
	t.Run("testGetMergeTaskOptimusMrInDevBasic_RepoFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var devBizConfigHandlerMockPtrValue biz_config.DevBizConfigHandler
			devBizConfigHandlerMock := &devBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewDevBizConfigHandler).Return(devBizConfigHandlerMock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var getMergeTaskOptimusMrIdRet1Mock int64
			mockey.Mock((*biz_config.DevBizConfigHandler).GetMergeTaskOptimusMrId).Return(getMergeTaskOptimusMrIdRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			convey.So(func() { _ = GetMergeTaskOptimusMrInDevBasic(ctx, devBasicID) }, convey.ShouldPanic)
		})
	})

}

func TestSyncOptimusMrStateToDevSmrAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(GetMergeTaskOptimusMrByProjectIDAndIid).Return(rMock).Build()

			// prepare parameters
			var projectID int64
			var iid int64
			var mrState string
			ctx := context.Background()
			convey.So(func() { _ = SyncOptimusMrStateToDevSmr(ctx, projectID, iid, mrState) }, convey.ShouldPanic)
		})
	})

}

func TestGetDevSmrListAutoGen(t *testing.T) {
	// Verify the function behavior when UserRole is nil, username length is not zero and GetDevBasicInfoByFilterOptions return length is zero.
	t.Run("testGetDevSmrList_UserRoleNil_UserNameLenNotZero_GetDevBasicInfoByFilterOptionsRetLenZero", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.DevSmrInfo).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			var getUserRoleRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserRole, mockey.OptUnsafe).Return(getUserRoleRet1Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var getLastIDRet1Mock int64
			mockey.Mock((*dev.DevSmrSearchOptions).GetLastID, mockey.OptUnsafe).Return(getLastIDRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var getGroupNameRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetGroupName, mockey.OptUnsafe).Return(getGroupNameRet1Mock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(rMock).Build()

			var getLimitRet1Mock int32
			mockey.Mock((*dev.DevSmrSearchOptions).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			getUserNameRet1Mock := "a"
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserName, mockey.OptUnsafe).Return(getUserNameRet1Mock).Build()

			getDevBasicInfoByFilterOptionsRet1Mock := []*model.DevBasicInfo{}
			var getDevBasicInfoByFilterOptionsRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByFilterOptions).Return(getDevBasicInfoByFilterOptionsRet1Mock, getDevBasicInfoByFilterOptionsRet2Mock).Build()

			var getDevSmrListItemRet1Mock gresult.R[int]
			mockey.Mock(getDevSmrListItem).Return(getDevSmrListItemRet1Mock).Build()

			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock []*model.DevBasicInfo
			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock error
			mockey.Mock((*data.Repo).BatchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDev).Return(batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock, batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var optionsPtrValue dev.DevSmrSearchOptions
			options := &optionsPtrValue
			convey.So(func() { _ = GetDevSmrList(ctx, options) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// Verify the function behavior when UserRole is not nil and username length is zero.
	t.Run("testGetDevSmrList_UserRoleNotNil_UserNameLenZero", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var getDevSmrListItemRet1Mock gresult.R[int]
			mockey.Mock(getDevSmrListItem).Return(getDevSmrListItemRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(rMock).Build()

			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock []*model.DevBasicInfo
			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock error
			mockey.Mock((*data.Repo).BatchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDev).Return(batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock, batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock).Build()

			var getLimitRet1Mock int32
			mockey.Mock((*dev.DevSmrSearchOptions).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			var getGroupNameRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetGroupName, mockey.OptUnsafe).Return(getGroupNameRet1Mock).Build()

			var getLastIDRet1Mock int64
			mockey.Mock((*dev.DevSmrSearchOptions).GetLastID, mockey.OptUnsafe).Return(getLastIDRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.DevSmrInfo).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			getUserNameRet1Mock := ""
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserName, mockey.OptUnsafe).Return(getUserNameRet1Mock).Build()

			var getUserRoleRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserRole, mockey.OptUnsafe).Return(getUserRoleRet1Mock).Build()

			var getDevBasicInfoByFilterOptionsRet1Mock []*model.DevBasicInfo
			var getDevBasicInfoByFilterOptionsRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByFilterOptions).Return(getDevBasicInfoByFilterOptionsRet1Mock, getDevBasicInfoByFilterOptionsRet2Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			// prepare parameters
			ctx := context.Background()
			var optionsPtrValue dev.DevSmrSearchOptions
			options := &optionsPtrValue
			var optionsUserRolePtrValue string
			options.UserRole = &optionsUserRolePtrValue
			convey.So(func() { _ = GetDevSmrList(ctx, options) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// Verify the function behavior when UserRole is nil, username length is not zero and LastID is not zero.
	t.Run("testGetDevSmrList_UserRoleNil_UserNameLenNotZero_LastIDNotZero", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			getUserNameRet1Mock := "a"
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserName, mockey.OptUnsafe).Return(getUserNameRet1Mock).Build()

			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock []*model.DevBasicInfo
			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock error
			mockey.Mock((*data.Repo).BatchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDev).Return(batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock, batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock).Build()

			var getLimitRet1Mock int32
			mockey.Mock((*dev.DevSmrSearchOptions).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(rMock).Build()

			var getUserRoleRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserRole, mockey.OptUnsafe).Return(getUserRoleRet1Mock).Build()

			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.DevSmrInfo).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var getDevSmrListItemRet1Mock gresult.R[int]
			mockey.Mock(getDevSmrListItem).Return(getDevSmrListItemRet1Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var getGroupNameRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetGroupName, mockey.OptUnsafe).Return(getGroupNameRet1Mock).Build()

			var getLastIDRet1Mock int64
			mockey.Mock((*dev.DevSmrSearchOptions).GetLastID, mockey.OptUnsafe).Return(getLastIDRet1Mock).Build()

			var getDevBasicInfoByFilterOptionsRet1Mock []*model.DevBasicInfo
			var getDevBasicInfoByFilterOptionsRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByFilterOptions).Return(getDevBasicInfoByFilterOptionsRet1Mock, getDevBasicInfoByFilterOptionsRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var optionsPtrValue dev.DevSmrSearchOptions
			options := &optionsPtrValue
			options.LastID = int64(1)
			convey.So(func() { _ = GetDevSmrList(ctx, options) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// Verify the function behavior when UserRole is nil, username length is not zero and GroupName is not nil.
	t.Run("testGetDevSmrList_UserRoleNil_UserNameLenNotZero_GroupNameNotNil", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock []*model.DevBasicInfo
			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock error
			mockey.Mock((*data.Repo).BatchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDev).Return(batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock, batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock).Build()

			var getLimitRet1Mock int32
			mockey.Mock((*dev.DevSmrSearchOptions).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var getDevBasicInfoByFilterOptionsRet1Mock []*model.DevBasicInfo
			var getDevBasicInfoByFilterOptionsRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByFilterOptions).Return(getDevBasicInfoByFilterOptionsRet1Mock, getDevBasicInfoByFilterOptionsRet2Mock).Build()

			getUserNameRet1Mock := "a"
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserName, mockey.OptUnsafe).Return(getUserNameRet1Mock).Build()

			var getDevSmrListItemRet1Mock gresult.R[int]
			mockey.Mock(getDevSmrListItem).Return(getDevSmrListItemRet1Mock).Build()

			var getGroupNameRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetGroupName, mockey.OptUnsafe).Return(getGroupNameRet1Mock).Build()

			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.DevSmrInfo).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			var getLastIDRet1Mock int64
			mockey.Mock((*dev.DevSmrSearchOptions).GetLastID, mockey.OptUnsafe).Return(getLastIDRet1Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(rMock).Build()

			var getUserRoleRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserRole, mockey.OptUnsafe).Return(getUserRoleRet1Mock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var optionsPtrValue dev.DevSmrSearchOptions
			options := &optionsPtrValue
			var optionsGroupNamePtrValue string
			options.GroupName = &optionsGroupNamePtrValue
			convey.So(func() { _ = GetDevSmrList(ctx, options) }, convey.ShouldPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// Verify the function behavior when UserRole is nil, username length is not zero, Limit is not nil and GetLimit is greater than 100.
	t.Run("testGetDevSmrList_UserRoleNil_UserNameLenNotZero_LimitNotNil_GetLimitGreaterThan100", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var getLastIDRet1Mock int64
			mockey.Mock((*dev.DevSmrSearchOptions).GetLastID, mockey.OptUnsafe).Return(getLastIDRet1Mock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var getUserRoleRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserRole, mockey.OptUnsafe).Return(getUserRoleRet1Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var getDevBasicInfoByFilterOptionsRet1Mock []*model.DevBasicInfo
			var getDevBasicInfoByFilterOptionsRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByFilterOptions).Return(getDevBasicInfoByFilterOptionsRet1Mock, getDevBasicInfoByFilterOptionsRet2Mock).Build()

			var getDevSmrListItemRet1Mock gresult.R[int]
			mockey.Mock(getDevSmrListItem).Return(getDevSmrListItemRet1Mock).Build()

			var getGroupNameRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetGroupName, mockey.OptUnsafe).Return(getGroupNameRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			getLimitRet1Mock := int32(101)
			mockey.Mock((*dev.DevSmrSearchOptions).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(rMock).Build()

			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock []*model.DevBasicInfo
			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock error
			mockey.Mock((*data.Repo).BatchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDev).Return(batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock, batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock).Build()

			getUserNameRet1Mock := "a"
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserName, mockey.OptUnsafe).Return(getUserNameRet1Mock).Build()

			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.DevSmrInfo).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var optionsPtrValue dev.DevSmrSearchOptions
			options := &optionsPtrValue
			var optionsLimitPtrValue int32
			options.Limit = &optionsLimitPtrValue
			convey.So(func() { _ = GetDevSmrList(ctx, options) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// Verify the function behavior when UserRole is nil, username length is not zero, Limit is not nil and GetLimit is less than or equal to 100.
	t.Run("testGetDevSmrList_UserRoleNil_UserNameLenNotZero_LimitNotNil_GetLimitLessThanOrEqualTo100", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// mock function returns or global values
			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.DevSmrInfo).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			var getGroupNameRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetGroupName, mockey.OptUnsafe).Return(getGroupNameRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var getUserRoleRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserRole, mockey.OptUnsafe).Return(getUserRoleRet1Mock).Build()

			getUserNameRet1Mock := "a"
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserName, mockey.OptUnsafe).Return(getUserNameRet1Mock).Build()

			getLimitRet1Mock := int32(100)
			mockey.Mock((*dev.DevSmrSearchOptions).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			var getDevBasicInfoByFilterOptionsRet1Mock []*model.DevBasicInfo
			var getDevBasicInfoByFilterOptionsRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByFilterOptions).Return(getDevBasicInfoByFilterOptionsRet1Mock, getDevBasicInfoByFilterOptionsRet2Mock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock []*model.DevBasicInfo
			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock error
			mockey.Mock((*data.Repo).BatchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDev).Return(batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock, batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock).Build()

			var getLastIDRet1Mock int64
			mockey.Mock((*dev.DevSmrSearchOptions).GetLastID, mockey.OptUnsafe).Return(getLastIDRet1Mock).Build()

			var getDevSmrListItemRet1Mock gresult.R[int]
			mockey.Mock(getDevSmrListItem).Return(getDevSmrListItemRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var optionsPtrValue dev.DevSmrSearchOptions
			options := &optionsPtrValue
			var optionsLimitPtrValue int32
			options.Limit = &optionsLimitPtrValue
			convey.So(func() { _ = GetDevSmrList(ctx, options) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

	// Verify the function behavior when UserRole is nil, username length is not zero and GetDevBasicInfoByFilterOptions return error is not nil.
	t.Run("testGetDevSmrList_UserRoleNil_UserNameLenNotZero_GetDevBasicInfoByFilterOptionsRetErrNotNil", func(t *testing.T) {
		mockey.PatchConvey("case_6", t, func() {
			// mock function returns or global values
			var getLimitRet1Mock int32
			mockey.Mock((*dev.DevSmrSearchOptions).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			var getGroupNameRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetGroupName, mockey.OptUnsafe).Return(getGroupNameRet1Mock).Build()

			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock []*model.DevBasicInfo
			var batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock error
			mockey.Mock((*data.Repo).BatchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDev).Return(batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet1Mock, batchGetDevBasicByCollaboratorNameAndFilterOptionsAndLastDevIDLimitDevRet2Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.DevSmrInfo).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(rMock).Build()

			var getDevBasicInfoByFilterOptionsRet1Mock []*model.DevBasicInfo
			getDevBasicInfoByFilterOptionsRet2Mock := fmt.Errorf("error")
			mockey.Mock((*data.Repo).GetDevBasicInfoByFilterOptions).Return(getDevBasicInfoByFilterOptionsRet1Mock, getDevBasicInfoByFilterOptionsRet2Mock).Build()

			var getUserRoleRet1Mock string
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserRole, mockey.OptUnsafe).Return(getUserRoleRet1Mock).Build()

			var getLastIDRet1Mock int64
			mockey.Mock((*dev.DevSmrSearchOptions).GetLastID, mockey.OptUnsafe).Return(getLastIDRet1Mock).Build()

			var getDevSmrListItemRet1Mock gresult.R[int]
			mockey.Mock(getDevSmrListItem).Return(getDevSmrListItemRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			getUserNameRet1Mock := "a"
			mockey.Mock((*dev.DevSmrSearchOptions).GetUserName, mockey.OptUnsafe).Return(getUserNameRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var optionsPtrValue dev.DevSmrSearchOptions
			options := &optionsPtrValue
			convey.So(func() { _ = GetDevSmrList(ctx, options) }, convey.ShouldNotPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

}

func TestRetryDevSmrWorkflowFailedTasksAutoGen(t *testing.T) {
	// Verify the behavior when there is a database error.
	t.Run("testRetryDevSmrWorkflowFailedTasks_DBError", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var optimusClientMockValueImpl optimusserviceClientImplForTestAutoGen
			optimusClientMockValue := &optimusClientMockValueImpl
			optimusClientMock := optimusClientMockValue
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var getMergeTaskOptimusMrInDevBasicRet1Mock int64
			mockey.Mock(GetMergeTaskOptimusMrInDevBasic).Return(getMergeTaskOptimusMrInDevBasicRet1Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(rMock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			getDevBasicInfoByIDRet2Mock := fmt.Errorf("error")
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var rerunMrWorkflowRet1Mock error
			mockey.Mock(optimus2.RerunMrWorkflow).Return(rerunMrWorkflowRet1Mock).Build()

			var getMrMainInfoResponseMockPtrValue optimus.GetMrMainInfoResponse
			getMrMainInfoResponseMock := &getMrMainInfoResponseMockPtrValue
			var getMainMrInfoRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetMainMrInfo"), mockey.OptUnsafe).Return(getMrMainInfoResponseMock, getMainMrInfoRet2Mock).Build()

			// prepare parameters
			var username string
			ctx := context.Background()
			var devBasicID int64

			// run target function and assert
			got1 := RetryDevSmrWorkflowFailedTasks(ctx, devBasicID, username)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify that the function panics when an error occurs.
	t.Run("testRetryDevSmrWorkflowFailedTasks_PanicOnError", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var rerunMrWorkflowRet1Mock error
			mockey.Mock(optimus2.RerunMrWorkflow).Return(rerunMrWorkflowRet1Mock).Build()

			var optimusClientMockValueImpl optimusserviceClientImplForTestAutoGen
			optimusClientMockValue := &optimusClientMockValueImpl
			optimusClientMock := optimusClientMockValue
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var getMergeTaskOptimusMrInDevBasicRet1Mock int64
			mockey.Mock(GetMergeTaskOptimusMrInDevBasic).Return(getMergeTaskOptimusMrInDevBasicRet1Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(rMock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var getMrMainInfoResponseMockPtrValue optimus.GetMrMainInfoResponse
			getMrMainInfoResponseMock := &getMrMainInfoResponseMockPtrValue
			var getMainMrInfoRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetMainMrInfo"), mockey.OptUnsafe).Return(getMrMainInfoResponseMock, getMainMrInfoRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			var username string
			convey.So(func() { _ = RetryDevSmrWorkflowFailedTasks(ctx, devBasicID, username) }, convey.ShouldPanic)
		})
	})

}

func TestUpdateDevSmrInfoAutoGen(t *testing.T) {
	// Verify the error handling when updating DevSmrInfo fails.
	t.Run("testUpdateDevSmrInfo_UpdateFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			mockey.Mock(adaptIdlDevSmrInfoUpdateOptionsToEntityDevBasicInfo).Return(devBasicInfoMock).Build()

			updateDevBasicInfoByIDRet1Mock := fmt.Errorf("error")
			mockey.Mock((*data.Repo).UpdateDevBasicInfoByID).Return(updateDevBasicInfoByIDRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			// prepare parameters
			var optionsPtrValue dev.DevSmrInfoUpdateOptions
			options := &optionsPtrValue
			var username *string
			ctx := context.Background()
			var devBasicID int64

			// run target function and assert
			got1 := UpdateDevSmrInfo(ctx, devBasicID, options, username)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// Verify the successful update of DevSmrInfo.
	t.Run("testUpdateDevSmrInfo_UpdateSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			mockey.Mock(adaptIdlDevSmrInfoUpdateOptionsToEntityDevBasicInfo).Return(devBasicInfoMock).Build()

			var updateDevBasicInfoByIDRet1Mock error
			mockey.Mock((*data.Repo).UpdateDevBasicInfoByID).Return(updateDevBasicInfoByIDRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			var optionsPtrValue dev.DevSmrInfoUpdateOptions
			options := &optionsPtrValue
			var username *string

			// run target function and assert
			got1 := UpdateDevSmrInfo(ctx, devBasicID, options, username)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func Test_getDevSmrListItemAutoGen(t *testing.T) {
	// Verify the function behavior when parameters are in a certain state.
	t.Run("testGetDevSmrListItem_ParamValidation", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var formDevTaskUrlRet1Mock string
			mockey.Mock(functools.FormDevTaskUrl, mockey.OptUnsafe).Return(formDevTaskUrlRet1Mock).Build()

			var getAppSimpleInfoByGroupNameRet1Mock gresult.R[int]
			mockey.Mock(meta.GetAppSimpleInfoByGroupName).Return(getAppSimpleInfoByGroupNameRet1Mock).Build()

			var getDevSmrConfigByDevBasicIDRet1Mock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(getDevSmrConfigByDevBasicIDRet1Mock).Build()

			var setUseCacheRet1MockPtrValue StageGatekeeper
			setUseCacheRet1Mock := &setUseCacheRet1MockPtrValue
			mockey.Mock((*StageGatekeeper).SetUseCache, mockey.OptUnsafe).Return(setUseCacheRet1Mock).Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			var optimusClientMockImpl optimusserviceClientImplForTestAutoGen
			optimusClientMock := &optimusClientMockImpl
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			var devSmrTypeMock dev.DevSmrType
			var devSmrTypeFromStringRet2Mock error
			mockey.Mock(dev.DevSmrTypeFromString).Return(devSmrTypeMock, devSmrTypeFromStringRet2Mock).Build()

			var processModeMock dev.ProcessMode
			var processModeFromStringRet2Mock error
			mockey.Mock(dev.ProcessModeFromString).Return(processModeMock, processModeFromStringRet2Mock).Build()

			var stageGatekeeperMockPtrValue StageGatekeeper
			stageGatekeeperMock := &stageGatekeeperMockPtrValue
			mockey.Mock((*DevDetail).GetDevTaskStageGatekeeper, mockey.OptUnsafe).Return(stageGatekeeperMock).Build()

			var devSmrCheckStatusMock dev.DevSmrCheckStatus
			mockey.Mock(adaptDevWorkflowStageToCheckStatus).Return(devSmrCheckStatusMock).Build()

			var getChangesCountRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).GetChangesCount).Return(getChangesCountRet1Mock).Build()

			var getDevCollaboratorNamesRet1Mock []string
			var getDevCollaboratorNamesRet2Mock error
			mockey.Mock((*data.Repo).GetDevCollaboratorNames).Return(getDevCollaboratorNamesRet1Mock, getDevCollaboratorNamesRet2Mock).Build()

			var getDevMergeTaskInfoRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).GetDevMergeTaskInfo).Return(getDevMergeTaskInfoRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*StageGatekeeper).GetStatus).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var getDevTagsResponseMockPtrValue optimus.GetDevTagsResponse
			getDevTagsResponseMock := &getDevTagsResponseMockPtrValue
			var getDevTagsRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetDevTags"), mockey.OptUnsafe).Return(getDevTagsResponseMock, getDevTagsRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicPtrValue model.DevBasicInfo
			devBasic := &devBasicPtrValue
			devBasic.SpaceId = int64(0)
			spaceID := int64(1)
			var dataParamsPtrValue DevTaskListItemDataParams
			dataParams := &dataParamsPtrValue
			convey.So(func() { _ = getDevSmrListItem(ctx, devBasic, spaceID, dataParams) }, convey.ShouldPanic)
		})
	})

}

func TestGetDevSmrOptimusMrCanMergeAutoGen(t *testing.T) {
	// Verify the function behavior with valid input parameters.
	t.Run("testGetDevSmrOptimusMrCanMerge_ValidParams", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var messageRet1MockPtrValue MergeTaskCanMergeResult
			messageRet1Mock := &messageRet1MockPtrValue
			mockey.Mock((*MergeTaskCanMergeResult).Message, mockey.OptUnsafe).Return(messageRet1Mock).Build()

			var mergeTaskCanMergeResultMockPtrValue MergeTaskCanMergeResult
			mergeTaskCanMergeResultMock := &mergeTaskCanMergeResultMockPtrValue
			mockey.Mock((*MergeTaskCanMergeResult).Failed, mockey.OptUnsafe).Return(mergeTaskCanMergeResultMock).Build()

			var newCanMergeResultRet1MockPtrValue MergeTaskCanMergeResult
			newCanMergeResultRet1Mock := &newCanMergeResultRet1MockPtrValue
			mockey.Mock(NewCanMergeResult, mockey.OptUnsafe).Return(newCanMergeResultRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(GetMergeTaskOptimusMrByProjectIDAndIid).Return(rMock).Build()

			var showBlockErrorRet1MockPtrValue MergeTaskCanMergeResult
			showBlockErrorRet1Mock := &showBlockErrorRet1MockPtrValue
			mockey.Mock((*MergeTaskCanMergeResult).ShowBlockError, mockey.OptUnsafe).Return(showBlockErrorRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			bizID := int64(1)
			projectID := int64(1)
			iid := int64(1)
			convey.So(func() { _ = GetDevSmrOptimusMrCanMerge(ctx, bizID, projectID, iid) }, convey.ShouldPanic)
		})
	})

	// Verify the function behavior with invalid or missing input parameters.
	t.Run("testGetDevSmrOptimusMrCanMerge_InvalidParams", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var messageRet1MockPtrValue MergeTaskCanMergeResult
			messageRet1Mock := &messageRet1MockPtrValue
			mockey.Mock((*MergeTaskCanMergeResult).Message, mockey.OptUnsafe).Return(messageRet1Mock).Build()

			var mergeTaskCanMergeResultMockPtrValue MergeTaskCanMergeResult
			mergeTaskCanMergeResultMock := &mergeTaskCanMergeResultMockPtrValue
			mockey.Mock((*MergeTaskCanMergeResult).Failed, mockey.OptUnsafe).Return(mergeTaskCanMergeResultMock).Build()

			var newCanMergeResultRet1MockPtrValue MergeTaskCanMergeResult
			newCanMergeResultRet1Mock := &newCanMergeResultRet1MockPtrValue
			mockey.Mock(NewCanMergeResult, mockey.OptUnsafe).Return(newCanMergeResultRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(GetMergeTaskOptimusMrByProjectIDAndIid).Return(rMock).Build()

			var showBlockErrorRet1MockPtrValue MergeTaskCanMergeResult
			showBlockErrorRet1Mock := &showBlockErrorRet1MockPtrValue
			mockey.Mock((*MergeTaskCanMergeResult).ShowBlockError, mockey.OptUnsafe).Return(showBlockErrorRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var bizID int64
			var projectID int64
			var iid int64

			// run target function and assert
			got1 := GetDevSmrOptimusMrCanMerge(ctx, bizID, projectID, iid)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).CanMerge, convey.ShouldEqual, false)
			convey.So((*got1).Msg, convey.ShouldBeBlank)
			convey.So((*got1).ShowBlockErrorEnabled, convey.ShouldEqual, false)
		})
	})

}

func TestGetDevIndexByDevIDAutoGen(t *testing.T) {
	// Verify the behavior when retrieving DevBasicID fails.
	t.Run("testGetDevIndexByDevID_DevBasicIDRetrievalFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevBasicIDByDevID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			// prepare parameters
			ctx := context.Background()
			var devID int64
			convey.So(func() { _ = GetDevIndexByDevID(ctx, devID) }, convey.ShouldPanic)
		})
	})

}

func TestGetDevSmrAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev:DevSmrTypeFromString()_ret-2 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var devTaskModeMock dev.DevTaskMode
			var devTaskModeFromStringRet2Mock error
			mockey.Mock(dev.DevTaskModeFromString).Return(devTaskModeMock, devTaskModeFromStringRet2Mock).Build()

			var getDevBasicUrlRet1Mock string
			mockey.Mock(utils.GetDevBasicUrl).Return(getDevBasicUrlRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(GetDevDetail).Return(rMock).Build()

			var devSmrTypeMock dev.DevSmrType
			var devSmrTypeFromStringRet2Mock error
			mockey.Mock(dev.DevSmrTypeFromString).Return(devSmrTypeMock, devSmrTypeFromStringRet2Mock).Build()

			var processModeMock dev.ProcessMode
			var processModeFromStringRet2Mock error
			mockey.Mock(dev.ProcessModeFromString).Return(processModeMock, processModeFromStringRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			convey.So(func() { _ = GetDevSmr(ctx, devBasicID) }, convey.ShouldPanic)
		})
	})

}

func TestGetDevDetailAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:UseCache()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:GetDevBasicInfoByID()_ret-2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			getDevBasicInfoByIDRet2Mock := fmt.Errorf("error")
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			convey.So(func() { _ = GetDevDetail(ctx, devBasicID) }, convey.ShouldNotPanic)
		})
	})

	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:UseCache()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:GetDevBasicInfoByID()_ret-2 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(rMock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			convey.So(func() { _ = GetDevDetail(ctx, devBasicID) }, convey.ShouldPanic)
		})
	})

}
