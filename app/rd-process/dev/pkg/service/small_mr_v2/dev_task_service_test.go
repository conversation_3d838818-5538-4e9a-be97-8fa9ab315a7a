package small_mr_v2

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	metaService "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_task_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/qa_test"
	"code.byted.org/lang/gg/gresult"
	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestInviteUserIntoDevLarkGroup(t *testing.T) {
	PatchConvey("invite user into lark group", t, func() {
		PatchConvey("invite user", func() {
			Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(gresult.OK(&model.DevSmrConfig{Id: 111, BizConfig: "{\"lark_group_id\":\"1111\"}"})).Build()
			Mock(metaService.InviteUserIntoLarkGroupWithMemberType).Return(nil).Build()
			err := NewDevTaskServiceImpl().InviteUserIntoDevLarkGroup(context.TODO(), 1, "userX", "operatorX", nil)
			So(err, ShouldBeNil)
		})
		PatchConvey("invite user with no lark", func() {
			Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(gresult.OK(&model.DevSmrConfig{Id: 111, BizConfig: "{}"})).Build()
			Mock(metaService.InviteUserIntoLarkGroupWithMemberType).Return(nil).Build()
			err := NewDevTaskServiceImpl().InviteUserIntoDevLarkGroup(context.TODO(), 1, "userX", "operatorX", nil)
			So(err, ShouldNotBeNil)
		})
	})
}

func TestCreateDevLarkGroup(t *testing.T) {
	PatchConvey("create dev lark group", t, func() {
		PatchConvey("group existed", func() {
			Mock(GetDevDetail).Return(gresult.OK(&DevDetail{
				DevTask: &dev_task_entity.DevTask{
					BasicInfo: &model.DevBasicInfo{Id: 111, Title: "dev task 111"},
					Config:    &model.DevSmrConfig{Id: 111, BizConfig: "{\"lark_group_id\":\"1111\"}"},
				},
			})).Build()
			Mock(metaService.InviteUserIntoLarkGroup).Return(nil).Build()
			_, err := NewDevTaskServiceImpl().CreateDevLarkGroup(context.TODO(), 1, "userX").Get()
			So(err, ShouldBeNil)
		})
		PatchConvey("new group", func() {
			Mock(GetDevDetail).Return(gresult.OK(&DevDetail{
				DevTask: &dev_task_entity.DevTask{
					BasicInfo: &model.DevBasicInfo{Id: 111, Title: "dev task 111"},
					Config:    &model.DevSmrConfig{Id: 111, BizConfig: "{}"},
				},
			})).Build()
			Mock(GetSmrChangesByFilterOptions).Return(gresult.OK([]*ChangeDetail{})).Build()
			Mock((*DevDetail).GetQaTesters).Return(gresult.OK([]*qa_test.TesterInfo{})).Build()
			Mock((*DevDetail).CreateLarkGroupWithUsers).Return(gresult.OK("xxx")).Build()
			_, err := NewDevTaskServiceImpl().CreateDevLarkGroup(context.TODO(), 1, "userX").Get()
			So(err, ShouldBeNil)
		})
	})
}

func TestDevTaskServiceImpl_DismissDevLarkGroupAutoGen(t *testing.T) {
	// Verify the behavior of DismissDevLarkGroup function with mocked dependencies.
	t.Run("testDevTaskServiceImpl_DismissDevLarkGroup", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var devBizConfigHandlerMockPtrValue biz_config.DevBizConfigHandler
			devBizConfigHandlerMock := &devBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewDevBizConfigHandler).Return(devBizConfigHandlerMock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var dismissLarkGroupRet1Mock error
			mockey.Mock(metaService.DismissLarkGroup).Return(dismissLarkGroupRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetDevSmrConfigByDevBasicID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var getConfigRet1Mock gresult.R[int]
			mockey.Mock((*biz_config.DevBizConfigHandler).GetConfig).Return(getConfigRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var devBasicID int64
			var operator string
			var receiverPtrValue DevTaskServiceImpl
			receiver := &receiverPtrValue
			convey.So(func() { _ = receiver.DismissDevLarkGroup(ctx, devBasicID, operator) }, convey.ShouldPanic)
		})
	})

}
