package small_mr_v2

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/base"
	meta0 "code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/meta"
	"code.byted.org/kite/kitex/client/callopt"
)

type metaserviceClientImplForTestAutoGen struct{}

func (*metaserviceClientImplForTestAutoGen) SearchEmployee(param1 context.Context, param2 *meta0.SearchEmployeeRequest, param3 ...callopt.Option) (*meta0.SearchEmployeeResponse, error) {
	var ret1PtrValue meta0.SearchEmployeeResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryEmployee(param1 context.Context, param2 *meta0.QueryEmployeeRequest, param3 ...callopt.Option) (*meta0.QueryEmployeeResponse, error) {
	var ret1PtrValue meta0.QueryEmployeeResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetAppBasicInfos(param1 context.Context, param2 *meta0.GetAppBasicInfosRequest, param3 ...callopt.Option) (*meta0.GetAppBasicInfosResponse, error) {
	var ret1PtrValue meta0.GetAppBasicInfosResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppSimpleInfoById(param1 context.Context, param2 *meta0.QueryAppSimpleInfoByIdRequest, param3 ...callopt.Option) (*meta0.QueryAppSimpleInfoByIdResponse, error) {
	var ret1PtrValue meta0.QueryAppSimpleInfoByIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppSimpleInfoByEnglishName(param1 context.Context, param2 *meta0.QueryAppSimpleInfoByEnglishNameRequest, param3 ...callopt.Option) (*meta0.QueryAppSimpleInfoByEnglishNameResponse, error) {
	var ret1PtrValue meta0.QueryAppSimpleInfoByEnglishNameResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CreateApp(param1 context.Context, param2 *meta0.CreateAppRequest, param3 ...callopt.Option) (*meta0.CreateAppResponse, error) {
	var ret1PtrValue meta0.CreateAppResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) UpdateApp(param1 context.Context, param2 *meta0.UpdateAppRequest, param3 ...callopt.Option) (*meta0.UpdateAppResponse, error) {
	var ret1PtrValue meta0.UpdateAppResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppInfo(param1 context.Context, param2 *meta0.QueryAppInfoRequest, param3 ...callopt.Option) (*meta0.QueryAppInfoResponse, error) {
	var ret1PtrValue meta0.QueryAppInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppInfoById(param1 context.Context, param2 *meta0.QueryAppInfoByIdRequest, param3 ...callopt.Option) (*meta0.QueryAppInfoResponse, error) {
	var ret1PtrValue meta0.QueryAppInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppInfoByEnglishName(param1 context.Context, param2 *meta0.QueryAppInfoByEnglishNameRequest, param3 ...callopt.Option) (*meta0.QueryAppInfoResponse, error) {
	var ret1PtrValue meta0.QueryAppInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppBaseInfoByIds(param1 context.Context, param2 *meta0.QueryAppBaseInfoByIdsRequest, param3 ...callopt.Option) (*meta0.QueryAppBaseInfoResponse, error) {
	var ret1PtrValue meta0.QueryAppBaseInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppInfoByAppName(param1 context.Context, param2 *meta0.QueryAppInfoByAppNameRequest, param3 ...callopt.Option) (*meta0.QueryAppInfoByAppNameResponse, error) {
	var ret1PtrValue meta0.QueryAppInfoByAppNameResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppInfosByEnglishName(param1 context.Context, param2 *meta0.QueryAppInfosByEnglishNameRequest, param3 ...callopt.Option) (*meta0.QueryAppInfosByEnglishNameResponse, error) {
	var ret1PtrValue meta0.QueryAppInfosByEnglishNameResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppSimpleInfosByRepo(param1 context.Context, param2 *meta0.QueryAppSimpleInfosByRepoRequest, param3 ...callopt.Option) (*meta0.QueryAppSimpleInfosByRepoResponse, error) {
	var ret1PtrValue meta0.QueryAppSimpleInfosByRepoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppInfoByNameNoLike(param1 context.Context, param2 *meta0.QueryAppInfoByNameNoLikeQuery, param3 ...callopt.Option) (*meta0.QueryAppInfoResponse, error) {
	var ret1PtrValue meta0.QueryAppInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppInfoByAppCloudId(param1 context.Context, param2 *meta0.QueryAppInfoByAppCloudIdRequest, param3 ...callopt.Option) (*meta0.QueryAppInfoByAppCloudIdResponse, error) {
	var ret1PtrValue meta0.QueryAppInfoByAppCloudIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppId(param1 context.Context, param2 *meta0.QueryAppIdRequest, param3 ...callopt.Option) (*meta0.QueryAppIdResponse, error) {
	var ret1PtrValue meta0.QueryAppIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppIdV2(param1 context.Context, param2 *meta0.QueryAppIdRequest, param3 ...callopt.Option) (*meta0.QueryAppIdResponse, error) {
	var ret1PtrValue meta0.QueryAppIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAllAppSimpleInfos(param1 context.Context, param2 *meta0.QueryAllAppSimpleInfosRequest, param3 ...callopt.Option) (*meta0.QueryAllAppSimpleInfosResponse, error) {
	var ret1PtrValue meta0.QueryAllAppSimpleInfosResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAllAppInfos(param1 context.Context, param2 *meta0.QueryAllAppInfosRequest, param3 ...callopt.Option) (*meta0.QueryAllAppInfostResponse, error) {
	var ret1PtrValue meta0.QueryAllAppInfostResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryAppSimpleInfoByIds(param1 context.Context, param2 *meta0.QueryAppSimpleInfoByIdsRequest, param3 ...callopt.Option) (*meta0.QueryAppSimpleInfoByIdsResponse, error) {
	var ret1PtrValue meta0.QueryAppSimpleInfoByIdsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CountApp(param1 context.Context, param2 *meta0.CountAppRequest, param3 ...callopt.Option) (*meta0.CountAppResponse, error) {
	var ret1PtrValue meta0.CountAppResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CheckAppAdminRoleByEnglishName(param1 context.Context, param2 *meta0.CheckAppAdminRoleByEnglishNameRequest, param3 ...callopt.Option) (*meta0.CheckAppAdminRoleByEnglishNameResponse, error) {
	var ret1PtrValue meta0.CheckAppAdminRoleByEnglishNameResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetAppBySpaceId(param1 context.Context, param2 *meta0.GetAppBySpaceIdRequest, param3 ...callopt.Option) (*meta0.GetAppBySpaceIdResponse, error) {
	var ret1PtrValue meta0.GetAppBySpaceIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryPlatformRoles(param1 context.Context, param2 *meta0.QueryPlatformRolesRequest, param3 ...callopt.Option) (*meta0.QueryPlatformRolesResponse, error) {
	var ret1PtrValue meta0.QueryPlatformRolesResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryUserHasPermission(param1 context.Context, param2 *meta0.QueryUserHasPermissionRequest, param3 ...callopt.Option) (*meta0.QueryUserHasPermissionResponse, error) {
	var ret1PtrValue meta0.QueryUserHasPermissionResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryUserAppPermission(param1 context.Context, param2 *meta0.QueryUserAppPermissionRequest, param3 ...callopt.Option) (*meta0.QueryUserAppPermissionResponse, error) {
	var ret1PtrValue meta0.QueryUserAppPermissionResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetUserEmployeeId(param1 context.Context, param2 *meta0.GetUserEmployeeIdRequest, param3 ...callopt.Option) (*meta0.GetUserEmployeeIdResponse, error) {
	var ret1PtrValue meta0.GetUserEmployeeIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryLarkUser(param1 context.Context, param2 *meta0.QueryLarkUserRequest, param3 ...callopt.Option) (*meta0.QueryLarkUserResponse, error) {
	var ret1PtrValue meta0.QueryLarkUserResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryLarkIdByEmail(param1 context.Context, param2 *meta0.QueryLarkIdByEmailRequest, param3 ...callopt.Option) (*meta0.QueryLarkIdByEmailResponse, error) {
	var ret1PtrValue meta0.QueryLarkIdByEmailResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) IsUserResigned(param1 context.Context, param2 *meta0.IsUserResignedRequest, param3 ...callopt.Option) (*meta0.IsUserResignedResponse, error) {
	var ret1PtrValue meta0.IsUserResignedResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) RefreshUser(param1 context.Context, param2 *meta0.RefreshUserRequest, param3 ...callopt.Option) (*base.EmptyResponse, error) {
	var ret1PtrValue base.EmptyResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) RefreshUserToken(param1 context.Context, param2 *meta0.RefreshUserTokenRequest, param3 ...callopt.Option) (*base.EmptyResponse, error) {
	var ret1PtrValue base.EmptyResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetUserTokenInfo(param1 context.Context, param2 *meta0.GetUserTokenInfoRequest, param3 ...callopt.Option) (*meta0.GetUserTokenInfoResponse, error) {
	var ret1PtrValue meta0.GetUserTokenInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetUserLarkInfo(param1 context.Context, param2 *meta0.GetUserLarkInfoRequest, param3 ...callopt.Option) (*meta0.GetUserLarkInfoResponse, error) {
	var ret1PtrValue meta0.GetUserLarkInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) RemoveUserFromLarkChat(param1 context.Context, param2 *meta0.RemoveUserFromLarkChatQuery, param3 ...callopt.Option) (*meta0.RemoveUserFromLarkChatResponse, error) {
	var ret1PtrValue meta0.RemoveUserFromLarkChatResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetUsersInLarkGroup(param1 context.Context, param2 *meta0.GetUsersInLarkGroupQuery, param3 ...callopt.Option) (*meta0.GetUsersInLarkGroupResponse, error) {
	var ret1PtrValue meta0.GetUsersInLarkGroupResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CreateLarkGroup(param1 context.Context, param2 *meta0.CreateLarkGroupQuery, param3 ...callopt.Option) (*meta0.CreateLarkGroupResponse, error) {
	var ret1PtrValue meta0.CreateLarkGroupResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) UpdateLarkGroup(param1 context.Context, param2 *meta0.UpdateLarkGroupQuery, param3 ...callopt.Option) (*meta0.UpdateLarkGroupResponse, error) {
	var ret1PtrValue meta0.UpdateLarkGroupResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) InviteUserJoinLarkGroup(param1 context.Context, param2 *meta0.InviteUserJoinLarkGroupQuery, param3 ...callopt.Option) (*meta0.InviteUserJoinLarkGroupResponse, error) {
	var ret1PtrValue meta0.InviteUserJoinLarkGroupResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetMiniAppUserInfo(param1 context.Context, param2 *meta0.GetMiniAppUserInfoQuery, param3 ...callopt.Option) (*meta0.GetMiniAppUserInfoResponse, error) {
	var ret1PtrValue meta0.GetMiniAppUserInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CreateTeamGroupInfo(param1 context.Context, param2 *meta0.CreateTeamGroupInfoRequest, param3 ...callopt.Option) (*meta0.CreateTeamGroupInfoResponse, error) {
	var ret1PtrValue meta0.CreateTeamGroupInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) DeleteTeamGroupInfo(param1 context.Context, param2 *meta0.DeleteTeamGroupInfoRequest, param3 ...callopt.Option) (*meta0.DeleteTeamGroupInfoResponse, error) {
	var ret1PtrValue meta0.DeleteTeamGroupInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) UpdateTeamGroupInfo(param1 context.Context, param2 *meta0.UpdateTeamGroupInfoRequest, param3 ...callopt.Option) (*meta0.UpdateTeamGroupInfoResponse, error) {
	var ret1PtrValue meta0.UpdateTeamGroupInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetTeamGroupInfo(param1 context.Context, param2 *meta0.GetTeamGroupInfoRequest, param3 ...callopt.Option) (*meta0.GetTeamGroupInfoResponse, error) {
	var ret1PtrValue meta0.GetTeamGroupInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetAllTeamGroupsInfoByAppId(param1 context.Context, param2 *meta0.GetAllTeamGroupsInfoByAppIdRequest, param3 ...callopt.Option) (*meta0.GetAllTeamGroupsInfoByAppIdResponse, error) {
	var ret1PtrValue meta0.GetAllTeamGroupsInfoByAppIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetAppTeamRepoRelationsByTeamID(param1 context.Context, param2 *meta0.GetAppTeamRepoRelationsByTeamIDRequest, param3 ...callopt.Option) (*meta0.GetAppTeamRepoRelationsByTeamIDResponse, error) {
	var ret1PtrValue meta0.GetAppTeamRepoRelationsByTeamIDResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CreateAppMemberInfo(param1 context.Context, param2 *meta0.CreateAppMemberInfoRequest, param3 ...callopt.Option) (*meta0.CreateAppMemberInfoResponse, error) {
	var ret1PtrValue meta0.CreateAppMemberInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) DeleteAppMemberInfo(param1 context.Context, param2 *meta0.DeleteAppMemberInfoRequest, param3 ...callopt.Option) (*meta0.DeleteAppMemberInfoResponse, error) {
	var ret1PtrValue meta0.DeleteAppMemberInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) UpdateAppMemberInfo(param1 context.Context, param2 *meta0.UpdateAppMemberInfoRequest, param3 ...callopt.Option) (*meta0.UpdateAppMemberInfoResponse, error) {
	var ret1PtrValue meta0.UpdateAppMemberInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetSingleAppMemberInfo(param1 context.Context, param2 *meta0.GetSingleAppMemberInfoRequest, param3 ...callopt.Option) (*meta0.GetSingleAppMemberInfoResponse, error) {
	var ret1PtrValue meta0.GetSingleAppMemberInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetAllAppMembersInfo(param1 context.Context, param2 *meta0.GetAllAppMembersInfoRequest, param3 ...callopt.Option) (*meta0.GetAllAppMembersInfoResponse, error) {
	var ret1PtrValue meta0.GetAllAppMembersInfoResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetAppMemberAllAdminApps(param1 context.Context, param2 *meta0.GetAppMemberAllAdminAppsRequest, param3 ...callopt.Option) (*meta0.GetAppMemberAllAdminAppsResponse, error) {
	var ret1PtrValue meta0.GetAppMemberAllAdminAppsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CreateIAMGroup(param1 context.Context, param2 *meta0.IAMCreateGroupReq, param3 ...callopt.Option) (*meta0.IAMServiceTreeNode, error) {
	var ret1PtrValue meta0.IAMServiceTreeNode
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetIAMGroupList(param1 context.Context, param2 *meta0.VoidStruct, param3 ...callopt.Option) (*meta0.GetIAMAccountListResp, error) {
	var ret1PtrValue meta0.GetIAMAccountListResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) BindIAMRole(param1 context.Context, param2 *meta0.IAMBindRoleQuery, param3 ...callopt.Option) (*meta0.CloudApiResp, error) {
	var ret1PtrValue meta0.CloudApiResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CheckIAMPermission(param1 context.Context, param2 *meta0.IAMPermissionCheckQuery, param3 ...callopt.Option) (*meta0.IAMPermissionCheckRes, error) {
	var ret1PtrValue meta0.IAMPermissionCheckRes
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GenerateIamToken(param1 context.Context, param2 *meta0.GenerateIamTokenQuery, param3 ...callopt.Option) (*meta0.GenerateIamTokenResp, error) {
	var ret1PtrValue meta0.GenerateIamTokenResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) VerifyIAMToken(param1 context.Context, param2 *meta0.VerifyIAMTokenQuery, param3 ...callopt.Option) (*meta0.VerifyIAMTokenResp, error) {
	var ret1PtrValue meta0.VerifyIAMTokenResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) VerifyServiceToken(param1 context.Context, param2 *meta0.VerifyIAMTokenQuery, param3 ...callopt.Option) (*meta0.VerifyIAMTokenResp, error) {
	var ret1PtrValue meta0.VerifyIAMTokenResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetIAMToken(param1 context.Context, param2 *meta0.GetIAMTokenQuery, param3 ...callopt.Option) (*meta0.GetIAMTokenResp, error) {
	var ret1PtrValue meta0.GetIAMTokenResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) QueryCodebaseToken(param1 context.Context, param2 *meta0.QueryCodebaseTokenRequest, param3 ...callopt.Option) (*meta0.QueryCodebaseTokenResponse, error) {
	var ret1PtrValue meta0.QueryCodebaseTokenResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) SearchApp(param1 context.Context, param2 *meta0.SearchAppInfoQuery, param3 ...callopt.Option) (*meta0.QueryAppInfoListResponse, error) {
	var ret1PtrValue meta0.QueryAppInfoListResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) ListApp(param1 context.Context, param2 *meta0.SearchAppInfoQuery, param3 ...callopt.Option) (*meta0.SearchAppInfoResp, error) {
	var ret1PtrValue meta0.SearchAppInfoResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CheckAppPermission(param1 context.Context, param2 *meta0.CheckAppPermissionQuery, param3 ...callopt.Option) (*meta0.CheckAppPermissionResp, error) {
	var ret1PtrValue meta0.CheckAppPermissionResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CreateIAMTicket(param1 context.Context, param2 *meta0.CreateIAMTIcketQuery, param3 ...callopt.Option) (*meta0.CreateIAMTIcketResp, error) {
	var ret1PtrValue meta0.CreateIAMTIcketResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CancelTicket(param1 context.Context, param2 *meta0.CancelTicketQuery, param3 ...callopt.Option) (*meta0.VoidStruct, error) {
	var ret1PtrValue meta0.VoidStruct
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetAuthorizedApps(param1 context.Context, param2 *meta0.GetAuthorizedAppsQuery, param3 ...callopt.Option) (*meta0.QueryAppInfoListResponse, error) {
	var ret1PtrValue meta0.QueryAppInfoListResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) OneSitePing(param1 context.Context, param2 *meta0.OneSitePingReq, param3 ...callopt.Option) (*meta0.OneSitePingResp, error) {
	var ret1PtrValue meta0.OneSitePingResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) OneSiteCreate(param1 context.Context, param2 *meta0.OneSiteCreateSpaceReq, param3 ...callopt.Option) (*meta0.OneSiteCreateSpaceResp, error) {
	var ret1PtrValue meta0.OneSiteCreateSpaceResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) OneSiteUpdate(param1 context.Context, param2 *meta0.OneSiteUpdateSpaceReq, param3 ...callopt.Option) (*meta0.OneSiteUpdateResp, error) {
	var ret1PtrValue meta0.OneSiteUpdateResp
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) OneSiteInitialize(param1 context.Context, param2 *meta0.OneSiteInitializeSpaceReq, param3 ...callopt.Option) (*base.EmptyResponse, error) {
	var ret1PtrValue base.EmptyResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) CreateOneSiteResource(param1 context.Context, param2 *meta0.CreateOneSiteResourceRequest, param3 ...callopt.Option) (*meta0.CreateOneSiteResourceResponse, error) {
	var ret1PtrValue meta0.CreateOneSiteResourceResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) DeleteOneSiteResource(param1 context.Context, param2 *meta0.DeleteOneSiteResourceRequest, param3 ...callopt.Option) (*base.EmptyResponse, error) {
	var ret1PtrValue base.EmptyResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) UpdateOneSiteResource(param1 context.Context, param2 *meta0.UpdateOneSiteResourceRequest, param3 ...callopt.Option) (*meta0.UpdateOneSiteResourceResponse, error) {
	var ret1PtrValue meta0.UpdateOneSiteResourceResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) BatchCheckOneSitePermission(param1 context.Context, param2 *meta0.BatchCheckOneSitePermissionRequest, param3 ...callopt.Option) (*meta0.BatchCheckOneSitePermissionResponse, error) {
	var ret1PtrValue meta0.BatchCheckOneSitePermissionResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GrantOneSitePermission(param1 context.Context, param2 *meta0.GrantOneSitePermissionRequest, param3 ...callopt.Option) (*base.EmptyResponse, error) {
	var ret1PtrValue base.EmptyResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) RevokeOneSitePermission(param1 context.Context, param2 *meta0.RevokeOneSitePermissionRequest, param3 ...callopt.Option) (*base.EmptyResponse, error) {
	var ret1PtrValue base.EmptyResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) UpdateOneSitePermission(param1 context.Context, param2 *meta0.UpdateOneSitePermissionRequest, param3 ...callopt.Option) (*base.EmptyResponse, error) {
	var ret1PtrValue base.EmptyResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) IdTransfer(param1 context.Context, param2 *meta0.IdTransferRequest, param3 ...callopt.Option) (*meta0.IdTransferResponse, error) {
	var ret1PtrValue meta0.IdTransferResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) OneSiteCheckSpaceUsable(param1 context.Context, param2 *meta0.CheckSpaceUsableRequest, param3 ...callopt.Option) (*meta0.CheckSpaceUsableResponse, error) {
	var ret1PtrValue meta0.CheckSpaceUsableResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) BatchGrantRevokeOneSitePermission(param1 context.Context, param2 *meta0.BatchGrantRevokeOneSitePermissionRequest, param3 ...callopt.Option) (*meta0.BatchGrantRevokeOneSitePermissionResponse, error) {
	var ret1PtrValue meta0.BatchGrantRevokeOneSitePermissionResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetCalendarWorkspacesByAppId(param1 context.Context, param2 *meta0.GetCalendarWorkspacesByAppIdRequest, param3 ...callopt.Option) (*meta0.GetCalendarWorkspacesByAppIdResponse, error) {
	var ret1PtrValue meta0.GetCalendarWorkspacesByAppIdResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) ListLarkChatGroups(param1 context.Context, param2 *meta0.ListLarkChatGroupsRequest, param3 ...callopt.Option) (*meta0.ListLarkChatGroupsResponse, error) {
	var ret1PtrValue meta0.ListLarkChatGroupsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*metaserviceClientImplForTestAutoGen) GetErrorConfigList(param1 context.Context, param2 *meta0.GetErrorConfigListRequest, param3 ...callopt.Option) (*meta0.GetErrorConfigListResponse, error) {
	var ret1PtrValue meta0.GetErrorConfigListResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}
func (*metaserviceClientImplForTestAutoGen) QueryAppSimpleInfosPaginate(param1 context.Context, param2 *meta0.QueryAppSimpleInfosPaginateRequest, param3 ...callopt.Option) (*meta0.QueryAppSimpleInfosPaginateResponse, error) {
	var ret1PtrValue meta0.QueryAppSimpleInfosPaginateResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}
