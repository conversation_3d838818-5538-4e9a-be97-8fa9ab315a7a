package change_info_pack

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/lang/gg/gslice"
)

func adaptCheckItemStatusToContributionCheckStatus(status dev.CheckItemStatus) dev.ContributionCodeChangeCheckStatus {
	switch status {
	case dev.CheckItemStatus_failed:
		return dev.ContributionCodeChangeCheckStatus_failed
	case dev.CheckItemStatus_exceptional:
		return dev.ContributionCodeChangeCheckStatus_exceptional
	case dev.CheckItemStatus_succeeded:
		return dev.ContributionCodeChangeCheckStatus_succeeded
	case dev.CheckItemStatus_unstarted:
		return dev.ContributionCodeChangeCheckStatus_unstarted
	default:
		return dev.ContributionCodeChangeCheckStatus_running
	}
}

func AdaptChangeInfosToContributionInfos(changes []*ChangeInfo) []*dev.ContributionCodeChangeInfo {
	return gslice.Map(changes, func(c *ChangeInfo) *dev.ContributionCodeChangeInfo {
		return c.Change
	})
}
