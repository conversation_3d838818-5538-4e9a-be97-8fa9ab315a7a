package change_info_pack

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
)

func Test_adaptCheckItemStatusToContributionCheckStatusAutoGen(t *testing.T) {
	// Verify the function output when param1 is equal to 2.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_Param1Equal2", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			statusAlias := int64(2)
			status := dev.CheckItemStatus(statusAlias)

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 2)
		})
	})

	// Verify the function output when param1 is equal to 4.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_Param1Equal4", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			statusAlias := int64(4)
			status := dev.CheckItemStatus(statusAlias)

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 4)
		})
	})

	// Verify the function output when param1 is equal to 5.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_Param1Equal5", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// prepare parameters
			statusAlias := int64(5)
			status := dev.CheckItemStatus(statusAlias)

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 5)
		})
	})

	// Verify the function output when param1 is equal to 3.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_Param1Equal3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// prepare parameters
			statusAlias := int64(3)
			status := dev.CheckItemStatus(statusAlias)

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 3)
		})
	})

	// Verify the function output under default condition.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// prepare parameters
			var status dev.CheckItemStatus

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 1)
		})
	})

}

func TestAdaptChangeInfosToContributionInfosAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var changes []*ChangeInfo

			// run target function and assert
			got1 := AdaptChangeInfosToContributionInfos(changes)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

