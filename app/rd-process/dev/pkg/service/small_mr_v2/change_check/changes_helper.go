package change_check

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"context"
	"errors"
	"golang.org/x/sync/errgroup"
	"sync"
)

type ChangesCheckHelper struct{}

func NewChangesCheckHelper() *ChangesCheckHelper {
	return &ChangesCheckHelper{}
}

type ChangesCheckResult struct {
	Succeeded     bool
	Msg           string
	FailedChanges []*change_entity.CodeChange
}

func (h *ChangesCheckHelper) ParallelCheck(ctx context.Context, changes []*change_entity.CodeChange, checkItem CheckItem) *ChangesCheckResult {
	answer := &ChangesCheckResult{
		Succeeded:     true,
		Msg:           "",
		FailedChanges: make([]*change_entity.CodeChange, 0),
	}
	lock := sync.Mutex{}
	eg, innerCtx := errgroup.WithContext(ctx)
	for idx := range changes {
		change := changes[idx]
		eg.Go(func() error {
			result := checkItem.GetResult(innerCtx, change)
			if !result.Pass {
				lock.Lock()
				answer.FailedChanges = append(answer.FailedChanges, change)
				lock.Unlock()
				return errors.New(result.Msg)
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		answer.Succeeded = false
		answer.Msg = err.Error()
	}
	return answer
}
