package change_check

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
)

func TestChangesCheckHelper_ParallelCheckAutoGen(t *testing.T) {
	// Verify the scenario when the ParallelCheck function fails.
	t.Run("testChangesCheckHelper_ParallelCheckFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var withContextRet1MockPtrValue errgroup.Group
			withContextRet1Mock := &withContextRet1MockPtrValue
			withContextRet2Mock := context.Background()
			mockey.Mock(errgroup.WithContext, mockey.OptUnsafe).Return(withContextRet1Mock, withContextRet2Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			waitRet1Mock := fmt.Errorf("error")
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			// prepare parameters
			var checkItem CheckItem
			var receiverPtrValue ChangesCheckHelper
			receiver := &receiverPtrValue
			ctx := context.Background()
			var changes []*change_entity.CodeChange

			// run target function and assert
			got1 := receiver.ParallelCheck(ctx, changes, checkItem)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Succeeded, convey.ShouldEqual, false)
			convey.So((*got1).Msg, convey.ShouldEqual, "error")
			convey.So(len((*got1).FailedChanges), convey.ShouldEqual, 0)
		})
	})

}

