package change_check

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/value_object"
)

func Test_adaptPipelineStatusToCheckItemStatusAutoGen(t *testing.T) {
	// param1 == "failed"
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			status := "failed"

			// run target function and assert
			got1 := adaptPipelineStatusToCheckItemStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 2)
		})
	})

	// param1 != "failed"
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			status := "not failed"

			// run target function and assert
			got1 := adaptPipelineStatusToCheckItemStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 1)
		})
	})

	// param1 != "failed"
	// param1 == "success"
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// prepare parameters
			status := "success"

			// run target function and assert
			got1 := adaptPipelineStatusToCheckItemStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 3)
		})
	})

}

func Test_adaptCheckItemsStatusToFinalStatusAutoGen(t *testing.T) {
	// Verify the function output when the first item in the status list is 2.
	t.Run("testAdaptCheckItemsStatusToFinalStatus_Status2", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			statusList := []dev.CheckItemStatus{}
			statusList0ItemAlias := int64(2)
			statusList0Item := dev.CheckItemStatus(statusList0ItemAlias)
			statusList = append(statusList, statusList0Item)

			// run target function and assert
			got1 := adaptCheckItemsStatusToFinalStatus(statusList)
			convey.So(int64(got1), convey.ShouldEqual, 2)
		})
	})

	// Verify the function output when the first item in the status list is 1.
	t.Run("testAdaptCheckItemsStatusToFinalStatus_Status1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			statusList := []dev.CheckItemStatus{}
			statusList0ItemAlias := int64(1)
			statusList0Item := dev.CheckItemStatus(statusList0ItemAlias)
			statusList = append(statusList, statusList0Item)

			// run target function and assert
			got1 := adaptCheckItemsStatusToFinalStatus(statusList)
			convey.So(int64(got1), convey.ShouldEqual, 1)
		})
	})

}

func Test_adaptToConflictDetectCheckItemInfoAutoGen(t *testing.T) {
	// param2.HasConflicts != true
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getGitlabMrConflictURLRet1Mock string
			mockey.Mock(functools.GetGitlabMrConflictURL, mockey.OptUnsafe).Return(getGitlabMrConflictURLRet1Mock).Build()

			// prepare parameters
			var name string
			var infoPtrValue value_object.ConflictInfo
			info := &infoPtrValue
			info.HasConflicts = false
			var repoPath string
			var iid int64

			// run target function and assert
			got1 := adaptToConflictDetectCheckItemInfo(name, info, repoPath, iid)
			convey.So((*got1).PipelineID == nil, convey.ShouldBeTrue)
			convey.So((*got1).CommitID == nil, convey.ShouldBeTrue)
			convey.So((*got1).CommitMsg == nil, convey.ShouldBeTrue)
			convey.So((*got1).Msg == nil, convey.ShouldBeTrue)
			convey.So(int64((*got1).Status), convey.ShouldEqual, 3)
			convey.So((*got1).CheckName, convey.ShouldBeBlank)
			convey.So((*got1).DetailLink == nil, convey.ShouldBeTrue)
			convey.So(int64((*got1).CheckType), convey.ShouldEqual, 3)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func Test_adaptCheckItemStatusToContributionCheckStatusAutoGen(t *testing.T) {
	// Verify the function output when param1 is equal to 2.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_Param1Equal2", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			statusAlias := int64(2)
			status := dev.CheckItemStatus(statusAlias)

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 2)
		})
	})

	// Verify the function output when param1 is not equal to 2.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_Param1NotEqual2", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			statusAlias := int64(3)
			status := dev.CheckItemStatus(statusAlias)

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 3)
		})
	})

	// Verify the function output when param1 is equal to 4.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_Param1Equal4", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// prepare parameters
			statusAlias := int64(4)
			status := dev.CheckItemStatus(statusAlias)

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 4)
		})
	})

	// Verify the function output when param1 is not equal to 2, 3, or 4.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_Param1NotEqual234", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// prepare parameters
			statusAlias := int64(5)
			status := dev.CheckItemStatus(statusAlias)

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 5)
		})
	})

	// Verify the function output in the default condition.
	t.Run("testAdaptCheckItemStatusToContributionCheckStatus_Default", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// prepare parameters
			var status dev.CheckItemStatus

			// run target function and assert
			got1 := adaptCheckItemStatusToContributionCheckStatus(status)
			convey.So(int64(got1), convey.ShouldEqual, 1)
		})
	})

}

