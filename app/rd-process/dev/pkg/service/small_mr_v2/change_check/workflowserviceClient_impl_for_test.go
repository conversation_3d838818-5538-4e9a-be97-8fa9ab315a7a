package change_check
import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/workflow"
	"code.byted.org/kite/kitex/client/callopt"
)

type workflowserviceClientImplForTestAutoGen struct{}

func (*workflowserviceClientImplForTestAutoGen) GetPipelineConfig(param1 context.Context, param2 *workflow.GetPipelineConfigRequest, param3 ...callopt.Option)(*workflow.GetPipelineConfigResponse, error) {
	var ret1PtrValue workflow.GetPipelineConfigResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) AddPipelineConfig(param1 context.Context, param2 *workflow.AddPipelineConfigRequest, param3 ...callopt.Option)(*workflow.AddPipelineConfigResponse, error) {
	var ret1PtrValue workflow.AddPipelineConfigResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdatePipelineConfig(param1 context.Context, param2 *workflow.UpdatePipelineConfigRequest, param3 ...callopt.Option)(*workflow.UpdatePipelineConfigResponse, error) {
	var ret1PtrValue workflow.UpdatePipelineConfigResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) AddJobService(param1 context.Context, param2 *workflow.AddJobServiceRequest, param3 ...callopt.Option)(*workflow.AddJobServiceResponse, error) {
	var ret1PtrValue workflow.AddJobServiceResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) AddWebhook(param1 context.Context, param2 *workflow.AddWebhookRequest, param3 ...callopt.Option)(*workflow.AddWebhookResponse, error) {
	var ret1PtrValue workflow.AddWebhookResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) TriggerPipeline(param1 context.Context, param2 *workflow.TriggerPipelineRequest, param3 ...callopt.Option)(*workflow.TriggerPipelineResponse, error) {
	var ret1PtrValue workflow.TriggerPipelineResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) TriggerTemplatePipeline(param1 context.Context, param2 *workflow.TriggerTemplatePipelineRequest, param3 ...callopt.Option)(*workflow.TriggerTemplatePipelineResponse, error) {
	var ret1PtrValue workflow.TriggerTemplatePipelineResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) TriggerFreePipeline(param1 context.Context, param2 *workflow.TriggerFreePipelineRequest, param3 ...callopt.Option)(*workflow.TriggerFreePipelineResponse, error) {
	var ret1PtrValue workflow.TriggerFreePipelineResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) RetryPipeline(param1 context.Context, param2 *workflow.RetryPipelineRequest, param3 ...callopt.Option)(*workflow.RetryPipelineResponse, error) {
	var ret1PtrValue workflow.RetryPipelineResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetPipeline(param1 context.Context, param2 *workflow.GetPipelineRequest, param3 ...callopt.Option)(*workflow.GetPipelineResponse, error) {
	var ret1PtrValue workflow.GetPipelineResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) PipelineDetail(param1 context.Context, param2 *workflow.PipelineDetailRequest, param3 ...callopt.Option)(*workflow.PipelineDetailResponse, error) {
	var ret1PtrValue workflow.PipelineDetailResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) MRPipelineList(param1 context.Context, param2 *workflow.MRPipelineListRequest, param3 ...callopt.Option)(*workflow.MRPipelineListResponse, error) {
	var ret1PtrValue workflow.MRPipelineListResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) MGetPipeline(param1 context.Context, param2 *workflow.MGetPipelineRequest, param3 ...callopt.Option)(*workflow.MGetPipelineResponse, error) {
	var ret1PtrValue workflow.MGetPipelineResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) CancelPipeline(param1 context.Context, param2 *workflow.CancelPipelineRequest, param3 ...callopt.Option)(*workflow.CancelPipelineResponse, error) {
	var ret1PtrValue workflow.CancelPipelineResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) TriggerGroupPipeline(param1 context.Context, param2 *workflow.TriggerGroupPipelineRequest, param3 ...callopt.Option)(*workflow.TriggerGroupPipelineResponse, error) {
	var ret1PtrValue workflow.TriggerGroupPipelineResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdatePipeline(param1 context.Context, param2 *workflow.UpdatePipelineRequest, param3 ...callopt.Option)(*workflow.UpdatePipelineResponse, error) {
	var ret1PtrValue workflow.UpdatePipelineResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) TriggerJob(param1 context.Context, param2 *workflow.TriggerJobRequest, param3 ...callopt.Option)(*workflow.TriggerJobResponse, error) {
	var ret1PtrValue workflow.TriggerJobResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) TriggerJenkinsJob(param1 context.Context, param2 *workflow.TriggerJenkinsJobRequest, param3 ...callopt.Option)(*workflow.TriggerJobResponse, error) {
	var ret1PtrValue workflow.TriggerJobResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) TriggerTemplateJob(param1 context.Context, param2 *workflow.TriggerTemplateJobRequest, param3 ...callopt.Option)(*workflow.TriggerJobResponse, error) {
	var ret1PtrValue workflow.TriggerJobResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) RetryJob(param1 context.Context, param2 *workflow.RetryJobRequest, param3 ...callopt.Option)(*workflow.RetryJobResponse, error) {
	var ret1PtrValue workflow.RetryJobResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetJob(param1 context.Context, param2 *workflow.GetJobRequest, param3 ...callopt.Option)(*workflow.GetJobResponse, error) {
	var ret1PtrValue workflow.GetJobResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetJobs(param1 context.Context, param2 *workflow.GetJobsRequest, param3 ...callopt.Option)(*workflow.GetJobsResponse, error) {
	var ret1PtrValue workflow.GetJobsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) CancelJob(param1 context.Context, param2 *workflow.CancelJobRequest, param3 ...callopt.Option)(*workflow.CancelJobResponse, error) {
	var ret1PtrValue workflow.CancelJobResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdateJob(param1 context.Context, param2 *workflow.UpdateJobRequest, param3 ...callopt.Option)(*workflow.UpdateJobResponse, error) {
	var ret1PtrValue workflow.UpdateJobResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdateStep(param1 context.Context, param2 *workflow.UpdateStepRequest, param3 ...callopt.Option)(*workflow.UpdateStepResponse, error) {
	var ret1PtrValue workflow.UpdateStepResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) BatchUpdateStep(param1 context.Context, param2 *workflow.BatchUpdateStepRequest, param3 ...callopt.Option)(*workflow.BatchUpdateStepResponse, error) {
	var ret1PtrValue workflow.BatchUpdateStepResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetStepLog(param1 context.Context, param2 *workflow.GetStepLogRequest, param3 ...callopt.Option)(*workflow.GetStepLogResponse, error) {
	var ret1PtrValue workflow.GetStepLogResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetJobAllLog(param1 context.Context, param2 *workflow.GetJobAllLogRequest, param3 ...callopt.Option)(*workflow.GetJobAllLogResponse, error) {
	var ret1PtrValue workflow.GetJobAllLogResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetJobLog(param1 context.Context, param2 *workflow.GetJobLogRequest, param3 ...callopt.Option)(*workflow.GetJobLogResponse, error) {
	var ret1PtrValue workflow.GetJobLogResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) PlaceholderTriggerResult_(param1 context.Context, param2 *workflow.PlaceholderTriggerResultRequest, param3 ...callopt.Option)(*workflow.PlaceholderTriggerResultResponse, error) {
	var ret1PtrValue workflow.PlaceholderTriggerResultResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) AllowJobFailed(param1 context.Context, param2 *workflow.AllowJobFailedRequest, param3 ...callopt.Option)(*workflow.AllowJobFailedResponse, error) {
	var ret1PtrValue workflow.AllowJobFailedResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetJobLogURL(param1 context.Context, param2 *workflow.GetJobLogURLRequest, param3 ...callopt.Option)(*workflow.GetJobLogURLResponse, error) {
	var ret1PtrValue workflow.GetJobLogURLResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) AddJobArtifacts(param1 context.Context, param2 *workflow.AddJobArtifactsRequest, param3 ...callopt.Option)(*workflow.AddJobArtifactsResponse, error) {
	var ret1PtrValue workflow.AddJobArtifactsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdateJobResults(param1 context.Context, param2 *workflow.UpdateJobResultsRequest, param3 ...callopt.Option)(*workflow.UpdateJobResultsResponse, error) {
	var ret1PtrValue workflow.UpdateJobResultsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdateJobMsg(param1 context.Context, param2 *workflow.UpdateJobMsgRequest, param3 ...callopt.Option)(*workflow.UpdateJobMsgResponse, error) {
	var ret1PtrValue workflow.UpdateJobMsgResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdateJobURL(param1 context.Context, param2 *workflow.UpdateJobURLRequest, param3 ...callopt.Option)(*workflow.UpdateJobURLResponse, error) {
	var ret1PtrValue workflow.UpdateJobURLResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetAppCloudbuildQueues(param1 context.Context, param2 *workflow.GetAppCloudbuildQueuesRequest, param3 ...callopt.Option)(*workflow.GetAppCloudbuildQueuesResponse, error) {
	var ret1PtrValue workflow.GetAppCloudbuildQueuesResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdateCloudBuildQueue(param1 context.Context, param2 *workflow.UpdateCloudBuildQueueRequest, param3 ...callopt.Option)(*workflow.UpdateCloudBuildQueueResponse, error) {
	var ret1PtrValue workflow.UpdateCloudBuildQueueResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdateCloudBuildQueueRelation(param1 context.Context, param2 *workflow.UpdateCloudBuildQueueRelationRequest, param3 ...callopt.Option)(*workflow.UpdateCloudBuildQueueRelationResponse, error) {
	var ret1PtrValue workflow.UpdateCloudBuildQueueRelationResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetCloudBuildQueue(param1 context.Context, param2 *workflow.GetCloudBuildQueueRequest, param3 ...callopt.Option)(*workflow.GetCloudBuildQueueResponse, error) {
	var ret1PtrValue workflow.GetCloudBuildQueueResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) DeleteCloudBuildQueue(param1 context.Context, param2 *workflow.DeleteCloudBuildQueueRequest, param3 ...callopt.Option)(*workflow.DeleteCloudBuildQueueResponse, error) {
	var ret1PtrValue workflow.DeleteCloudBuildQueueResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) BytestCallback(param1 context.Context, param2 *workflow.BytestCallbackRequest, param3 ...callopt.Option)(*workflow.BytestCallbackResponse, error) {
	var ret1PtrValue workflow.BytestCallbackResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) JenkinsQueueReport(param1 context.Context, param2 *workflow.JenkinsQueueReportRequest, param3 ...callopt.Option)(*workflow.JenkinsQueueReportResponse, error) {
	var ret1PtrValue workflow.JenkinsQueueReportResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) JenkinsBuildReport(param1 context.Context, param2 *workflow.JenkinsBuildReportRequest, param3 ...callopt.Option)(*workflow.JenkinsBuildReportResponse, error) {
	var ret1PtrValue workflow.JenkinsBuildReportResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) JobDetail(param1 context.Context, param2 *workflow.JobDetailRequest, param3 ...callopt.Option)(*workflow.JobDetailResponse, error) {
	var ret1PtrValue workflow.JobDetailResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetTemplateConfigParams(param1 context.Context, param2 *workflow.GetTemplateConfigParamsRequest, param3 ...callopt.Option)(*workflow.GetTemplateConfigParamsResponse, error) {
	var ret1PtrValue workflow.GetTemplateConfigParamsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) GetMergeRequestJobs(param1 context.Context, param2 *workflow.GetMergeRequestJobsRequest, param3 ...callopt.Option)(*workflow.GetMergeRequestJobsResponse, error) {
	var ret1PtrValue workflow.GetMergeRequestJobsResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) ClearMergeBaseCache(param1 context.Context, param2 *workflow.ClearMergeBaseCacheRequest, param3 ...callopt.Option)(*workflow.ClearMergeBaseCacheResponse, error) {
	var ret1PtrValue workflow.ClearMergeBaseCacheResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) TrimTemplateConfig(param1 context.Context, param2 *workflow.TrimTemplateConfigRequest, param3 ...callopt.Option)(*workflow.TrimTemplateConfigResponse, error) {
	var ret1PtrValue workflow.TrimTemplateConfigResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}

func (*workflowserviceClientImplForTestAutoGen) UpdatePipelineForbiddenRetry(param1 context.Context, param2 *workflow.UpdatePipelineForbiddenRetryRequest, param3 ...callopt.Option)(*workflow.UpdatePipelineForbiddenRetryResponse, error) {
	var ret1PtrValue workflow.UpdatePipelineForbiddenRetryResponse
	ret1 := &ret1PtrValue
	var ret2 error
	return ret1, ret2
}
