package change_check

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/feature_gate"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_task_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/value_object"
	logs "code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
)

func TestGetChecksInfo(t *testing.T) {
	PatchConvey("get checks info by helper", t, func() {
		Mock(GetCheckInfoTypePipeline).Return(gresult.OK(&dev.CheckItemInfo{
			CheckName: "ci check",
			CheckType: dev.ContributionCheckType_pipeline,
			Status:    dev.ContributionCodeChangeCheckStatus_unstarted,
		})).Build()
		Mock(GetCheckInfoTypeConflictDetect).Return(gresult.OK(&dev.CheckItemInfo{
			CheckName: "conflict detect",
			CheckType: dev.ContributionCheckType_conflict_detect,
			Status:    dev.ContributionCodeChangeCheckStatus_unstarted,
		})).Build()
		helper := NewChangeCheckHelper(&change_entity.CodeChange{
			Contribution: &model.ContributionCodeChange{BizConfig: "{\"checks\":[{\"name\":\"ci check\",\"type\":\"pipeline\"}]}"},
			GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
		})
		resp, _ := helper.GetCiChecksInfo(context.TODO()).Get()
		t.Logf(fmt.Sprintf("resp:%v", resp))
	})
}

func TestGetCheckRes(t *testing.T) {
	PatchConvey("get checks info by helper", t, func() {
		Mock(GetCheckInfoTypePipeline).Return(gresult.OK(&dev.CheckItemInfo{
			CheckName: "ci check",
			CheckType: dev.ContributionCheckType_pipeline,
			Status:    dev.ContributionCodeChangeCheckStatus_succeeded,
		})).Build()
		Mock(redis.SetVal[dev.CheckItemStatus]).Return(nil).Build()
		Mock(gitsvr.CheckConflictByCodeChangeId).Return(gresult.OK(&gitsvr.ConflictRepo{
			Conflicted: false,
		})).Build()
		helper := NewChangeCheckHelper(&change_entity.CodeChange{
			Contribution: &model.ContributionCodeChange{BizConfig: "{\"checks\":[{\"name\":\"ci check\",\"type\":\"pipeline\",\"status\":\"failed\"},{\"name\":\"conflict detect\",\"type\":\"conflict_detect\",\"status\":\"succeeded\"}]}"},
			GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
		})
		PatchConvey("get checks by biz config", func() {
			resp, err := helper.GetCiCheckRes(context.TODO(), false, consts.CheckResSourceBizConfig).Get()
			So(err, ShouldBeNil)
			t.Logf(fmt.Sprintf("resp:%v", resp))
		})
		PatchConvey("get checks by down stream", func() {
			resp, err := helper.GetCiCheckRes(context.TODO(), false, consts.CheckResSourceBizConfig).Get()
			So(err, ShouldBeNil)
			t.Logf(fmt.Sprintf("resp:%v", resp))
		})
	})
}

func TestParallelCheck(t *testing.T) {
	PatchConvey("parallel check", t, func() {
		change := &change_entity.CodeChange{
			Contribution: &model.ContributionCodeChange{BizConfig: "{\"checks\":[{\"name\":\"ci check\",\"type\":\"pipeline\"}]}"},
			GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
		}
		Mock((*CodeReview).GetResult).Return(&CheckItemResult{
			Pass: true,
			Msg:  "",
		}).Build()
		res := NewChangesCheckHelper().ParallelCheck(context.TODO(), []*change_entity.CodeChange{change}, NewCheckItemCodeReview())
		So(res.Succeeded, ShouldEqual, true)
	})
}

func TestChangeCheckHelper_TriggerSyncChecksStatusAutoGen(t *testing.T) {
	// code.byted.org/gopkg/logs/v2.Log:Str()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.Log:With()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.ByteDLogger:Info()_ret-1 != nil
	// V2Global != nil
	// receiver.Change != nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	// bizConfig.ReplaceChecks(ctx, bizConfig.Checks):Get()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:UpdateContributionCodeChangeByID()_ret-1 != nil
	// receiver.Change.Contribution != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getBizConfigRet1Mock gresult.R[int]
			mockey.Mock((*change_entity.CodeChange).GetBizConfig, mockey.OptUnsafe).Return(getBizConfigRet1Mock).Build()

			updateContributionCodeChangeByIDRet1Mock := fmt.Errorf("error")
			mockey.Mock((*data.Repo).UpdateContributionCodeChangeByID).Return(updateContributionCodeChangeByIDRet1Mock).Build()

			var v2MockPtrValue logs.ByteDLogger
			v2Mock := &v2MockPtrValue
			mockey.MockValue(&log.V2).To(v2Mock)

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var rMock gresult.R[int]
			mockey.Mock(GetCheckStatusTypeOfBitsPipeline).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var receiverPtrValue ChangeCheckHelper
			receiver := &receiverPtrValue
			var receiverChangePtrValue change_entity.CodeChange
			receiver.Change = &receiverChangePtrValue
			var receiverChangeContributionPtrValue model.ContributionCodeChange
			receiver.Change.Contribution = &receiverChangeContributionPtrValue
			convey.So(func() { _ = receiver.TriggerSyncChecksStatus(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestChangeCheckHelper_GetCiCheckInfoAutoGen(t *testing.T) {
	// param2 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(GetCheckInfoTypePipeline).Return(rMock).Build()

			var isNotOpenedRet1Mock bool
			mockey.Mock((*change_entity.CodeChange).IsNotOpened, mockey.OptUnsafe).Return(isNotOpenedRet1Mock).Build()

			var ifUseNewConflictDetectRet1Mock bool
			mockey.Mock(feature_gate.IfUseNewConflictDetect).Return(ifUseNewConflictDetectRet1Mock).Build()

			var checkItemInfoMockPtrValue dev.CheckItemInfo
			checkItemInfoMock := &checkItemInfoMockPtrValue
			mockey.Mock(adaptToConflictDetectCheckItemInfo).Return(checkItemInfoMock).Build()

			var getDevTaskWithOptRet1Mock gresult.R[int]
			mockey.Mock(dev_task_entity.GetDevTaskWithOpt).Return(getDevTaskWithOptRet1Mock).Build()

			var getConflictInfoRet1Mock gresult.R[int]
			mockey.Mock((*change_entity.CodeChange).GetConflictInfo).Return(getConflictInfoRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue ChangeCheckHelper
			receiver := &receiverPtrValue
			ctx := context.Background()
			var checkItem *value_object.ContributionCheckItem
			convey.So(func() { _ = receiver.GetCiCheckInfo(ctx, checkItem) }, convey.ShouldNotPanic)
		})
	})

	// param2 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock(GetCheckInfoTypePipeline).Return(rMock).Build()

			var isNotOpenedRet1Mock bool
			mockey.Mock((*change_entity.CodeChange).IsNotOpened, mockey.OptUnsafe).Return(isNotOpenedRet1Mock).Build()

			var ifUseNewConflictDetectRet1Mock bool
			mockey.Mock(feature_gate.IfUseNewConflictDetect).Return(ifUseNewConflictDetectRet1Mock).Build()

			var checkItemInfoMockPtrValue dev.CheckItemInfo
			checkItemInfoMock := &checkItemInfoMockPtrValue
			mockey.Mock(adaptToConflictDetectCheckItemInfo).Return(checkItemInfoMock).Build()

			var getDevTaskWithOptRet1Mock gresult.R[int]
			mockey.Mock(dev_task_entity.GetDevTaskWithOpt).Return(getDevTaskWithOptRet1Mock).Build()

			var getConflictInfoRet1Mock gresult.R[int]
			mockey.Mock((*change_entity.CodeChange).GetConflictInfo).Return(getConflictInfoRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue ChangeCheckHelper
			receiver := &receiverPtrValue
			ctx := context.Background()
			var checkItemPtrValue value_object.ContributionCheckItem
			checkItem := &checkItemPtrValue
			convey.So(func() { _ = receiver.GetCiCheckInfo(ctx, checkItem) }, convey.ShouldNotPanic)
		})
	})

}

func TestChangeCheckHelper_UpdateCheckStatusAutoGen(t *testing.T) {
	// Verify the UpdateCheckStatus function behavior.
	t.Run("testChangeCheckHelper_UpdateCheckStatus", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stringRet1Mock string
			mockey.Mock(dev.ContributionCheckType.String).Return(stringRet1Mock).Build()

			var ret5T1 string
			mockey.Mock(dev.ContributionCodeChangeCheckStatus.String).Return(ret5T1).Build()

			var updateContributionCodeChangeByIDRet1Mock error
			mockey.Mock((*data.Repo).UpdateContributionCodeChangeByID).Return(updateContributionCodeChangeByIDRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var rMock gresult.R[int]
			mockey.Mock((*change_entity.CodeChange).GetBizConfig, mockey.OptUnsafe).Return(rMock).Build()

			// prepare parameters
			var checkType dev.ContributionCheckType
			var status dev.ContributionCodeChangeCheckStatus
			var receiverPtrValue ChangeCheckHelper
			receiver := &receiverPtrValue
			var receiverChangePtrValue change_entity.CodeChange
			receiver.Change = &receiverChangePtrValue
			ctx := context.Background()
			var checkName string
			convey.So(func() { _ = receiver.UpdateCheckStatus(ctx, checkName, checkType, status) }, convey.ShouldPanic)
		})
	})

}

func TestChangeCheckHelper_TriggerNewPipelineCheckAutoGen(t *testing.T) {
	// param2 != nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getCommitIDRet1Mock string
			mockey.Mock((*dev.ContributionCodeChangePipelineCreationConfig).GetCommitID, mockey.OptUnsafe).Return(getCommitIDRet1Mock).Build()

			var contributionPipelineTypeMock dev.ContributionPipelineType
			mockey.Mock((*dev.ContributionCodeChangePipelineCreationConfig).GetPipelineType, mockey.OptUnsafe).Return(contributionPipelineTypeMock).Build()

			var stringRet1Mock string
			mockey.Mock(dev.ContributionPipelineType.String).Return(stringRet1Mock).Build()

			var getEnvVarsRet1Mock string
			mockey.Mock((*dev.ContributionCodeChangePipelineCreationConfig).GetEnvVars, mockey.OptUnsafe).Return(getEnvVarsRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).CreateContributionCodeChangePipeline).Return(rMock).Build()

			var updateCheckStatusRet1Mock error
			mockey.Mock((*ChangeCheckHelper).UpdateCheckStatus).Return(updateCheckStatusRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var getPipelineIDRet1Mock int64
			mockey.Mock((*dev.ContributionCodeChangePipelineCreationConfig).GetPipelineID, mockey.OptUnsafe).Return(getPipelineIDRet1Mock).Build()

			var getContributionIDRet1Mock int64
			mockey.Mock((*dev.ContributionCodeChangePipelineCreationConfig).GetContributionID, mockey.OptUnsafe).Return(getContributionIDRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue ChangeCheckHelper
			receiver := &receiverPtrValue
			ctx := context.Background()
			var configPtrValue dev.ContributionCodeChangePipelineCreationConfig
			config := &configPtrValue
			convey.So(func() { _ = receiver.TriggerNewPipelineCheck(ctx, config) }, convey.ShouldPanic)
		})
	})

}

