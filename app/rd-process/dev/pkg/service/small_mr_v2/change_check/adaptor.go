package change_check

import (
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/value_object"
	"code.byted.org/lang/gg/gptr"
)

func adaptPipelineStatusToCheckItemStatus(status string) dev.CheckItemStatus {
	switch status {
	case "failed":
		return dev.CheckItemStatus_failed
	case "success", "skip":
		return dev.CheckItemStatus_succeeded
	default:
		return dev.CheckItemStatus_running
	}
}

func adaptCheckItemStatusToContributionCheckStatus(status dev.CheckItemStatus) dev.ContributionCodeChangeCheckStatus {
	switch status {
	case dev.CheckItemStatus_failed:
		return dev.ContributionCodeChangeCheckStatus_failed
	case dev.CheckItemStatus_exceptional:
		return dev.ContributionCodeChangeCheckStatus_exceptional
	case dev.CheckItemStatus_succeeded:
		return dev.ContributionCodeChangeCheckStatus_succeeded
	case dev.CheckItemStatus_unstarted:
		return dev.ContributionCodeChangeCheckStatus_unstarted
	default:
		return dev.ContributionCodeChangeCheckStatus_running
	}
}

func adaptCheckItemsStatusToFinalStatus(statusList []dev.CheckItemStatus) dev.CheckItemStatus {
	answer := dev.CheckItemStatus_succeeded
	for _, status := range statusList {
		if status == dev.CheckItemStatus_failed {
			answer = dev.CheckItemStatus_failed
			break
		} else if status == dev.CheckItemStatus_running {
			answer = dev.CheckItemStatus_running
		}
	}
	return answer
}

func adaptToConflictDetectCheckItemInfo(name string, info *value_object.ConflictInfo, repoPath string, iid int64) *dev.CheckItemInfo {
	answer := &dev.CheckItemInfo{
		CheckName: name,
		CheckType: dev.ContributionCheckType_conflict_detect,
		Status:    dev.ContributionCodeChangeCheckStatus_unstarted,
	}
	if info.HasConflicts {
		answer.Status = dev.ContributionCodeChangeCheckStatus_failed
		answer.DetailLink = gptr.Of(functools.GetGitlabMrConflictURL(repoPath, iid))
		msg := ""
		for i, file := range info.ConflictFiles {
			if i != 0 {
				msg += "\n"
			}
			msg += fmt.Sprintf(`- %s`, file.OurPath)
		}
		answer.Msg = &msg
	} else {
		answer.Status = dev.ContributionCodeChangeCheckStatus_succeeded
	}
	return answer
}
