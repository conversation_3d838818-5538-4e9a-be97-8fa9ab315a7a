package small_mr_v2

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_task_entity"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
)

func TestGetGetMultiMergingGroups(t *testing.T) {
	    t.Skip()
	PatchConvey("get multi merging groups", t, func() {
		PatchConvey("multi group", func() {
			changes := []*ChangeDetail{{
				CodeChange: &change_entity.CodeChange{
					Contribution: &model.ContributionCodeChange{Id: 1, Author: "userX", Status: dev.ContributionCodeChangeStatus_opened.String(), BizConfig: "{\"merge_task\":{\"biz_id\",1,\"biz_type\":4}}"},
					GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
				},
				SourceBranch: "x",
				TargetBranch: "target",
			}, {
				CodeChange: &change_entity.CodeChange{
					Contribution: &model.ContributionCodeChange{Id: 2, Author: "userY", Status: dev.ContributionCodeChangeStatus_opened.String(), BizConfig: "{\"merge_task\":{\"biz_id\",2,\"biz_type\":4}}"},
					GitlabMr:     &git_server.CodeChangeGitlabMergeRequest{},
				},
				SourceBranch: "y",
				TargetBranch: "target",
			}}
			Mock(dev_task_entity.GetDevTaskWithOpt).Return(gresult.OK(&dev_task_entity.DevTask{BasicInfo: &model.DevBasicInfo{}, Config: &model.DevSmrConfig{TargetBranch: "target"}})).Build()
			_, err := GetMultiMergingGroups(context.TODO(), changes, &GroupParams{
				DevBasicID:  gptr.Of(int64(1)),
				TrunkBranch: gptr.Of("target"),
			}).Get()
			So(err, ShouldBeNil)
		})
	})
}

func Test_getStackGroupFuncByModeAndOptionsAutoGen(t *testing.T) {
	// Verify the function behavior when the user role is reviewer.
	t.Run("testGetStackGroupFuncByModeAndOptions_ReviewerRole", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			getUserRoleRet1Mock := "reviewer"
			mockey.Mock((*dev.ContributionGroupFilterOptions).GetUserRole, mockey.OptUnsafe).Return(getUserRoleRet1Mock).Build()

			// prepare parameters
			var mode string
			var partialMergeEnabled bool
			var optionsPtrValue dev.ContributionGroupFilterOptions
			options := &optionsPtrValue

			// run target function and assert
			got1 := getStackGroupFuncByModeAndOptions(mode, partialMergeEnabled, options)
			convey.So(len(got1), convey.ShouldEqual, 1)
		})
	})

	// Verify the function behavior in the default condition.
	t.Run("testGetStackGroupFuncByModeAndOptions_Default", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getUserRoleRet1Mock string
			mockey.Mock((*dev.ContributionGroupFilterOptions).GetUserRole, mockey.OptUnsafe).Return(getUserRoleRet1Mock).Build()

			// prepare parameters
			var partialMergeEnabled bool
			var optionsPtrValue dev.ContributionGroupFilterOptions
			options := &optionsPtrValue
			var mode string

			// run target function and assert
			got1 := getStackGroupFuncByModeAndOptions(mode, partialMergeEnabled, options)
			convey.So(len(got1), convey.ShouldEqual, 3)
		})
	})

}

func Test_checkIfChangeItemCanTriggerMergeAutoGen(t *testing.T) {
	// param1.ReviewInfo != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev.ContributionCodeChangeListItem:GetCheckStatus()_ret-1 == 3
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			contributionCodeChangeCheckStatusMockAlias := int64(3)
			contributionCodeChangeCheckStatusMock := dev.ContributionCodeChangeCheckStatus(contributionCodeChangeCheckStatusMockAlias)
			mockey.Mock((*dev.ContributionCodeChangeListItem).GetCheckStatus, mockey.OptUnsafe).Return(contributionCodeChangeCheckStatusMock).Build()

			// prepare parameters
			var changeItemPtrValue dev.ContributionCodeChangeListItem
			changeItem := &changeItemPtrValue
			var changeItemReviewInfoPtrValue dev.ReviewInfo
			changeItem.ReviewInfo = &changeItemReviewInfoPtrValue

			// run target function and assert
			got1 := checkIfChangeItemCanTriggerMerge(changeItem)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

func TestGroupChangesAutoGen(t *testing.T) {
	// Verify the error handling when an error occurs in GroupChanges.
	t.Run("testGroupChanges_ErrorHandling", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var paramsPtrValue GroupParams
			params := &paramsPtrValue
			var fsItem0 GetChangeGroupsFunc
			fs := []GetChangeGroupsFunc{fsItem0,}
			convey.So(func() { _ = GroupChanges(ctx, changes, params, fs) }, convey.ShouldPanic)
		})
	})

	// Verify the normal operation of GroupChanges without errors.
	t.Run("testGroupChanges_NormalOperation", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var paramsPtrValue GroupParams
			params := &paramsPtrValue
			var fs []GetChangeGroupsFunc
			convey.So(func() { _ = GroupChanges(ctx, changes, params, fs) }, convey.ShouldNotPanic)
		})
	})

}

func Test_filterChangesInDownsideMergingGroupAutoGen(t *testing.T) {
	// len(param2) <= 1
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			changes := []*ChangeDetail{}

			// run target function and assert
			got1 := filterChangesInDownsideMergingGroup(ctx, changes)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

	// len(param2) > 1
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			ctx := context.Background()
			var changesItem0PtrValueCodeChange change_entity.CodeChange
			changesItem0PtrValue := ChangeDetail{CodeChange: &changesItem0PtrValueCodeChange}
			changesItem0 := &changesItem0PtrValue
			var changesItem1PtrValueCodeChange change_entity.CodeChange
			changesItem1PtrValue := ChangeDetail{CodeChange: &changesItem1PtrValueCodeChange}
			changesItem1 := &changesItem1PtrValue
			changes := []*ChangeDetail{changesItem0,changesItem1,}
			convey.So(func() { _ = filterChangesInDownsideMergingGroup(ctx, changes) }, convey.ShouldPanic)
		})
	})

}

func TestGetDevContributionStatusAutoGen(t *testing.T) {
	// Verify the function behavior when options are not null.
	t.Run("testGetDevContributionStatus_OptionsNotNull", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getConflictRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetConflict, mockey.OptUnsafe).Return(getConflictRet1Mock).Build()

			var getChangeDetailRet1Mock gresult.R[int]
			mockey.Mock(GetChangeDetail).Return(getChangeDetailRet1Mock).Build()

			var getReviewerStatusRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetReviewerStatus, mockey.OptUnsafe).Return(getReviewerStatusRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(GetContributionListItemByChange).Return(rMock).Build()

			var getMergeStatusRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetMergeStatus, mockey.OptUnsafe).Return(getMergeStatusRet1Mock).Build()

			var getDiffRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetDiff, mockey.OptUnsafe).Return(getDiffRet1Mock).Build()

			var getCommentRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetComment, mockey.OptUnsafe).Return(getCommentRet1Mock).Build()

			var getCiStatusRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetCiStatus, mockey.OptUnsafe).Return(getCiStatusRet1Mock).Build()

			// prepare parameters
			var contributionIDsItem0 int64
			contributionIDs := []int64{contributionIDsItem0,}
			var optionsPtrValue dev.GetDevContributionStatusOptions
			options := &optionsPtrValue
			ctx := context.Background()
			convey.So(func() { _ = GetDevContributionStatus(ctx, contributionIDs, options) }, convey.ShouldNotPanic)
		})
	})

	// Verify the function behavior under default conditions.
	t.Run("testGetDevContributionStatus_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getReviewerStatusRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetReviewerStatus, mockey.OptUnsafe).Return(getReviewerStatusRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(GetContributionListItemByChange).Return(rMock).Build()

			var getMergeStatusRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetMergeStatus, mockey.OptUnsafe).Return(getMergeStatusRet1Mock).Build()

			var getDiffRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetDiff, mockey.OptUnsafe).Return(getDiffRet1Mock).Build()

			var getCommentRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetComment, mockey.OptUnsafe).Return(getCommentRet1Mock).Build()

			var getCiStatusRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetCiStatus, mockey.OptUnsafe).Return(getCiStatusRet1Mock).Build()

			var getConflictRet1Mock bool
			mockey.Mock((*dev.GetDevContributionStatusOptions).GetConflict, mockey.OptUnsafe).Return(getConflictRet1Mock).Build()

			var getChangeDetailRet1Mock gresult.R[int]
			mockey.Mock(GetChangeDetail).Return(getChangeDetailRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var contributionIDs []int64
			var optionsPtrValue dev.GetDevContributionStatusOptions
			options := &optionsPtrValue
			convey.So(func() { _ = GetDevContributionStatus(ctx, contributionIDs, options) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetMergingGroupsAutoGen(t *testing.T) {
	// len(code.byted.org/lang/gg/gslice:Filter()_ret-1) != 0
	// param3 != nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.GetStackedChangesHelper:SetSkipDivergeCommitCheck()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2:NewGetStackedChangesHelper()_ret-1 != nil
	// len(code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.StackedChanges:ToChangeDetails()_ret-1) > 0
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.StackedChanges:ToChangeDetails()_ret-1[0] != nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2.StackedChanges:IsEmpty()_ret-1 == true
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			isEmptyRet1Mock := true
			mockey.Mock(StackedChanges.IsEmpty, mockey.OptUnsafe).Return(isEmptyRet1Mock).Build()

			var getStackedChangesHelperMockPtrValue GetStackedChangesHelper
			getStackedChangesHelperMock := &getStackedChangesHelperMockPtrValue
			mockey.Mock((*GetStackedChangesHelper).SetSkipDivergeCommitCheck, mockey.OptUnsafe).Return(getStackedChangesHelperMock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetContributionMergeTask).Return(rMock).Build()

			var newGetStackedChangesHelperRet1MockPtrValue GetStackedChangesHelper
			newGetStackedChangesHelperRet1Mock := &newGetStackedChangesHelperRet1MockPtrValue
			mockey.Mock(NewGetStackedChangesHelper, mockey.OptUnsafe).Return(newGetStackedChangesHelperRet1Mock).Build()

			toChangeDetailsRet1Mock := []*ChangeDetail{}
			var toChangeDetailsRet1Mock0ItemPtrValueCodeChange change_entity.CodeChange
			toChangeDetailsRet1Mock0ItemPtrValue := ChangeDetail{CodeChange: &toChangeDetailsRet1Mock0ItemPtrValueCodeChange}
			toChangeDetailsRet1Mock0Item := &toChangeDetailsRet1Mock0ItemPtrValue
			toChangeDetailsRet1Mock = append(toChangeDetailsRet1Mock, toChangeDetailsRet1Mock0Item)
			mockey.Mock(StackedChanges.ToChangeDetails, mockey.OptUnsafe).Return(toChangeDetailsRet1Mock).Build()

			var filterChangesInDownsideMergingGroupRet1Mock []*ChangeDetail
			mockey.Mock(filterChangesInDownsideMergingGroup).Return(filterChangesInDownsideMergingGroupRet1Mock).Build()

			var stackedChangesMock StackedChanges
			var buildStackedChangesByTrunkBranchRet2Mock []*ChangeDetail
			mockey.Mock((*GetStackedChangesHelper).BuildStackedChangesByTrunkBranch).Return(stackedChangesMock, buildStackedChangesByTrunkBranchRet2Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var downStackChangesRet1Mock StackedChanges
			mockey.Mock(StackedChanges.DownStackChanges).Return(downStackChangesRet1Mock).Build()

			var getDevTaskWithOptRet1Mock gresult.R[int]
			mockey.Mock(dev_task_entity.GetDevTaskWithOpt).Return(getDevTaskWithOptRet1Mock).Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var paramsPtrValue GroupParams
			params := &paramsPtrValue
			convey.So(func() { _ = GetMergingGroups(ctx, changes, params) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetMultiMergedGroupsAutoGen(t *testing.T) {
	// Verify the functionality of GetMultiMergedGroups function.
	t.Run("testGetMultiMergedGroups", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getStackedChangesHelperMockPtrValue GetStackedChangesHelper
			getStackedChangesHelperMock := &getStackedChangesHelperMockPtrValue
			mockey.Mock(NewGetStackedChangesHelper, mockey.OptUnsafe).Return(getStackedChangesHelperMock).Build()

			var isEmptyRet1Mock bool
			mockey.Mock(StackedChanges.IsEmpty, mockey.OptUnsafe).Return(isEmptyRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(dev_task_entity.GetDevTaskWithOpt).Return(rMock).Build()

			var setSkipDivergeCommitCheckRet1MockPtrValue GetStackedChangesHelper
			setSkipDivergeCommitCheckRet1Mock := &setSkipDivergeCommitCheckRet1MockPtrValue
			mockey.Mock((*GetStackedChangesHelper).SetSkipDivergeCommitCheck, mockey.OptUnsafe).Return(setSkipDivergeCommitCheckRet1Mock).Build()

			var toChangeDetailsRet1Mock []*ChangeDetail
			mockey.Mock(StackedChanges.ToChangeDetails, mockey.OptUnsafe).Return(toChangeDetailsRet1Mock).Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var stackedChangesMock StackedChanges
			mockey.Mock((*GetStackedChangesHelper).GetStackedChangesBasedOnSpecifiedChange).Return(stackedChangesMock).Build()

			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var paramsPtrValue GroupParams
			params := &paramsPtrValue
			convey.So(func() { _ = GetMultiMergedGroups(ctx, changes, params) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetOpenedGroupsAutoGen(t *testing.T) {
	// Verify the function behavior under default condition.
	t.Run("testGetOpenedGroups_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var filterChangesByStatusRet1Mock []*ChangeDetail
			mockey.Mock(filterChangesByStatus).Return(filterChangesByStatusRet1Mock).Build()

			// prepare parameters
			var changes []*ChangeDetail
			var paramsPtrValue GroupParams
			params := &paramsPtrValue
			ctx := context.Background()
			convey.So(func() { _ = GetOpenedGroups(ctx, changes, params) }, convey.ShouldNotPanic)
		})
	})

}

func Test_filterChangesNotInChangeMergeTaskAutoGen(t *testing.T) {
	// len(param2) > 0
	// param2[0].CodeChange != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*change_entity.CodeChange).GetMergeTask).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var changesItem0PtrValueCodeChange change_entity.CodeChange
			changesItem0PtrValue := ChangeDetail{CodeChange: &changesItem0PtrValueCodeChange}
			changesItem0 := &changesItem0PtrValue
			changes := []*ChangeDetail{changesItem0,}
			var changes0CodeChangePtrValue change_entity.CodeChange
			changes[0].CodeChange = &changes0CodeChangePtrValue
			convey.So(func() { _ = filterChangesNotInChangeMergeTask(ctx, changes) }, convey.ShouldPanic)
		})
	})

	// default condition
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var rMock gresult.R[int]
			mockey.Mock((*change_entity.CodeChange).GetMergeTask).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail

			// run target function and assert
			got1 := filterChangesNotInChangeMergeTask(ctx, changes)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func TestGetClosedGroupsAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var filterChangesByStatusRet1Mock []*ChangeDetail
			mockey.Mock(filterChangesByStatus).Return(filterChangesByStatusRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var paramsPtrValue GroupParams
			params := &paramsPtrValue
			convey.So(func() { _ = GetClosedGroups(ctx, changes, params) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetMultiStackGroupsAutoGen(t *testing.T) {
	// Verify the functionality of GetMultiStackGroups function.
	t.Run("testGetMultiStackGroups", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stackedChangesMock StackedChanges
			var buildStackedChangesByTrunkBranchRet2Mock []*ChangeDetail
			mockey.Mock((*GetStackedChangesHelper).BuildStackedChangesByTrunkBranch).Return(stackedChangesMock, buildStackedChangesByTrunkBranchRet2Mock).Build()

			var getStackedChangesHelperMockPtrValue GetStackedChangesHelper
			getStackedChangesHelperMock := &getStackedChangesHelperMockPtrValue
			mockey.Mock(NewGetStackedChangesHelper, mockey.OptUnsafe).Return(getStackedChangesHelperMock).Build()

			var setUseCacheRet1MockPtrValue GetStackedChangesHelper
			setUseCacheRet1Mock := &setUseCacheRet1MockPtrValue
			mockey.Mock((*GetStackedChangesHelper).SetUseCache, mockey.OptUnsafe).Return(setUseCacheRet1Mock).Build()

			var toChangeDetailsRet1Mock []*ChangeDetail
			mockey.Mock(StackedChanges.ToChangeDetails, mockey.OptUnsafe).Return(toChangeDetailsRet1Mock).Build()

			var filterChangesNotInChangeMergeTaskRet1Mock []*ChangeDetail
			mockey.Mock(filterChangesNotInChangeMergeTask).Return(filterChangesNotInChangeMergeTaskRet1Mock).Build()

			// prepare parameters
			var paramsPtrValue GroupParams
			params := &paramsPtrValue
			var paramsTrunkBranchPtrValue string
			params.TrunkBranch = &paramsTrunkBranchPtrValue
			ctx := context.Background()
			var changes []*ChangeDetail
			convey.So(func() { _ = GetMultiStackGroups(ctx, changes, params) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetStackGroupsAutoGen(t *testing.T) {
	// Verify the functionality of GetStackGroups function.
	t.Run("testGetStackGroups", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var filterChangesNotInChangeMergeTaskRet1Mock []*ChangeDetail
			mockey.Mock(filterChangesNotInChangeMergeTask).Return(filterChangesNotInChangeMergeTaskRet1Mock).Build()

			var getStackedChangesHelperMockPtrValue GetStackedChangesHelper
			getStackedChangesHelperMock := &getStackedChangesHelperMockPtrValue
			mockey.Mock((*GetStackedChangesHelper).SetUseCache, mockey.OptUnsafe).Return(getStackedChangesHelperMock).Build()

			var newGetStackedChangesHelperRet1MockPtrValue GetStackedChangesHelper
			newGetStackedChangesHelperRet1Mock := &newGetStackedChangesHelperRet1MockPtrValue
			mockey.Mock(NewGetStackedChangesHelper, mockey.OptUnsafe).Return(newGetStackedChangesHelperRet1Mock).Build()

			var stackedChangesMock StackedChanges
			var buildStackedChangesByTrunkBranchRet2Mock []*ChangeDetail
			mockey.Mock((*GetStackedChangesHelper).BuildStackedChangesByTrunkBranch).Return(stackedChangesMock, buildStackedChangesByTrunkBranchRet2Mock).Build()

			var toChangeDetailsRet1Mock []*ChangeDetail
			mockey.Mock(StackedChanges.ToChangeDetails, mockey.OptUnsafe).Return(toChangeDetailsRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var paramsPtrValue GroupParams
			params := &paramsPtrValue
			convey.So(func() { _ = GetStackGroups(ctx, changes, params) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetMergedGroupsAutoGen(t *testing.T) {
	// Verify the function behavior under default condition.
	t.Run("testGetMergedGroups_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var filterChangesByStatusRet1Mock []*ChangeDetail
			mockey.Mock(filterChangesByStatus).Return(filterChangesByStatusRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var changes []*ChangeDetail
			var paramsPtrValue GroupParams
			params := &paramsPtrValue
			convey.So(func() { _ = GetMergedGroups(ctx, changes, params) }, convey.ShouldNotPanic)
		})
	})

}

func Test_updateStackedContributionItemsMergeStatusAutoGen(t *testing.T) {
	// Verify the function behavior when PartialMergeEnabled is true.
	t.Run("testUpdateStackedContributionItemsMergeStatus_PartialMergeEnabledTrue", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var contributionMergeStatusMock dev.ContributionMergeStatus
			mockey.Mock((*dev.ContributionCodeChangeListItem).GetMergeStatus, mockey.OptUnsafe).Return(contributionMergeStatusMock).Build()

			var checkIfChangeItemCanTriggerMergeRet1Mock bool
			mockey.Mock(checkIfChangeItemCanTriggerMerge).Return(checkIfChangeItemCanTriggerMergeRet1Mock).Build()

			// prepare parameters
			var items []*dev.ContributionCodeChangeListItem
			var processMode string
			var bizConfigPtrValue biz_config.DevBizConfig
			bizConfig := &bizConfigPtrValue
			bizConfig.PartialMergeEnabled = true

			// run target function and assert
			got1 := updateStackedContributionItemsMergeStatus(items, processMode, bizConfig)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior when PartialMergeEnabled is false.
	t.Run("testUpdateStackedContributionItemsMergeStatus_PartialMergeEnabledFalse", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var checkIfChangeItemCanTriggerMergeRet1Mock bool
			mockey.Mock(checkIfChangeItemCanTriggerMerge).Return(checkIfChangeItemCanTriggerMergeRet1Mock).Build()

			var contributionMergeStatusMock dev.ContributionMergeStatus
			mockey.Mock((*dev.ContributionCodeChangeListItem).GetMergeStatus, mockey.OptUnsafe).Return(contributionMergeStatusMock).Build()

			// prepare parameters
			var items []*dev.ContributionCodeChangeListItem
			var processMode string
			var bizConfigPtrValue biz_config.DevBizConfig
			bizConfig := &bizConfigPtrValue
			bizConfig.PartialMergeEnabled = false

			// run target function and assert
			got1 := updateStackedContributionItemsMergeStatus(items, processMode, bizConfig)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

func Test_filterChangesByStatusAutoGen(t *testing.T) {
	// Verify that the function returns a non-empty result when filtering changes by status.
	t.Run("testFilterChangesByStatus_NotEmptyResult", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeStatus.String).Return(stringRet1Mock).Build()

			// prepare parameters
			var changes []*ChangeDetail
			var status dev.ContributionCodeChangeStatus

			// run target function and assert
			got1 := filterChangesByStatus(changes, status)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

