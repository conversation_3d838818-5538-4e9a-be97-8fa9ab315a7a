package small_mr_v2

import (
	"context"
	"strings"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/code_review"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"
)

func Test_buildDevLarkGroupReviewRemindMsgAutoGen(t *testing.T) {
	// param4 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var withSuffixRet1Mock string
			mockey.Mock(emails.WithSuffix).Return(withSuffixRet1Mock).Build()

			var getDevBasicUrlRet1Mock string
			mockey.Mock(utils.GetDevBasicUrl).Return(getDevBasicUrlRet1Mock).Build()

			var joinRet1Mock string
			mockey.Mock(strings.Join).Return(joinRet1Mock).Build()

			var getRemindModeRet1Mock string
			mockey.Mock(code_review.GetRemindMode).Return(getRemindModeRet1Mock).Build()

			var getContributionCodeChangeUrlRet1Mock string
			mockey.Mock(utils.GetContributionCodeChangeUrl).Return(getContributionCodeChangeUrlRet1Mock).Build()

			var getWipRet1Mock bool
			mockey.Mock((*git_server.CodeChangeGitlabMergeRequest).GetWip, mockey.OptUnsafe).Return(getWipRet1Mock).Build()

			var getNeedRemindReviewUsersRet1Mock []string
			mockey.Mock(code_review.GetNeedRemindReviewUsers).Return(getNeedRemindReviewUsersRet1Mock).Build()

			// prepare parameters
			var devBasicPtrValue model.DevBasicInfo
			devBasic := &devBasicPtrValue
			ctx := context.Background()
			var changes []*ChangeDetail
			var operator string

			// run target function and assert
			got1 := buildDevLarkGroupReviewRemindMsg(ctx, changes, operator, devBasic)
			convey.So(len(got1), convey.ShouldEqual, 4)
		})
	})

	// param4 != nil
	// len(param2) > 0
	// param2[0].ReviewInfo == nil
	// param2[0] != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getNeedRemindReviewUsersRet1Mock []string
			mockey.Mock(code_review.GetNeedRemindReviewUsers).Return(getNeedRemindReviewUsersRet1Mock).Build()

			var withSuffixRet1Mock string
			mockey.Mock(emails.WithSuffix).Return(withSuffixRet1Mock).Build()

			var getDevBasicUrlRet1Mock string
			mockey.Mock(utils.GetDevBasicUrl).Return(getDevBasicUrlRet1Mock).Build()

			var joinRet1Mock string
			mockey.Mock(strings.Join).Return(joinRet1Mock).Build()

			var getRemindModeRet1Mock string
			mockey.Mock(code_review.GetRemindMode).Return(getRemindModeRet1Mock).Build()

			var getContributionCodeChangeUrlRet1Mock string
			mockey.Mock(utils.GetContributionCodeChangeUrl).Return(getContributionCodeChangeUrlRet1Mock).Build()

			var getWipRet1Mock bool
			mockey.Mock((*git_server.CodeChangeGitlabMergeRequest).GetWip, mockey.OptUnsafe).Return(getWipRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			changes := []*ChangeDetail{}
			var changes0ItemPtrValueCodeChange change_entity.CodeChange
			changes0ItemPtrValue := ChangeDetail{CodeChange: &changes0ItemPtrValueCodeChange}
			changes0Item := &changes0ItemPtrValue
			changes = append(changes, changes0Item)
			var operator string
			var devBasicPtrValue model.DevBasicInfo
			devBasic := &devBasicPtrValue

			// run target function and assert
			got1 := buildDevLarkGroupReviewRemindMsg(ctx, changes, operator, devBasic)
			convey.So(len(got1), convey.ShouldEqual, 4)
		})
	})

}

func Test_buildDismissLarkGroupCardDataAutoGen(t *testing.T) {
	// Verify the function behavior when all mocks are set and a non-null parameter is provided.
	t.Run("testBuildDismissLarkGroupCardData", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getDevBasicUrlRet1Mock string
			mockey.Mock(utils.GetDevBasicUrl).Return(getDevBasicUrlRet1Mock).Build()

			var getBitsBaseUrlRet1Mock string
			mockey.Mock(functools.GetBitsBaseUrl, mockey.OptUnsafe).Return(getBitsBaseUrlRet1Mock).Build()

			// prepare parameters
			var devBasicPtrValue model.DevBasicInfo
			devBasic := &devBasicPtrValue

			// run target function and assert
			got1 := buildDismissLarkGroupCardData(devBasic)
			convey.So(len(got1), convey.ShouldEqual, 4)
		})
	})

}

func Test_buildDevLarkGroupTestRemindMsgAutoGen(t *testing.T) {
	// Verify the buildDevLarkGroupTestRemindMsg function with given parameters.
	t.Run("testBuildDevLarkGroupTestRemindMsg", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var withSuffixRet1Mock string
			mockey.Mock(emails.WithSuffix).Return(withSuffixRet1Mock).Build()

			var formDevTaskSmrTestUrlRet1Mock string
			mockey.Mock(functools.FormDevTaskSmrTestUrl, mockey.OptUnsafe).Return(formDevTaskSmrTestUrlRet1Mock).Build()

			// prepare parameters
			var qaTestersItem0 string
			qaTesters := []string{qaTestersItem0,}
			var operator string
			var devBasicPtrValue model.DevBasicInfo
			devBasic := &devBasicPtrValue

			// run target function and assert
			got1 := buildDevLarkGroupTestRemindMsg(qaTesters, operator, devBasic)
			convey.So(len(got1), convey.ShouldEqual, 3)
		})
	})

}

func Test_buildDevUserReviewRemindMsgDataAutoGen(t *testing.T) {
	// param4 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var joinRet1Mock string
			mockey.Mock(strings.Join).Return(joinRet1Mock).Build()

			var getContributionCodeChangeUrlRet1Mock string
			mockey.Mock(utils.GetContributionCodeChangeUrl).Return(getContributionCodeChangeUrlRet1Mock).Build()

			var getDevBasicUrlRet1Mock string
			mockey.Mock(utils.GetDevBasicUrl).Return(getDevBasicUrlRet1Mock).Build()

			var withSuffixRet1Mock string
			mockey.Mock(emails.WithSuffix).Return(withSuffixRet1Mock).Build()

			// prepare parameters
			var changes []*ChangeDetail
			var reviewer string
			var operator string
			var devBasicPtrValue model.DevBasicInfo
			devBasic := &devBasicPtrValue

			// run target function and assert
			got1 := buildDevUserReviewRemindMsgData(changes, reviewer, operator, devBasic)
			convey.So(len(got1), convey.ShouldEqual, 6)
		})
	})

}

