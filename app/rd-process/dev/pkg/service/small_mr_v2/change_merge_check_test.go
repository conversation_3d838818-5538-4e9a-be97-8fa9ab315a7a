package small_mr_v2

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
)

func TestParallelMergeCheck_RunAutoGen(t *testing.T) {
	// param3 == 1
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeMergeOption.String).Return(stringRet1Mock).Build()

			var withContextRet1MockPtrValue errgroup.Group
			withContextRet1Mock := &withContextRet1MockPtrValue
			withContextRet2Mock := context.Background()
			mockey.Mock(errgroup.WithContext, mockey.OptUnsafe).Return(withContextRet1Mock, withContextRet2Mock).Build()

			// prepare parameters
			var changes []*ChangeDetail
			mergeOptionAlias := int64(1)
			mergeOption := dev.ContributionCodeChangeMergeOption(mergeOptionAlias)
			var receiverPtrValue ParallelMergeCheck
			receiver := &receiverPtrValue
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.Run(ctx, changes, mergeOption)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Succeeded, convey.ShouldEqual, true)
			convey.So((*got1).Msg, convey.ShouldBeBlank)
			convey.So(len((*got1).FailedChanges), convey.ShouldEqual, 0)
		})
	})

	// param3 != 1
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeMergeOption.String).Return(stringRet1Mock).Build()

			var withContextRet1MockPtrValue errgroup.Group
			withContextRet1Mock := &withContextRet1MockPtrValue
			withContextRet2Mock := context.Background()
			mockey.Mock(errgroup.WithContext, mockey.OptUnsafe).Return(withContextRet1Mock, withContextRet2Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue ParallelMergeCheck
			receiver := &receiverPtrValue
			ctx := context.Background()
			var changes []*ChangeDetail
			mergeOptionAlias := int64(2)
			mergeOption := dev.ContributionCodeChangeMergeOption(mergeOptionAlias)

			// run target function and assert
			got1 := receiver.Run(ctx, changes, mergeOption)
			convey.So((*got1).Msg, convey.ShouldBeBlank)
			convey.So(len((*got1).FailedChanges), convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Succeeded, convey.ShouldEqual, true)
		})
	})

	// param3 != 1
	// len(param2) > 0
	// len(param2) != 0
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeMergeOption.String).Return(stringRet1Mock).Build()

			var withContextRet1MockPtrValue errgroup.Group
			withContextRet1Mock := &withContextRet1MockPtrValue
			withContextRet2Mock := context.Background()
			mockey.Mock(errgroup.WithContext, mockey.OptUnsafe).Return(withContextRet1Mock, withContextRet2Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			// prepare parameters
			var receiverPtrValue ParallelMergeCheck
			receiver := &receiverPtrValue
			ctx := context.Background()
			var changesItem0PtrValueCodeChange change_entity.CodeChange
			changesItem0PtrValue := ChangeDetail{CodeChange: &changesItem0PtrValueCodeChange}
			changesItem0 := &changesItem0PtrValue
			changes := []*ChangeDetail{changesItem0,}
			mergeOptionAlias := int64(2)
			mergeOption := dev.ContributionCodeChangeMergeOption(mergeOptionAlias)

			// run target function and assert
			got1 := receiver.Run(ctx, changes, mergeOption)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Succeeded, convey.ShouldEqual, true)
			convey.So((*got1).Msg, convey.ShouldBeBlank)
			convey.So(len((*got1).FailedChanges), convey.ShouldEqual, 0)
		})
	})

	// param3 != 1
	// golang.org/x/sync/errgroup.Group:Wait()_ret-1 != nil
	// golang.org/x/sync/errgroup:WithContext()_ret-1 != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			waitRet1Mock := fmt.Errorf("error")
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeMergeOption.String).Return(stringRet1Mock).Build()

			var withContextRet1MockPtrValue errgroup.Group
			withContextRet1Mock := &withContextRet1MockPtrValue
			withContextRet2Mock := context.Background()
			mockey.Mock(errgroup.WithContext, mockey.OptUnsafe).Return(withContextRet1Mock, withContextRet2Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			// prepare parameters
			var receiverPtrValue ParallelMergeCheck
			receiver := &receiverPtrValue
			ctx := context.Background()
			var changes []*ChangeDetail
			mergeOptionAlias := int64(2)
			mergeOption := dev.ContributionCodeChangeMergeOption(mergeOptionAlias)

			// run target function and assert
			got1 := receiver.Run(ctx, changes, mergeOption)
			convey.So(len((*got1).FailedChanges), convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Succeeded, convey.ShouldEqual, false)
			convey.So((*got1).Msg, convey.ShouldEqual, "error")
		})
	})

}

func TestSmrChangeCanMergeChecksAutoGen(t *testing.T) {
	// param3 == 1
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var codeReviewMockPtrValue change_check.CodeReview
			codeReviewMock := &codeReviewMockPtrValue
			mockey.Mock(change_check.NewCheckItemCodeReview, mockey.OptUnsafe).Return(codeReviewMock).Build()

			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeMergeOption.String).Return(stringRet1Mock).Build()

			var ciCheckMockPtrValue change_check.CiCheck
			ciCheckMock := &ciCheckMockPtrValue
			mockey.Mock(change_check.NewCheckItemCiCheck, mockey.OptUnsafe).Return(ciCheckMock).Build()

			var ret11T1PtrValue change_check.CheckItemResult
			ret11T1 := &ret11T1PtrValue
			mockey.Mock((*change_check.ConflictDetect).GetResult).Return(ret11T1).Build()

			var checkItemResultMockPtrValue change_check.CheckItemResult
			checkItemResultMock := &checkItemResultMockPtrValue
			mockey.Mock((*change_check.CiCheck).GetResult).Return(checkItemResultMock).Build()

			var conflictDetectMockPtrValue change_check.ConflictDetect
			conflictDetectMock := &conflictDetectMockPtrValue
			mockey.Mock(change_check.NewCheckItemConflictDetect, mockey.OptUnsafe).Return(conflictDetectMock).Build()

			var getResultRet1MockPtrValue change_check.CheckItemResult
			getResultRet1Mock := &getResultRet1MockPtrValue
			mockey.Mock((*change_check.CodeReview).GetResult).Return(getResultRet1Mock).Build()

			var ret9T1PtrValue change_check.CheckItemResult
			ret9T1 := &ret9T1PtrValue
			mockey.Mock((*change_check.Wip).GetResult).Return(ret9T1).Build()

			var wipMockPtrValue change_check.Wip
			wipMock := &wipMockPtrValue
			mockey.Mock(change_check.NewCheckItemWip, mockey.OptUnsafe).Return(wipMock).Build()

			// prepare parameters
			mergeOptionAlias := int64(1)
			mergeOption := dev.ContributionCodeChangeMergeOption(mergeOptionAlias)
			ctx := context.Background()
			var changePtrValueCodeChange change_entity.CodeChange
			changePtrValue := ChangeDetail{CodeChange: &changePtrValueCodeChange}
			change := &changePtrValue

			// run target function and assert
			got1 := SmrChangeCanMergeChecks(ctx, change, mergeOption)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3 != 1
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check:NewCheckItemWip()_ret-1 != nil
	// param2 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check.Wip:GetResult()_ret-1.Pass == true
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check:NewCheckItemCiCheck()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check.CiCheck:GetResult()_ret-1.Pass != true
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check.CiCheck:GetResult()_ret-1 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var codeReviewMockPtrValue change_check.CodeReview
			codeReviewMock := &codeReviewMockPtrValue
			mockey.Mock(change_check.NewCheckItemCodeReview, mockey.OptUnsafe).Return(codeReviewMock).Build()

			var ret11T1PtrValue change_check.CheckItemResult
			ret11T1 := &ret11T1PtrValue
			mockey.Mock((*change_check.ConflictDetect).GetResult).Return(ret11T1).Build()

			var ciCheckMockPtrValue change_check.CiCheck
			ciCheckMock := &ciCheckMockPtrValue
			mockey.Mock(change_check.NewCheckItemCiCheck, mockey.OptUnsafe).Return(ciCheckMock).Build()

			var conflictDetectMockPtrValue change_check.ConflictDetect
			conflictDetectMock := &conflictDetectMockPtrValue
			mockey.Mock(change_check.NewCheckItemConflictDetect, mockey.OptUnsafe).Return(conflictDetectMock).Build()

			var ret9T1PtrValue change_check.CheckItemResult
			ret9T1 := &ret9T1PtrValue
			ret9T1.Pass = true
			mockey.Mock((*change_check.Wip).GetResult).Return(ret9T1).Build()

			var wipMockPtrValue change_check.Wip
			wipMock := &wipMockPtrValue
			mockey.Mock(change_check.NewCheckItemWip, mockey.OptUnsafe).Return(wipMock).Build()

			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeMergeOption.String).Return(stringRet1Mock).Build()

			var getResultRet1MockPtrValue change_check.CheckItemResult
			getResultRet1Mock := &getResultRet1MockPtrValue
			mockey.Mock((*change_check.CodeReview).GetResult).Return(getResultRet1Mock).Build()

			var checkItemResultMockPtrValue change_check.CheckItemResult
			checkItemResultMock := &checkItemResultMockPtrValue
			checkItemResultMock.Pass = false
			mockey.Mock((*change_check.CiCheck).GetResult).Return(checkItemResultMock).Build()

			// prepare parameters
			ctx := context.Background()
			var changePtrValueCodeChange change_entity.CodeChange
			changePtrValue := ChangeDetail{CodeChange: &changePtrValueCodeChange}
			change := &changePtrValue
			mergeOptionAlias := int64(2)
			mergeOption := dev.ContributionCodeChangeMergeOption(mergeOptionAlias)
			convey.So(func() { _ = SmrChangeCanMergeChecks(ctx, change, mergeOption) }, convey.ShouldPanic)
		})
	})

}

func TestNewParallelMergeCheckAutoGen(t *testing.T) {
	// Verify the function behavior under default conditions.
	t.Run("testNewParallelMergeCheck_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			check := func(context.Context, *ChangeDetail, dev.ContributionCodeChangeMergeOption)error{
				var checkRet1 error
				return checkRet1
			}

			// run target function and assert
			got1 := NewParallelMergeCheck(check)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestChangesMergeChecksQueue_FullRunAutoGen(t *testing.T) {
	// Verify the functionality of the FullRun method in the ChangesMergeChecksQueue.
	t.Run("testChangesMergeChecksQueue_FullRun", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var changesMergeCheckResultMockPtrValue ChangesMergeCheckResult
			changesMergeCheckResultMock := &changesMergeCheckResultMockPtrValue
			mockey.Mock((*ParallelMergeCheck).Run).Return(changesMergeCheckResultMock).Build()

			// prepare parameters
			var receiverPtrValue ChangesMergeChecksQueue
			receiver := &receiverPtrValue
			receiver.Checks = []ChangesMergeCheck{}
			var receiverChecks0ItemImpl ParallelMergeCheck
			receiverChecks0Item := &receiverChecks0ItemImpl
			receiver.Checks = append(receiver.Checks, receiverChecks0Item)
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.FullRun(ctx)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Succeeded, convey.ShouldEqual, false)
			convey.So(len((*got1).ChecksRes), convey.ShouldEqual, 1)
		})
	})

}

func TestNewBundledMergeCheckAutoGen(t *testing.T) {
	// Verify the function behavior under default condition.
	t.Run("testNewBundledMergeCheck_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			check := func(context.Context, []*ChangeDetail, dev.ContributionCodeChangeMergeOption)error{
				var checkRet1 error
				return checkRet1
			}

			// run target function and assert
			got1 := NewBundledMergeCheck(check)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestChangesMergeChecksQueue_RunAutoGen(t *testing.T) {
	// Verify the functionality of the ChangesMergeChecksQueue Run method.
	t.Run("testChangesMergeChecksQueue_Run", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var changesMergeCheckResultMockPtrValue ChangesMergeCheckResult
			changesMergeCheckResultMock := &changesMergeCheckResultMockPtrValue
			mockey.Mock((*ParallelMergeCheck).Run).Return(changesMergeCheckResultMock).Build()

			// prepare parameters
			var receiverPtrValue ChangesMergeChecksQueue
			receiver := &receiverPtrValue
			receiver.Checks = []ChangesMergeCheck{}
			var receiverChecks0ItemImpl ParallelMergeCheck
			receiverChecks0Item := &receiverChecks0ItemImpl
			receiver.Checks = append(receiver.Checks, receiverChecks0Item)
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.Run(ctx)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Succeeded, convey.ShouldEqual, false)
			convey.So((*got1).Msg, convey.ShouldBeBlank)
			convey.So(len((*got1).FailedChanges), convey.ShouldEqual, 0)
		})
	})

}

func TestNewChangesMergeChecksQueueAutoGen(t *testing.T) {
	// Verify the behavior of the function under default conditions.
	t.Run("testNewChangesMergeChecksQueue_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var changes []*ChangeDetail
			var mergeOption dev.ContributionCodeChangeMergeOption

			// run target function and assert
			got1 := NewChangesMergeChecksQueue(changes, mergeOption)
			convey.So(len((*got1).Checks), convey.ShouldEqual, 0)
			convey.So(len((*got1).Changes), convey.ShouldEqual, 0)
			convey.So(int64((*got1).MergeOption), convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestBundledMergeCheck_RunAutoGen(t *testing.T) {
	// Verify the successful execution of the Run function when merge option is valid.
	t.Run("testBundledMergeCheck_Run_Success", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeMergeOption.String).Return(stringRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue BundledMergeCheck
			receiver := &receiverPtrValue
			ctx := context.Background()
			var changes []*ChangeDetail
			mergeOptionAlias := int64(1)
			mergeOption := dev.ContributionCodeChangeMergeOption(mergeOptionAlias)

			// run target function and assert
			got1 := receiver.Run(ctx, changes, mergeOption)
			convey.So((*got1).Succeeded, convey.ShouldEqual, true)
			convey.So((*got1).Msg, convey.ShouldBeBlank)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).FailedChanges), convey.ShouldEqual, 0)
		})
	})

	// Verify the panic behavior of the Run function when merge option is invalid.
	t.Run("testBundledMergeCheck_Run_Failure", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeMergeOption.String).Return(stringRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue BundledMergeCheck
			receiver := &receiverPtrValue
			ctx := context.Background()
			var changes []*ChangeDetail
			mergeOptionAlias := int64(2)
			mergeOption := dev.ContributionCodeChangeMergeOption(mergeOptionAlias)
			convey.So(func() { _ = receiver.Run(ctx, changes, mergeOption) }, convey.ShouldPanic)
		})
	})

}

