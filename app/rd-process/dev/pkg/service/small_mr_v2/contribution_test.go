package small_mr_v2

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"golang.org/x/sync/errgroup"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/engine"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_check"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/change_entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/utils"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/mq/producer"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	utils2 "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/lang/gg/gresult"
)

func Test_mUpdateDevChangeAutoGen(t *testing.T) {
	// param2.Change != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var contributionCodeChangeUpdateOptionsMockPtrValue dev.ContributionCodeChangeUpdateOptions
			contributionCodeChangeUpdateOptionsMock := &contributionCodeChangeUpdateOptionsMockPtrValue
			mockey.Mock((*dev.UpdateDevChange).GetOptions, mockey.OptUnsafe).Return(contributionCodeChangeUpdateOptionsMock).Build()

			var getContributionIDRet1Mock int64
			mockey.Mock((*dev.UpdateDevChange).GetContributionID, mockey.OptUnsafe).Return(getContributionIDRet1Mock).Build()

			var updateContributionCodeChangeRet1Mock error
			mockey.Mock(UpdateContributionCodeChange).Return(updateContributionCodeChangeRet1Mock).Build()

			// prepare parameters
			var inputPtrValue mUpdateDevChangeRequest
			input := &inputPtrValue
			var inputChangePtrValue dev.UpdateDevChange
			input.Change = &inputChangePtrValue
			ctx := context.Background()

			// run target function and assert
			got1, got2 := mUpdateDevChange(ctx, input)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeTrue)
		})
	})

	// param2.Change != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2:UpdateContributionCodeChange()_ret-1 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getContributionIDRet1Mock int64
			mockey.Mock((*dev.UpdateDevChange).GetContributionID, mockey.OptUnsafe).Return(getContributionIDRet1Mock).Build()

			updateContributionCodeChangeRet1Mock := fmt.Errorf("error")
			mockey.Mock(UpdateContributionCodeChange).Return(updateContributionCodeChangeRet1Mock).Build()

			var contributionCodeChangeUpdateOptionsMockPtrValue dev.ContributionCodeChangeUpdateOptions
			contributionCodeChangeUpdateOptionsMock := &contributionCodeChangeUpdateOptionsMockPtrValue
			mockey.Mock((*dev.UpdateDevChange).GetOptions, mockey.OptUnsafe).Return(contributionCodeChangeUpdateOptionsMock).Build()

			// prepare parameters
			ctx := context.Background()
			var inputPtrValue mUpdateDevChangeRequest
			input := &inputPtrValue
			var inputChangePtrValue dev.UpdateDevChange
			input.Change = &inputChangePtrValue

			// run target function and assert
			got1, got2 := mUpdateDevChange(ctx, input)
			convey.So(got1 == nil, convey.ShouldBeTrue)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestGetContributionStatusInfoAutoGen(t *testing.T) {
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis:Get()_ret-1 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data.Repo:UseCache()_ret-1 != nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	// code.byted.org/lang/gg/gresult.R:Get()_ret-1.CodeChangeId != 0
	// GitServerClientGlobal != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server/gitservice.Client:GetCodeChangeDetail()_ret-2 == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server/gitservice.Client:GetCodeChangeDetail()_ret-1.Detail.GitlabMr == nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server/gitservice.Client:GetCodeChangeDetail()_ret-1.Detail != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server/gitservice.Client:GetCodeChangeDetail()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var checkConflictByCodeChangeIdRet1Mock gresult.R[int]
			mockey.Mock(gitsvr.CheckConflictByCodeChangeId).Return(checkConflictByCodeChangeIdRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var gitServerClientMockImpl gitserviceClientImplForTestAutoGen
			gitServerClientMock := &gitServerClientMockImpl
			mockey.MockValue(&rpc.GitServerClient).To(gitServerClientMock)

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetContributionCodeChangeByID).Return(rMock).Build()

			var getCodeChangeDetaileResponseMockPtrValue git_server.GetCodeChangeDetaileResponse
			getCodeChangeDetaileResponseMock := &getCodeChangeDetaileResponseMockPtrValue
			var getCodeChangeDetaileResponseMockDetailPtrValue git_server.CodeChangeDetail
			getCodeChangeDetaileResponseMock.Detail = &getCodeChangeDetaileResponseMockDetailPtrValue
			var getCodeChangeDetailRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.GitServerClient, "GetCodeChangeDetail"), mockey.OptUnsafe).Return(getCodeChangeDetaileResponseMock, getCodeChangeDetailRet2Mock).Build()

			var compareRefResponseMockPtrValue git_server.CompareRefResponse
			compareRefResponseMock := &compareRefResponseMockPtrValue
			var compareRefRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.GitServerClient, "CompareRef"), mockey.OptUnsafe).Return(compareRefResponseMock, compareRefRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var cid int64
			convey.So(func() { _ = GetContributionStatusInfo(ctx, cid) }, convey.ShouldPanic)

			// post-task, e.g. wait for the go func to finish during mocks effect
			time.Sleep(500 * time.Microsecond)
		})
	})

}

func TestGetContributionCodeChangeDependencyDetailAutoGen(t *testing.T) {
	// Verify the behavior of the function when mocked dependencies return specific values and it should panic.
	t.Run("testGetContributionCodeChangeDependencyDetail", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var gitServerClientMockImpl gitserviceClientImplForTestAutoGen
			gitServerClientMock := &gitServerClientMockImpl
			mockey.MockValue(&rpc.GitServerClient).To(gitServerClientMock)

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetContributionCodeChangeByID).Return(rMock).Build()

			var getIdRet1Mock int64
			mockey.Mock((*git_server.CodebaseRepository).GetId, mockey.OptUnsafe).Return(getIdRet1Mock).Build()

			var ret5T1 int64
			mockey.Mock((*git_server.CodebaseChange).GetId, mockey.OptUnsafe).Return(ret5T1).Build()

			var getChangeByGitlabIdResponseMockPtrValue git_server.GetChangeByGitlabIdResponse
			getChangeByGitlabIdResponseMock := &getChangeByGitlabIdResponseMockPtrValue
			var getChangeByGitlabIdResponseMockChangePtrValue git_server.CodebaseChange
			getChangeByGitlabIdResponseMock.Change = &getChangeByGitlabIdResponseMockChangePtrValue
			var getChangeByGitlabIdResponseMockChangeSourcePtrValue git_server.ChangeSource
			getChangeByGitlabIdResponseMock.Change.Source = &getChangeByGitlabIdResponseMockChangeSourcePtrValue
			var getChangeByGitlabIdResponseMockChangeSourceRepoPtrValue git_server.CodebaseRepository
			getChangeByGitlabIdResponseMock.Change.Source.Repo = &getChangeByGitlabIdResponseMockChangeSourceRepoPtrValue
			var getChangeByGitlabIdRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.GitServerClient, "GetChangeByGitlabId"), mockey.OptUnsafe).Return(getChangeByGitlabIdResponseMock, getChangeByGitlabIdRet2Mock).Build()

			var getCodeChangeDetaileResponseMockPtrValue git_server.GetCodeChangeDetaileResponse
			getCodeChangeDetaileResponseMock := &getCodeChangeDetaileResponseMockPtrValue
			var getCodeChangeDetaileResponseMockDetailPtrValue git_server.CodeChangeDetail
			getCodeChangeDetaileResponseMock.Detail = &getCodeChangeDetaileResponseMockDetailPtrValue
			var getCodeChangeDetaileResponseMockDetailGitlabMrPtrValue git_server.CodeChangeGitlabMergeRequest
			getCodeChangeDetaileResponseMock.Detail.GitlabMr = &getCodeChangeDetaileResponseMockDetailGitlabMrPtrValue
			var getCodeChangeDetailRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.GitServerClient, "GetCodeChangeDetail"), mockey.OptUnsafe).Return(getCodeChangeDetaileResponseMock, getCodeChangeDetailRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var cid int64
			convey.So(func() { _ = GetContributionCodeChangeDependencyDetail(ctx, cid) }, convey.ShouldPanic)
		})
	})

}

func TestUpdateDevChangesAutoGen(t *testing.T) {
	// Verify the successful execution of the UpdateDevChanges function.
	t.Run("testUpdateDevChanges_Success", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			var username string
			var changes []*dev.UpdateDevChange

			// run target function and assert
			got1 := UpdateDevChanges(ctx, username, changes)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestUpdateContributionCodeChangeAutoGen(t *testing.T) {
	// Verify the panic situation when updating contribution code change.
	t.Run("testUpdateContributionCodeChange_Panic", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var stringRet1Mock string
			mockey.Mock(dev.ContributionCodeChangeStatus.String).Return(stringRet1Mock).Build()

			var clientMockPtrValue events.ProducerWrapper
			clientMock := &clientMockPtrValue
			mockey.MockValue(&producer.Client).To(clientMock)

			var stringifyRet1Mock string
			mockey.Mock(jsons.Stringify).Return(stringifyRet1Mock).Build()

			var codeChangeUpdateOptionsMockPtrValue git_server.CodeChangeUpdateOptions
			codeChangeUpdateOptionsMock := &codeChangeUpdateOptionsMockPtrValue
			mockey.Mock(adaptIdlContributionCodeChangeUpdateOptionsToGitServer).Return(codeChangeUpdateOptionsMock).Build()

			var getContributionCodeChangeByIDRet1Mock gresult.R[int]
			mockey.Mock((*data.Repo).GetContributionCodeChangeByID).Return(getContributionCodeChangeByIDRet1Mock).Build()

			var gitServerClientMockValueImpl gitserviceClientImplForTestAutoGen
			gitServerClientMockValue := &gitServerClientMockValueImpl
			gitServerClientMock := gitServerClientMockValue
			mockey.MockValue(&rpc.GitServerClient).To(gitServerClientMock)

			var getCodeChangeIDRet1Mock int64
			mockey.Mock((*dev.ContributionCodeChangeUpdateOptions).GetCodeChangeID, mockey.OptUnsafe).Return(getCodeChangeIDRet1Mock).Build()

			var updateContributionCodeChangeIncludeZeroRet1Mock error
			mockey.Mock((*data.Repo).UpdateContributionCodeChangeIncludeZero).Return(updateContributionCodeChangeIncludeZeroRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*events.ProducerWrapper).SendDevChangeEvent, mockey.OptUnsafe).Return(rMock).Build()

			var getTitleRet1Mock string
			mockey.Mock((*dev.ContributionCodeChangeUpdateOptions).GetTitle, mockey.OptUnsafe).Return(getTitleRet1Mock).Build()

			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var getBaseCommitRet1Mock string
			mockey.Mock((*dev.ContributionCodeChangeUpdateOptions).GetBaseCommit, mockey.OptUnsafe).Return(getBaseCommitRet1Mock).Build()

			var updateSelfRet1MockPtrValue biz_config.ChangeBizConfigHandler
			updateSelfRet1Mock := &updateSelfRet1MockPtrValue
			var updateSelfRet2Mock error
			mockey.Mock((*biz_config.ChangeBizConfigHandler).UpdateSelf, mockey.OptUnsafe).Return(updateSelfRet1Mock, updateSelfRet2Mock).Build()

			var formatTitleWithWipRet1Mock string
			mockey.Mock(functools.FormatTitleWithWip).Return(formatTitleWithWipRet1Mock).Build()

			var configStrRet1Mock string
			mockey.Mock((*biz_config.BizConfigHandler).ConfigStr, mockey.OptUnsafe).Return(configStrRet1Mock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var changeBizConfigHandlerMockPtrValue biz_config.ChangeBizConfigHandler
			changeBizConfigHandlerMock := &changeBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewChangeBizConfigHandler).Return(changeBizConfigHandlerMock).Build()

			var verifyIfCodeChangeNeedToUpdateRet1Mock bool
			mockey.Mock(utils.VerifyIfCodeChangeNeedToUpdate).Return(verifyIfCodeChangeNeedToUpdateRet1Mock).Build()

			var devBasicInfoMockPtrValue model.DevBasicInfo
			devBasicInfoMock := &devBasicInfoMockPtrValue
			var getDevBasicInfoByIDRet2Mock error
			mockey.Mock((*data.Repo).GetDevBasicInfoByID).Return(devBasicInfoMock, getDevBasicInfoByIDRet2Mock).Build()

			var getWipRet1Mock bool
			mockey.Mock((*dev.ContributionCodeChangeUpdateOptions).GetWip, mockey.OptUnsafe).Return(getWipRet1Mock).Build()

			var createDevTimelineEventRet1Mock error
			mockey.Mock(optimus.CreateDevTimelineEvent).Return(createDevTimelineEventRet1Mock).Build()

			mockey.Mock(utils2.SafeGo, mockey.OptUnsafe).Return().Build()

			var updateCodeChangeResponseMockPtrValue git_server.UpdateCodeChangeResponse
			updateCodeChangeResponseMock := &updateCodeChangeResponseMockPtrValue
			var updateCodeChangeRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.GitServerClient, "UpdateCodeChange"), mockey.OptUnsafe).Return(updateCodeChangeResponseMock, updateCodeChangeRet2Mock).Build()

			// prepare parameters
			var optionsPtrValue dev.ContributionCodeChangeUpdateOptions
			options := &optionsPtrValue
			var cid int64
			var username *string
			ctx := context.Background()
			convey.So(func() { _ = UpdateContributionCodeChange(ctx, options, cid, username) }, convey.ShouldPanic)
		})
	})

}

func TestGetContributionListItemByChangeAutoGen(t *testing.T) {
	// Verify the behavior of the function under default conditions.
	t.Run("testGetContributionListItemByChange_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getCodebaseRepoIDRet1Mock int64
			mockey.Mock((*git_server.CodeChangeGitlabMergeRequest).GetCodebaseRepoID, mockey.OptUnsafe).Return(getCodebaseRepoIDRet1Mock).Build()

			var getInsertionsRet1Mock int32
			mockey.Mock((*git_server.QueryMergeRequestDiffStatResponse).GetInsertions, mockey.OptUnsafe).Return(getInsertionsRet1Mock).Build()

			var changeBizConfigHandlerMockPtrValue biz_config.ChangeBizConfigHandler
			changeBizConfigHandlerMock := &changeBizConfigHandlerMockPtrValue
			mockey.Mock(biz_config.NewChangeBizConfigHandler).Return(changeBizConfigHandlerMock).Build()

			var contributionCodeChangeCheckStatusMock dev.ContributionCodeChangeCheckStatus
			mockey.Mock(adaptCheckItemStatusToContributionCheckStatus).Return(contributionCodeChangeCheckStatusMock).Build()

			var changeCheckHelperMockPtrValue change_check.ChangeCheckHelper
			changeCheckHelperMock := &changeCheckHelperMockPtrValue
			mockey.Mock(change_check.NewChangeCheckHelper, mockey.OptUnsafe).Return(changeCheckHelperMock).Build()

			var getCommentStatsRet1Mock gresult.R[int]
			mockey.Mock(gitsvr.GetCommentStats).Return(getCommentStatsRet1Mock).Build()

			var getChangeUrlByContributionIDRet1Mock string
			var getChangeUrlByContributionIDRet2Mock error
			mockey.Mock(utils.GetChangeUrlByContributionID).Return(getChangeUrlByContributionIDRet1Mock, getChangeUrlByContributionIDRet2Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var getReviewInfoRet1Mock gresult.R[int]
			mockey.Mock((*change_entity.CodeChange).GetReviewInfo).Return(getReviewInfoRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock((*ChangeDetail).GetMergeTask).Return(rMock).Build()

			var getCodebaseChangeIDRet1Mock int64
			mockey.Mock((*git_server.CodeChangeGitlabMergeRequest).GetCodebaseChangeID, mockey.OptUnsafe).Return(getCodebaseChangeIDRet1Mock).Build()

			var checkItemResultMockPtrValue change_check.CheckItemResult
			checkItemResultMock := &checkItemResultMockPtrValue
			mockey.Mock((*change_check.ConflictDetect).GetResult).Return(checkItemResultMock).Build()

			var conflictDetectMockPtrValue change_check.ConflictDetect
			conflictDetectMock := &conflictDetectMockPtrValue
			mockey.Mock(change_check.NewCheckItemConflictDetect, mockey.OptUnsafe).Return(conflictDetectMock).Build()

			var getConfigRet1Mock gresult.R[int]
			mockey.Mock((*biz_config.ChangeBizConfigHandler).GetConfig).Return(getConfigRet1Mock).Build()

			var getDeletionsRet1Mock int32
			mockey.Mock((*git_server.QueryMergeRequestDiffStatResponse).GetDeletions, mockey.OptUnsafe).Return(getDeletionsRet1Mock).Build()

			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			var getCiCheckResRet1Mock gresult.R[int]
			mockey.Mock((*change_check.ChangeCheckHelper).GetCiCheckRes).Return(getCiCheckResRet1Mock).Build()

			var contributionCodeChangeStatusMock dev.ContributionCodeChangeStatus
			var contributionCodeChangeStatusFromStringRet2Mock error
			mockey.Mock(dev.ContributionCodeChangeStatusFromString).Return(contributionCodeChangeStatusMock, contributionCodeChangeStatusFromStringRet2Mock).Build()

			var getContributionCodeChangeUrlRet1Mock string
			mockey.Mock(utils.GetContributionCodeChangeUrl).Return(getContributionCodeChangeUrlRet1Mock).Build()

			var gitServerClientMockValueImpl gitserviceClientImplForTestAutoGen
			gitServerClientMockValue := &gitServerClientMockValueImpl
			gitServerClientMock := gitServerClientMockValue
			mockey.MockValue(&rpc.GitServerClient).To(gitServerClientMock)

			var isNotOpenedRet1Mock bool
			mockey.Mock((*ChangeDetail).IsNotOpened, mockey.OptUnsafe).Return(isNotOpenedRet1Mock).Build()

			var queryMergeRequestDiffStatResponseMockPtrValue git_server.QueryMergeRequestDiffStatResponse
			queryMergeRequestDiffStatResponseMock := &queryMergeRequestDiffStatResponseMockPtrValue
			var queryMergeRequestDiffStatRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.GitServerClient, "QueryMergeRequestDiffStat"), mockey.OptUnsafe).Return(queryMergeRequestDiffStatResponseMock, queryMergeRequestDiffStatRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var changePtrValueCodeChange change_entity.CodeChange
			changePtrValue := ChangeDetail{CodeChange: &changePtrValueCodeChange}
			change := &changePtrValue
			var optionsPtrValue ContributionListItemAssembleOptions
			options := &optionsPtrValue
			convey.So(func() { _ = GetContributionListItemByChange(ctx, change, options) }, convey.ShouldPanic)
		})
	})

}

func TestGetContributionListItemsByChangesAutoGen(t *testing.T) {
	// Verify the function behavior when entities length is not zero.
	t.Run("testGetContributionListItemsByChanges_NotEmptyEntities", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var rMock gresult.R[int]
			mockey.Mock(GetContributionListItemByChange).Return(rMock).Build()

			// prepare parameters
			var optionsPtrValue ContributionListItemAssembleOptions
			options := &optionsPtrValue
			var keepOrder bool
			ctx := context.Background()
			var entitiesItem0PtrValueCodeChange change_entity.CodeChange
			entitiesItem0PtrValue := ChangeDetail{CodeChange: &entitiesItem0PtrValueCodeChange}
			entitiesItem0 := &entitiesItem0PtrValue
			entities := []*ChangeDetail{entitiesItem0,}
			convey.So(func() { _ = GetContributionListItemsByChanges(ctx, entities, options, keepOrder) }, convey.ShouldNotPanic)
		})
	})

	// Verify the function behavior when entities length is zero.
	t.Run("testGetContributionListItemsByChanges_EmptyEntities", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var waitRet1Mock error
			mockey.Mock((*errgroup.Group).Wait).Return(waitRet1Mock).Build()

			mockey.Mock((*errgroup.Group).Go).Return().Build()

			var rMock gresult.R[int]
			mockey.Mock(GetContributionListItemByChange).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var entities []*ChangeDetail
			var optionsPtrValue ContributionListItemAssembleOptions
			options := &optionsPtrValue
			var keepOrder bool
			convey.So(func() { _ = GetContributionListItemsByChanges(ctx, entities, options, keepOrder) }, convey.ShouldNotPanic)
		})
	})

}

func TestRetryContributionWorkflowAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var optimusDBMock data.DBHandler
			mockey.MockValue(&data.OptimusDB).To(optimusDBMock)

			var rMock gresult.R[int]
			mockey.Mock((*data.Repo).GetContributionCodeChangeByID).Return(rMock).Build()

			var repoMockPtrValue data.Repo
			repoMock := &repoMockPtrValue
			mockey.Mock((*data.Repo).UseCache, mockey.OptUnsafe).Return(repoMock).Build()

			var retrySmrEngineRet1Mock []byte
			var retrySmrEngineRet2Mock error
			mockey.Mock(engine.RetrySmrEngine).Return(retrySmrEngineRet1Mock, retrySmrEngineRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var contributionID int64
			convey.So(func() { _ = RetryContributionWorkflow(ctx, contributionID) }, convey.ShouldPanic)
		})
	})

}

func TestRetryContributionFailedTasksAutoGen(t *testing.T) {
	// code.byted.org/lang/gg/gresult.R:Get()_ret-2 == nil
	// int:IsNotOpened()_ret-1 != true
	// change.CheckIfLastCommitCheckedByPipelines(ctx, []string{dev.ContributionPipelineType_ci_check.String()}, false):Get()_ret-2 == nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var retrySmrEngineRet1Mock []byte
			var retrySmrEngineRet2Mock error
			mockey.Mock(engine.RetrySmrEngine).Return(retrySmrEngineRet1Mock, retrySmrEngineRet2Mock).Build()

			var retrySmrEngineTasksRet1Mock error
			mockey.Mock(engine.RetrySmrEngineTasks).Return(retrySmrEngineTasksRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(GetChangeDetail).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var cid int64
			var username string
			convey.So(func() { _ = RetryContributionFailedTasks(ctx, cid, username) }, convey.ShouldPanic)
		})
	})

}

