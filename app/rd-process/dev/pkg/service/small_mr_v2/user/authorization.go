package user

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"context"
)

func GetUserAuthorizationRoleForDevBasic(ctx context.Context, username string, devBasicID int64) gresult.R[consts.DevBasicUserAuthorizationRole] {
	devBasic, err := data.OptimusDB.Slave.UseCache().GetDevBasicInfoByID(ctx, devBasicID)
	if err != nil {
		return gresult.Err[consts.DevBasicUserAuthorizationRole](err)
	}
	if len(devBasic.GroupName) > 0 {
		isAdmin := meta.CheckIfUserIsAppAdmin(ctx, devBasic.GroupName, username)
		if isAdmin {
			return gresult.OK(consts.DevBasicUserAuthorizationRoleAdmin)
		}
	}
	if devBasic.Author == username {
		return gresult.OK(consts.DevBasicUserAuthorizationRoleAuthor)
	}
	collaborators, err := data.OptimusDB.Slave.UseCache().GetDevCollaboratorNames(ctx, devBasic.DevId)
	if err != nil {
		return gresult.Err[consts.DevBasicUserAuthorizationRole](err)
	}
	if gslice.Contains(collaborators, username) {
		return gresult.OK(consts.DevBasicUserAuthorizationRoleCollaborator)
	}
	return gresult.OK(consts.DevBasicUserAuthorizationRolePassenger)
}
