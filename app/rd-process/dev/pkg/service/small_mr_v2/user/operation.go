package user

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"context"
	"sort"
)

type UserOperation struct {
	ReviewOperation *dev.UserOperationCodeReivew
}

func GetUserOperation(ctx context.Context, username string, cid int64) gresult.R[*UserOperation] {
	answer := &UserOperation{}
	contribution, err := data.OptimusDB.Slave.UseCache().GetContributionCodeChangeByID(ctx, cid).Get()
	if err != nil {
		return gresult.Err[*UserOperation](err)
	}
	timelineResp, err := rpc.OptimusClient.GetDevTimelineEvents(ctx, &optimus.GetDevTimelineEventsQuery{
		DevID:         contribution.DevId,
		EventTypeList: []string{"review_comment"},
	})
	if err != nil {
		return gresult.Err[*UserOperation](err)
	}
	if len(timelineResp.List) == 0 {
		return gresult.OK(answer)
	}
	timelines := timelineResp.List
	sort.Slice(timelines, func(i, j int) bool {
		return timelines[i].ID > timelines[j].ID
	})
	// find last reviewed sha
	for _, timeline := range timelines {
		if timeline.Data.ReviewComment == nil {
			continue
		}
		if timeline.Operator == username && gslice.Contains([]string{"approve", "request_change", "always_approve"}, timeline.Data.ReviewComment.GetAction()) {
			answer.ReviewOperation = &dev.UserOperationCodeReivew{LastReviewedPatchsetNum: timeline.Data.ReviewComment.GetPatchsetNum()}
			break
		}
	}
	return gresult.OK(answer)
}
