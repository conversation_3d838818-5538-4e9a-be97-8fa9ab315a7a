package value_object

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
)

type DevSmr struct {
	DevBasicID          int64                      `json:"dev_basic_id"`
	DevID               int64                      `json:"dev_id"`
	Title               string                     `json:"title"`
	Author              string                     `json:"author"`
	State               string                     `json:"state"`
	Type                dev.DevSmrType             `json:"type"`
	CreateTime          int64                      `json:"create_time"`
	GroupName           string                     `json:"group_name"`
	TargetBranch        string                     `json:"target_branch"`
	RepoPath            string                     `json:"repo_path"`
	ProcessMode         dev.ProcessMode            `json:"process_mode"`
	Url                 string                     `json:"url"`
	SpaceID             int64                      `json:"space_id"`
	BizConfig           string                     `json:"biz_config"`
	GatekeeperStatus    dev.DevWorkflowStageStatus `json:"gatekeeper_status"`
	DevTaskMode         dev.DevTaskMode            `json:"dev_task_mode"`
	Description         string                     `json:"description"`
	MergeTaskInfo       *dev.MergeTaskInfo         `json:"merge_task_info"`
	PartialMergeEnabled bool                       `json:"partial_merge_enabled"`
	TrunkBranch         string                     `json:"trunk_branch"`
	DevTargetBranch     string                     `json:"dev_target_branch"`
	LarkGroupID         string                     `json:"lark_group_id"`
}

type ContributionStatusInfo struct {
	Conflicted         bool  `json:"conflicted"`
	NewCommitsInTarget int64 `json:"new_commits_in_target"`
}

type GitlabMrInfo struct {
	GitlabProjectID int64 `json:"gitlab_project_id"`
	GitlabIid       int64 `json:"gitlab_iid"`
}

type ContributionChangeStorageDetail struct {
	CodebaseRepoID   int64         `json:"codebase_repo_id"`
	CodebaseChangeID int64         `json:"codebase_change_id"`
	Url              string        `json:"url"`
	GitlabMrInfo     *GitlabMrInfo `json:"gitlab_mr_info"`
}

type ContributionChecksInfo struct {
	Checks []*ContributionCheckItem `json:"checks"`
}

type CheckStatusInfo struct {
	Status dev.CheckItemStatus `json:"status"`
	Msg    string              `json:"msg"`
}

type ContributionCheckItem struct {
	Name   string `json:"name"`
	Type   string `json:"type"`
	Status string `json:"status"`
}

type ChangeMergeTask struct {
	BizId           int64             `json:"biz_id"`
	BizType         dev.MergeTaskType `json:"biz_type"`
	ContributionIDs []int64           `json:"contribution_ids"`
	CreatedAt       int64             `json:"created_at"`
}

type ConflictInfo struct {
	HasConflicts  bool
	ConflictFiles []*git_server.ConflictFile
}
