package deps

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/gopkg/logs"
	"context"
)

func SyncDevTagToOptimusMr(ctx context.Context, DevID, optimusMrID int64) error {
	devTagsResp, err := rpc.OptimusClient.GetDevTags(ctx, &optimus.GetDevTagsQuery{DevID: DevID})
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return err
	}

	for _, tag := range devTagsResp.DevTags {
		_, err := rpc.OptimusClient.BindMrTag(ctx, &optimus.BindMrTagQuery{
			MrID:      optimusMrID,
			TagName:   tag.Name,
			DevID:     &DevID,
			GroupName: &tag.GroupProjectName,
			Reason:    tag.Reason,
		})
		if err != nil {
			logs.CtxError(ctx, err.Error())
		}
	}

	return nil
}
