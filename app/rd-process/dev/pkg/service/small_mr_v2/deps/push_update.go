package deps

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/devops/push"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/pushservice"
	"context"
)

func PushUpdateDevTaskWorkflow(ctx context.Context, devBasicID int64) error {
	data := &struct {
		DevBasicID int64
		Action     string
	}{
		DevBasicID: devBasicID,
		Action:     push.Action_Refresh.String(),
	}
	return pushservice.PushEvent(ctx, push.BizSmrDevTaskWorkflow, push.Action_Refresh, data, "dev", &push.FilterOption{
		PageFilter: &push.PageFilter{
			Page: push.PageSmrDevTask,
			Params: &push.PageParams{
				DevBasicID: &devBasicID,
			},
		},
	})
}
