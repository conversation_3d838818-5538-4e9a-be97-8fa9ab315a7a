package deps

import (
	optimusService "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimus"
	"code.byted.org/gopkg/logs/v2/log"
	"context"
)

func AddSetReadyForLandingTimeline(ctx context.Context, devID int64, operator string) error {
	timelineData := "Set ready for landing"
	if err := optimusService.CreateDevTimelineEvent(ctx, devID, "third_event", "primary", timelineData, operator); err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return err
	}
	return nil
}

func AddUnsetReadyForLandingTimeline(ctx context.Context, devID int64, operator string) error {
	timelineData := "Unset ready for landing"
	if err := optimusService.CreateDevTimelineEvent(ctx, devID, "third_event", "primary", timelineData, operator); err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return err
	}
	return nil
}
