package small_mr_v2

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_task_entity"
	"code.byted.org/lang/gg/gresult"
)

func TestStageCompletion_GetStatusAutoGen(t *testing.T) {
	// Verify the behavior when DevBasicID is zero.
	t.Run("testStageCompletion_GetStatus_DevBasicIDZero", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue StageCompletion
			receiver := &receiverPtrValue
			receiver.DevBasicID = int64(0)
			ctx := context.Background()
			convey.So(func() { _ = receiver.GetStatus(ctx) }, convey.ShouldNotPanic)
		})
	})

}

func TestStageGatekeeper_GetStatusAutoGen(t *testing.T) {
	// Verify the function behavior when DevBasicID is valid.
	t.Run("testStageGatekeeper_GetStatus_ValidID", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getChangesCountRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).GetChangesCount).Return(getChangesCountRet1Mock).Build()

			var devWorkflowStageStatusMock dev.DevWorkflowStageStatus
			mockey.Mock(adaptCheckItemStatusToWorkflowStageStatus).Return(devWorkflowStageStatusMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(GetGatekeeperCheckFinalStatus).Return(rMock).Build()

			// prepare parameters
			var receiverPtrValue StageGatekeeper
			receiver := &receiverPtrValue
			receiver.DevBasicID = int64(1)
			ctx := context.Background()
			convey.So(func() { _ = receiver.GetStatus(ctx) }, convey.ShouldNotPanic)
		})
	})

	// Verify the panic situation when DevDetail is invalid while DevBasicID is valid.
	t.Run("testStageGatekeeper_GetStatus_InvalidDetail", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var devWorkflowStageStatusMock dev.DevWorkflowStageStatus
			mockey.Mock(adaptCheckItemStatusToWorkflowStageStatus).Return(devWorkflowStageStatusMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(GetGatekeeperCheckFinalStatus).Return(rMock).Build()

			var getChangesCountRet1Mock gresult.R[int]
			mockey.Mock((*DevDetail).GetChangesCount).Return(getChangesCountRet1Mock).Build()

			// prepare parameters
			var receiverPtrValue StageGatekeeper
			receiver := &receiverPtrValue
			var receiverDevDetailPtrValueDevTask dev_task_entity.DevTask
			receiverDevDetailPtrValue := DevDetail{DevTask: &receiverDevDetailPtrValueDevTask}
			receiver.DevDetail = &receiverDevDetailPtrValue
			receiver.DevBasicID = int64(1)
			ctx := context.Background()
			convey.So(func() { _ = receiver.GetStatus(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestStageGatekeeper_SetUseCacheAutoGen(t *testing.T) {
	// Verify the functionality of setting the UseCache flag in StageGatekeeper.
	t.Run("testStageGatekeeper_SetUseCache", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue StageGatekeeper
			receiver := &receiverPtrValue
			var ifUse bool

			// run target function and assert
			got1 := receiver.SetUseCache(ifUse)
			convey.So((*got1).DevBasicID, convey.ShouldEqual, 0)
			convey.So((*got1).DevDetail == nil, convey.ShouldBeTrue)
			convey.So((*got1).UseCache, convey.ShouldEqual, false)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestGetWorkflowStageStatusAutoGen(t *testing.T) {
	// Verify the behavior of the function under default conditions.
	t.Run("testGetWorkflowStageStatus_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			ctx := context.Background()
			var stage WorkflowStage
			convey.So(func() { _ = GetWorkflowStageStatus(ctx, stage) }, convey.ShouldPanic)
		})
	})

}

