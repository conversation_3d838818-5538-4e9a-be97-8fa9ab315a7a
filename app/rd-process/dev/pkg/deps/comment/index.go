package comment

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"context"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
)

func CreateDevComment(ctx context.Context, comment *dev.DevComment) gresult.R[*dev.DevComment] {
	if comment.ReplyToID > 0 {
		commentReplied, _ := data.OptimusDB.Slave.GetDevCommentByID(ctx, comment.ReplyToID).Get()
		if commentReplied == nil || commentReplied.DevId != comment.DevID {
			log.V2.Error().With(ctx).Str("invalid reply comment params").KVs("reply comment id", comment.ReplyToID, "dev id", comment.DevID).Emit()
			return gresult.Err[*dev.DevComment](bits_err.COMMON.ErrInvalidInput)
		}
	}
	res, err := data.OptimusDB.Master.CreateDevComment(ctx, &model.DevComment{
		DevId:     comment.DevID,
		ReplyToId: comment.ReplyToID,
		Author:    comment.Author,
		Content:   comment.Content,
	}).Get()
	if err != nil {
		return gresult.Err[*dev.DevComment](err)
	}
	return gresult.OK(adaptDevCommentModelToIdl(res))
}

func DeleteDevComment(ctx context.Context, id int64) error {
	if err := data.OptimusDB.Master.DeleteCommentByID(ctx, id); err != nil {
		return err
	}
	return nil
}

func UpdateDevComment(ctx context.Context, id int64, options *dev.DevCommentUpdateOptions) error {
	if err := data.OptimusDB.Master.UpdateDevCommentByID(ctx, id, &model.DevComment{
		Content: gptr.Indirect(options.Content),
	}); err != nil {
		return err
	}
	return nil
}

func GetDevComment(ctx context.Context, id int64) gresult.R[*dev.DevComment] {
	res, err := data.OptimusDB.Slave.GetDevCommentByID(ctx, id).Get()
	if err != nil {
		return gresult.Err[*dev.DevComment](err)
	}
	return gresult.OK(adaptDevCommentModelToIdl(res))
}

func GetDevCommentsByDevID(ctx context.Context, devID int64) gresult.R[[]*dev.DevComment] {
	res, err := data.OptimusDB.Slave.GetCommentsByDevID(ctx, devID).Get()
	if err != nil {
		return gresult.Err[[]*dev.DevComment](err)
	}
	return gresult.OK(gslice.Map(res, adaptDevCommentModelToIdl))
}
