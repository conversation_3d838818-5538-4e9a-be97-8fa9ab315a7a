package env

import (
	"context"
	"fmt"
	"strconv"

	cd "code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/devops/continuous_deployment"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gresult"
)

type EnvSvc interface {
	ExistNewEnvData(ctx context.Context, devTaskID int64) bool
	GetDevTaskEnvInfo(ctx context.Context, devTaskID int64, operator string, option QueryOption) gresult.R[*GetDevTaskEnvInfoResp]
	GetDevTaskEnvInfoForDeployAtom(ctx context.Context, devTaskID int64, projectUniqueID, envName string, controlPlane dev.ControlPlane) gresult.R[*cd.GetEnvInfoForDeployAtomResp]
	CreateDevTaskEnvConfig(ctx context.Context, req CreateDevTaskEnvConfigReq) error
	UpdateLaneConfigs(ctx context.Context, devTaskID int64, laneConfigs []*dev.DevTaskEnvLaneConfig, operator string) error
	UpdateProjectConfigs(ctx context.Context, devTaskID int64, projectConfigs []*model.DevDeployConfig, operator string) error
	AddNewEnvProjectConfigs(ctx context.Context, devTaskID int64, newProjectConfigs []*model.DevDeployConfig) error
	DeleteEnvProjectConfigs(ctx context.Context, devTaskID int64, deleteProjects []*model.DevDeployConfig) error
	UpdateNodeAndProjectConfigs(ctx context.Context, username string, devBasicId, nodeId int64, envProjectData map[string]string) error
	UpdateGeckoProjectChannel(ctx context.Context, devTaskID int64, geckoProjects []*model.DevDeployConfig) error
	UpdateNodeConfig(ctx context.Context, username string, devBasicId int64, nodeConfigs []*cd.EnvNodeLaneConfig, envProjectData map[string]string) error
}

func NewSvc() EnvSvc { return EnvManager{} }

type EnvManager struct{}

func (EnvManager) UpdateGeckoProjectChannel(ctx context.Context, devTaskID int64, geckoProjects []*model.DevDeployConfig) error {
	req := &cd.UpdateGeckoProjectChannelReq{
		BizId:          devTaskID,
		BizScene:       cd.BizScene_BizSceneDevTask,
		ProjectConfigs: slicex.Map(geckoProjects, TransformDevDeployConfigToEnvProjectConfig),
	}
	_, err := rpc.EnvClient.UpdateGeckoProjectChannel(ctx, req)
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("add project to env config error (%v) (%v)", utils.ToJson(req), err)).Emit()
		return err
	}
	return nil
}

func (e EnvManager) AddNewEnvProjectConfigs(ctx context.Context, devTaskID int64, newProjectConfigs []*model.DevDeployConfig) error {
	req := &cd.AddNewProjectsReq{
		BizId:           devTaskID,
		BizScene:        cd.BizScene_BizSceneDevTask,
		AddProjectInfos: slicex.Map(newProjectConfigs, TransformDevDeployConfigToProjectBasicInfo),
	}
	_, err := rpc.EnvClient.AddNewProjectsToBiz(ctx, req)
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("add project to env config error (%v) (%v)", utils.ToJson(req), err)).Emit()
		return err
	}
	return nil
}

func (e EnvManager) DeleteEnvProjectConfigs(ctx context.Context, devTaskID int64, deleteProjects []*model.DevDeployConfig) error {
	req := &cd.DeleteProjectsReq{
		BizId:    devTaskID,
		BizScene: cd.BizScene_BizSceneDevTask,
		DeleteProjectInfos: slicex.Map(deleteProjects, func(from *model.DevDeployConfig) *cd.ProjectBasicInfo {
			projectType, _ := dev.ProjectTypeFromString(from.ProjectType)
			controlPlane, _ := dev.ControlPlaneFromString(from.ControlPlane)
			return &cd.ProjectBasicInfo{
				ProjectType:     projectType,
				ControlPlane:    controlPlane,
				ProjectUniqueId: from.ProjectUniqueId,
			}
		}),
	}
	_, err := rpc.EnvClient.DeleteProjectsFromBiz(ctx, req)
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("add project to env config error (%v) (%v)", utils.ToJson(req), err)).Emit()
		return err
	}
	return nil
}

func (e EnvManager) UpdateProjectConfigs(ctx context.Context, devTaskID int64, projectConfigs []*model.DevDeployConfig, operator string) error {
	req := &cd.UpdateProjectConfigsReq{
		BizId:          devTaskID,
		BizScene:       cd.BizScene_BizSceneDevTask,
		ProjectConfigs: slicex.Map(projectConfigs, TransformDevDeployConfigToEnvProjectConfig),
		Username:       operator,
	}
	_, err := rpc.EnvClient.UpdateProjectConfigs(ctx, req)
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("update biz env config error (%v) (%v)", utils.ToJson(req), err)).Emit()
		return err
	}
	return nil
}

func (e EnvManager) UpdateLaneConfigs(ctx context.Context, devTaskID int64, laneConfigs []*dev.DevTaskEnvLaneConfig, operator string) error {
	req := &cd.UpdateNodeConfigReq{
		BizId:       devTaskID,
		BizScene:    cd.BizScene_BizSceneDevTask,
		NodeConfigs: slicex.Map(laneConfigs, TransformDevTaskEnvLaneConfigToEnvNodeConfig),
		Username:    operator,
	}
	_, err := rpc.EnvClient.UpdateNodeConfig(ctx, req)
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("update biz env config error (%v) (%v)", utils.ToJson(req), err)).Emit()
		return err
	}
	return nil
}

func (e EnvManager) ExistNewEnvData(ctx context.Context, devTaskID int64) bool {
	resp, err := rpc.EnvClient.CheckBizRelationExist(ctx, &cd.CheckBizRelationExistReq{BizId: devTaskID, BizScene: cd.BizScene_BizSceneDevTask})
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("check biz env config error (%d) (%v)", devTaskID, err)).Emit()
		return false
	}
	if resp == nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("check biz env config error (%d) empty response", devTaskID)).Emit()
		return false
	}
	return resp.GetExisted()
}

type CreateDevTaskEnvConfigReq struct {
	DevTaskID                   int64
	SpaceId                     int64
	Operator                    string
	EnvSettings                 []*dev.DevTaskEnvSettingItem
	Projects                    []*model.DevDeployConfig
	IsGDP                       bool
	WorkflowID                  int64
	EnvProjectData              map[string]string
	GeckoCanDeployOnlineChannel bool
}

func (e EnvManager) CreateDevTaskEnvConfig(ctx context.Context, req CreateDevTaskEnvConfigReq) error {
	bizInfo := cd.BizInfo{
		BizID:                       req.DevTaskID,
		BizScene:                    cd.BizScene_BizSceneDevTask,
		WorkflowID:                  req.WorkflowID,
		ApplyClusterTemplate:        true,
		SpecialBizProcess:           choose.If(req.IsGDP, cd.SpecialBizProcess_SpecialBizProcessGDP, cd.SpecialBizProcess_SpecialBizProcessUnspecified),
		GeckoCanDeployOnlineChannel: req.GeckoCanDeployOnlineChannel,
	}
	_, err := rpc.EnvClient.CreateBizEnvConfig(ctx, &cd.CreateBizEnvConfigReq{
		BizInfo:          &bizInfo,
		NodeConfigs:      slicex.Map(req.EnvSettings, TransformDevTaskEnvSettingToEnvNodeConfig),
		ProjectConfigs:   slicex.Map(req.Projects, TransformDevDeployConfigToEnvProjectConfig),
		EnvVariableValue: nil,
		EnvProjectData:   req.EnvProjectData,
		Username:         req.Operator,
		SpaceId:          strconv.FormatInt(req.SpaceId, 10),
	})
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("create biz env config error (%v) (%v)", utils.ToJson(req), err)).Emit()
		return err
	}
	return nil
}

type QueryOption struct {
	NodeFixedName *string
	ControlPlane  *dev.ControlPlane
	IsWriteDb     *bool
}

type GetDevTaskEnvInfoResp struct {
	LaneConfigs    []*dev.DevTaskEnvLaneConfig
	ProjectConfigs []*dev.EnvProjectConfig
}

func (e EnvManager) GetDevTaskEnvInfo(ctx context.Context, devTaskID int64, operator string, option QueryOption) gresult.R[*GetDevTaskEnvInfoResp] {
	req := &cd.GetBizEnvConfigReq{
		BizID:         devTaskID,
		BizScene:      cd.BizScene_BizSceneDevTask,
		Operator:      operator,
		ControlPlane:  option.ControlPlane,
		NodeFixedName: option.NodeFixedName,
		IsWriteDb:     option.IsWriteDb,
	}
	resp, err := rpc.EnvClient.GetBizEnvConfig(ctx, req)
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("get biz env config error (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)).Emit()
		return gresult.Err[*GetDevTaskEnvInfoResp](bits_err.DEVTASK.ErrGetRecommendProjectEnvConfig.AddErrMsg(err.Error()))
	}
	if resp == nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("get biz env config error: config is nil (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)).Emit()
		return gresult.Err[*GetDevTaskEnvInfoResp](bits_err.DEVTASK.ErrGetRecommendProjectEnvConfig.AddErrMsg("GetBizEnvConfig return empty response"))
	}

	result := &GetDevTaskEnvInfoResp{
		LaneConfigs:    slicex.Map(resp.LaneConfigs, TransEnvLaneConfigToDev),
		ProjectConfigs: slicex.Map(resp.ProjectConfigs, ConvertBriefToFull),
	}
	return gresult.OK(result)
}

func (e EnvManager) GetDevTaskEnvInfoForDeployAtom(ctx context.Context, devTaskID int64, projectUniqueID, envName string, controlPlane dev.ControlPlane) gresult.R[*cd.GetEnvInfoForDeployAtomResp] {
	req := &cd.GetEnvInfoForDeployAtomReq{
		BizID:           devTaskID,
		BizScene:        cd.BizScene_BizSceneDevTask,
		ControlPlane:    controlPlane,
		EnvName:         envName,
		ProjectUniqueID: projectUniqueID,
	}
	resp, err := rpc.EnvClient.GetEnvInfoForDeployAtom(ctx, req)
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("get team flow config error (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)).Emit()
		return gresult.Err[*cd.GetEnvInfoForDeployAtomResp](bits_err.DEVTASK.ErrGetRecommendProjectEnvConfig.AddErrMsg(err.Error()))
	}
	if resp == nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("get team flow config error: config is nil (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)).Emit()
		return gresult.Err[*cd.GetEnvInfoForDeployAtomResp](bits_err.DEVTASK.ErrGetRecommendProjectEnvConfig.AddErrMsg("GetBizEnvConfig return empty response"))
	}
	return gresult.OK(resp)
}

func (e EnvManager) UpdateNodeAndProjectConfigs(ctx context.Context, username string, devBasicId, nodeId int64, envProjectData map[string]string) error {
	req := &cd.UpdateNodeAndProjectConfigsReq{
		BizID:          devBasicId,
		BizScene:       cd.BizScene_BizSceneDevTask,
		Username:       username,
		NodeID:         nodeId,
		EnvProjectData: envProjectData,
	}

	_, err := rpc.EnvClient.UpdateNodeAndProjectConfigs(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

func (e EnvManager) UpdateNodeConfig(ctx context.Context, username string, devBasicId int64, nodeConfigs []*cd.EnvNodeLaneConfig, envProjectData map[string]string) error {
	req := &cd.UpdateNodeConfigReq{BizId: devBasicId, BizScene: cd.BizScene_BizSceneDevTask, NodeConfigs: nodeConfigs, Username: username, EnvProjectData: envProjectData}
	if _, err := rpc.EnvClient.UpdateNodeConfig(ctx, req); err != nil {
		return err
	}
	return nil
}
