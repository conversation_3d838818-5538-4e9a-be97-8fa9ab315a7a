package devtask

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/dcache"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/third_part/unique"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/lang/gg/gresult"
	"context"
	"time"
)

func GetCreateDevTaskDraft(ctx context.Context, req *dev.GetCreateDevTaskDraftRequest) gresult.R[*dev.GetCreateDevTaskDraftResponse] {
	res := dev.NewGetCreateDevTaskDraftResponse()
	err := dcache.NewDefaultSvr().Get(ctx, req.GetDraftID(), res)
	if err != nil {
		return gresult.Err[*dev.GetCreateDevTaskDraftResponse](bits_err.DEVTASK.ErrGetCreateDevTaskDraft.AddError(err))
	}
	return gresult.OK(res)
}

func PostCreateDevTaskDraft(ctx context.Context, req *dev.PostCreateDevTaskDraftRequest) gresult.R[*dev.PostCreateDevTaskDraftResponse] {
	id, err := unique.IDString()
	if err != nil {
		return gresult.Err[*dev.PostCreateDevTaskDraftResponse](bits_err.DEVTASK.ErrPostCreateDevTaskDraft.AddError(err))
	}
	dcache.NewDefaultSvr().Set(id, req, time.Minute*30)
	return gresult.OK(&dev.PostCreateDevTaskDraftResponse{
		DraftID: id,
	})
}
