package devtask

import (
	"context"
	"encoding/json"
	"testing"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/toutiao/elastic/v7"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"golang.org/x/sync/errgroup"
)

func Test_getMostRecentUsedBranchNames(t *testing.T) {
	mockey.PatchConvey("稳定排序", t, func() {

		searchResult := &elastic.SearchResult{
			Hits: &elastic.SearchHits{
				Hits: []*elastic.SearchHit{
					{
						Source: json.RawMessage(`{"target_branch": ["a"]}`),
					},
					{
						Source: json.RawMessage(`{"target_branch": ["b"]}`),
					},
					{
						Source: json.RawMessage(`{"target_branch": ["b"]}`),
					},
					{
						Source: json.RawMessage(`{"target_branch": ["a"]}`),
					},
				},
			},
		}
		mockey.Mock((*elastic.SearchService).Do).Return(searchResult, nil).Build()
		names, err := getMostRecentUsedBranchNames(context.Background(), 1, 1).Get()
		convey.So(err, convey.ShouldBeNil)
		convey.So(names, convey.ShouldResemble, []string{"a", "b"})
	})
}

func Test_searchBranchesFromOptimusAndDevTask(t *testing.T) {
	mockey.PatchConvey("稳定排序", t, func() {
		mockey.Mock((*errgroup.Group).Go).To(func(f func() error) { _ = f() }).Build()
		mockey.Mock(getMostRecentUsedBranchNames).Return(gresult.OK([]string{"a", "b"})).Build()
		mockey.Mock(getBranchInfoByBranchNames).Return(gresult.OK([]*dev.BranchInfo{
			{
				Name: "a",
			},
			{
				Name: "b",
			},
		})).Build()
		mockey.Mock(getBranchesFromOptimus).Return(gresult.OK([]*dev.BranchInfo{
			{
				Name: "c",
			},
			{
				Name: "d",
			},
			{
				Name: "a",
			},
		})).Build()
		result, err := searchBranchesFromOptimusAndDevTask(context.Background(), &optimus.GetProjectBranchesQuery{Limit: 100}, 1).Get()
		convey.So(err, convey.ShouldBeNil)
		convey.So(result, convey.ShouldResemble, []*dev.BranchInfo{
			{
				Name: "a",
			},
			{
				Name: "b",
			},
			{
				Name: "c",
			},
			{
				Name: "d",
			},
		})
	})
}

func Test_getBranchInfoByBranchNames(t *testing.T) {
	mockey.PatchConvey("for coverage", t, func() {
		mockey.Mock((*errgroup.Group).Go).To(func(f func() error) { _ = f() }).Build()
		mockey.Mock(getBranchesFromOptimus).To(func(ctx context.Context, req *optimus.GetProjectBranchesQuery) gresult.R[[]*dev.BranchInfo] {
			return gresult.OK([]*dev.BranchInfo{{Name: req.GetText()}})
		}).Build()
		result, err := getBranchInfoByBranchNames(context.Background(), 1, []string{"a", "b"}).Get()
		convey.So(err, convey.ShouldBeNil)
		convey.So(result, convey.ShouldResemble, []*dev.BranchInfo{
			{
				Name: "a",
			},
			{
				Name: "b",
			},
		})
	})
}
