package small_mr_v2

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/feature_gate"
	"code.byted.org/lang/gg/gresult"
)

func TestPostDevFeatureGateAutoGen(t *testing.T) {
	// param2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var devFeatureGateActionMock dev.DevFeatureGateAction
			mockey.Mock((*dev.PostDevFeatureGateRequest).GetAction, mockey.OptUnsafe).Return(devFeatureGateActionMock).Build()

			var devFeatureGateExtraInfoMockPtrValue dev.DevFeatureGateExtraInfo
			devFeatureGateExtraInfoMock := &devFeatureGateExtraInfoMockPtrValue
			mockey.Mock((*dev.PostDevFeatureGateRequest).GetFgExtraInfo, mockey.OptUnsafe).Return(devFeatureGateExtraInfoMock).Build()

			var rMock gresult.R[int]
			mockey.Mock(feature_gate.PostDevFeatureGate).Return(rMock).Build()

			var getFgIDRet1Mock int64
			mockey.Mock((*dev.PostDevFeatureGateRequest).GetFgID, mockey.OptUnsafe).Return(getFgIDRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.PostDevFeatureGateRequest
			req := &reqPtrValue
			convey.So(func() { _ = PostDevFeatureGate(ctx, req) }, convey.ShouldPanic)
		})
	})

}

func TestGetDevFeatureGateAutoGen(t *testing.T) {
	// Verify the behavior when the GetDevFeatureGate request fails.
	t.Run("testGetDevFeatureGate_RequestFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.GetDevFeatureGateRequest).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			var rMock gresult.R[int]
			mockey.Mock(feature_gate.GetDevFeatureGate).Return(rMock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.GetDevFeatureGateRequest
			req := &reqPtrValue
			convey.So(func() { _ = GetDevFeatureGate(ctx, req) }, convey.ShouldPanic)
		})
	})

}

