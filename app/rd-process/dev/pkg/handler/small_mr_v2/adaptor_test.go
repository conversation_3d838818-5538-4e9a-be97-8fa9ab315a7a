package small_mr_v2

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/value_object"
)

func Test_adaptContributionCodeChangeStatusInfoToIdlAutoGen(t *testing.T) {
	// Verify the function adaptContributionCodeChangeStatusInfoToIdl with non-null parameter.
	t.Run("testAdaptContributionCodeChangeStatusInfoToIdl", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var infoPtrValue value_object.ContributionStatusInfo
			info := &infoPtrValue

			// run target function and assert
			got1 := adaptContributionCodeChangeStatusInfoToIdl(info)
			convey.So((*got1).NewCommitsInTarget_, convey.ShouldEqual, 0)
			convey.So((*got1).MergeStatusInfo == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Conflicted, convey.ShouldEqual, false)
		})
	})

}

func Test_adaptFeatureTrunkConflictToIdlAutoGen(t *testing.T) {
	// param1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var conflictReviewerStatusMock dev.ConflictReviewerStatus
			var conflictReviewerStatusFromStringRet2Mock error
			mockey.Mock(dev.ConflictReviewerStatusFromString).Return(conflictReviewerStatusMock, conflictReviewerStatusFromStringRet2Mock).Build()

			var featureTrunkConflictMergeMethodMock dev.FeatureTrunkConflictMergeMethod
			var featureTrunkConflictMergeMethodFromStringRet2Mock error
			mockey.Mock(dev.FeatureTrunkConflictMergeMethodFromString).Return(featureTrunkConflictMergeMethodMock, featureTrunkConflictMergeMethodFromStringRet2Mock).Build()

			// prepare parameters
			var conflictInfoPtrValue model.DevSmrConflict
			conflictInfo := &conflictInfoPtrValue
			var reviewers []*model.DevSmrConflictReviewer

			// run target function and assert
			got1 := adaptFeatureTrunkConflictToIdl(conflictInfo, reviewers)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Operator, convey.ShouldBeBlank)
			convey.So((*got1).LarkGroupId, convey.ShouldBeBlank)
			convey.So((*got1).VirtualCommitSha, convey.ShouldBeBlank)
			convey.So((*got1).ConflictId, convey.ShouldEqual, 0)
			convey.So((*got1).Status, convey.ShouldBeBlank)
			convey.So((*got1).RelatedContributionId, convey.ShouldEqual, 0)
			convey.So(len((*got1).Reviewers), convey.ShouldEqual, 0)
			convey.So((*got1).Author, convey.ShouldBeBlank)
			convey.So((*got1).RealCommitSha, convey.ShouldBeBlank)
		})
	})

}

func Test_adaptEntityDevSmrToIdlAutoGen(t *testing.T) {
	// param1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var entityPtrValue value_object.DevSmr
			entity := &entityPtrValue

			// run target function and assert
			got1 := adaptEntityDevSmrToIdl(entity)
			convey.So((*got1).TrunkBranch == nil, convey.ShouldBeFalse)
			convey.So((*got1).LarkGroupID == nil, convey.ShouldBeFalse)
			convey.So((*got1).Author, convey.ShouldBeBlank)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).Title, convey.ShouldBeBlank)
			convey.So((*got1).DevTaskMode == nil, convey.ShouldBeFalse)
			convey.So((*got1).MergeTaskInfo == nil, convey.ShouldBeTrue)
			convey.So((*got1).DevTargetBranch == nil, convey.ShouldBeFalse)
			convey.So((*got1).State, convey.ShouldBeBlank)
			convey.So((*got1).RepoPath == nil, convey.ShouldBeFalse)
		})
	})

}

func Test_adaptGetContributionCodeChangeListReqToSearchOptionsAutoGen(t *testing.T) {
	// param1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var contributionCodeChangeListOrderMock dev.ContributionCodeChangeListOrder
			mockey.Mock((*dev.GetContributionCodeChangeListRequest).GetOrder, mockey.OptUnsafe).Return(contributionCodeChangeListOrderMock).Build()

			var getLimitRet1Mock int64
			mockey.Mock((*dev.GetContributionCodeChangeListRequest).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			// prepare parameters
			var reqPtrValue dev.GetContributionCodeChangeListRequest
			req := &reqPtrValue

			// run target function and assert
			got1 := adaptGetContributionCodeChangeListReqToSearchOptions(req)
			convey.So((*got1).LastID == nil, convey.ShouldBeTrue)
			convey.So((*got1).Limit == nil, convey.ShouldBeFalse)
			convey.So((*got1).GroupName == nil, convey.ShouldBeTrue)
			convey.So((*got1).Username == nil, convey.ShouldBeTrue)
			convey.So((*got1).UserRole == nil, convey.ShouldBeTrue)
			convey.So((*got1).Status == nil, convey.ShouldBeTrue)
			convey.So((*got1).DevBasicID == nil, convey.ShouldBeTrue)
			convey.So((*got1).ReviewStatus == nil, convey.ShouldBeTrue)
			convey.So(int64((*got1).Order), convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// param1 != nil
	// param1.Order != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getLimitRet1Mock int64
			mockey.Mock((*dev.GetContributionCodeChangeListRequest).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			var contributionCodeChangeListOrderMock dev.ContributionCodeChangeListOrder
			mockey.Mock((*dev.GetContributionCodeChangeListRequest).GetOrder, mockey.OptUnsafe).Return(contributionCodeChangeListOrderMock).Build()

			// prepare parameters
			var reqPtrValue dev.GetContributionCodeChangeListRequest
			req := &reqPtrValue
			var reqOrderPtrValue dev.ContributionCodeChangeListOrder
			req.Order = &reqOrderPtrValue

			// run target function and assert
			got1 := adaptGetContributionCodeChangeListReqToSearchOptions(req)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).LastID == nil, convey.ShouldBeTrue)
			convey.So((*got1).Limit == nil, convey.ShouldBeFalse)
			convey.So((*got1).Status == nil, convey.ShouldBeTrue)
			convey.So((*got1).DevBasicID == nil, convey.ShouldBeTrue)
			convey.So((*got1).ReviewStatus == nil, convey.ShouldBeTrue)
			convey.So((*got1).GroupName == nil, convey.ShouldBeTrue)
			convey.So((*got1).Username == nil, convey.ShouldBeTrue)
			convey.So((*got1).UserRole == nil, convey.ShouldBeTrue)
			convey.So(int64((*got1).Order), convey.ShouldEqual, 0)
		})
	})

	// param1 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev.GetContributionCodeChangeListRequest:GetLimit()_ret-1 > 0
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev.GetContributionCodeChangeListRequest:GetLimit()_ret-1 <= 100
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			getLimitRet1Mock := int64(100)
			mockey.Mock((*dev.GetContributionCodeChangeListRequest).GetLimit, mockey.OptUnsafe).Return(getLimitRet1Mock).Build()

			var contributionCodeChangeListOrderMock dev.ContributionCodeChangeListOrder
			mockey.Mock((*dev.GetContributionCodeChangeListRequest).GetOrder, mockey.OptUnsafe).Return(contributionCodeChangeListOrderMock).Build()

			// prepare parameters
			var reqPtrValue dev.GetContributionCodeChangeListRequest
			req := &reqPtrValue

			// run target function and assert
			got1 := adaptGetContributionCodeChangeListReqToSearchOptions(req)
			convey.So((*got1).ReviewStatus == nil, convey.ShouldBeTrue)
			convey.So((*got1).UserRole == nil, convey.ShouldBeTrue)
			convey.So((*got1).Status == nil, convey.ShouldBeTrue)
			convey.So(int64((*got1).Order), convey.ShouldEqual, 0)
			convey.So((*got1).Username == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).DevBasicID == nil, convey.ShouldBeTrue)
			convey.So((*got1).LastID == nil, convey.ShouldBeTrue)
			convey.So((*got1).Limit == nil, convey.ShouldBeTrue)
			convey.So((*got1).GroupName == nil, convey.ShouldBeTrue)
		})
	})

}

func Test_adaptContributionCodeChangeDependencyDetailToIdlAutoGen(t *testing.T) {
	// param1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var detailPtrValue value_object.ContributionChangeStorageDetail
			detail := &detailPtrValue

			// run target function and assert
			got1 := adaptContributionCodeChangeDependencyDetailToIdl(detail)
			convey.So((*got1).PlatformUrl == nil, convey.ShouldBeFalse)
			convey.So((*got1).GitlabMrIid == nil, convey.ShouldBeTrue)
			convey.So((*got1).GitlabProjectID == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).CodebaseRepoID == nil, convey.ShouldBeFalse)
			convey.So((*got1).CodebaseChangeID == nil, convey.ShouldBeFalse)
		})
	})

	// param1 != nil
	// param1.GitlabMrInfo != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			var detailPtrValue value_object.ContributionChangeStorageDetail
			detail := &detailPtrValue
			var detailGitlabMrInfoPtrValue value_object.GitlabMrInfo
			detail.GitlabMrInfo = &detailGitlabMrInfoPtrValue

			// run target function and assert
			got1 := adaptContributionCodeChangeDependencyDetailToIdl(detail)
			convey.So((*got1).GitlabProjectID == nil, convey.ShouldBeFalse)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So((*got1).CodebaseRepoID == nil, convey.ShouldBeFalse)
			convey.So((*got1).CodebaseChangeID == nil, convey.ShouldBeFalse)
			convey.So((*got1).PlatformUrl == nil, convey.ShouldBeFalse)
			convey.So((*got1).GitlabMrIid == nil, convey.ShouldBeFalse)
		})
	})

}

func Test_adaptChangesToDevChangesAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var formContributionUrlRet1Mock string
			mockey.Mock(functools.FormContributionUrl, mockey.OptUnsafe).Return(formContributionUrlRet1Mock).Build()

			// prepare parameters
			var changes []*small_mr_v2.ChangeDetail
			var spaceID int64

			// run target function and assert
			got1 := adaptChangesToDevChanges(changes, spaceID)
			convey.So(len(got1), convey.ShouldEqual, 0)
		})
	})

}

