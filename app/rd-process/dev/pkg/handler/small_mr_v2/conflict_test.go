package small_mr_v2

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/conflict"
)

func TestPostFeatureTrunkConflictAutoGen(t *testing.T) {
	// param2 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/conflict:RegisterConflict()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getTargetBranchHeadShaRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetTargetBranchHeadSha, mockey.OptUnsafe).Return(getTargetBranchHeadShaRet1Mock).Build()

			registerConflictRet1Mock := fmt.Errorf("error")
			mockey.Mock(conflict.RegisterConflict).Return(registerConflictRet1Mock).Build()

			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			var getMergedBranchHeadShaRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetMergedBranchHeadSha, mockey.OptUnsafe).Return(getMergedBranchHeadShaRet1Mock).Build()

			var featureTrunkConflictMergeMethodMock dev.FeatureTrunkConflictMergeMethod
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetMergeMethod, mockey.OptUnsafe).Return(featureTrunkConflictMergeMethodMock).Build()

			var getSourceBranchHeadShaRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetSourceBranchHeadSha, mockey.OptUnsafe).Return(getSourceBranchHeadShaRet1Mock).Build()

			var getRepoPathRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetRepoPath, mockey.OptUnsafe).Return(getRepoPathRet1Mock).Build()

			var postFeatureTrunkConflictResponseMockPtrValue dev.PostFeatureTrunkConflictResponse
			postFeatureTrunkConflictResponseMock := &postFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewPostFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(postFeatureTrunkConflictResponseMock).Build()

			var getAuthorRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetAuthor, mockey.OptUnsafe).Return(getAuthorRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.PostFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = PostFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

	// default condition
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getMergedBranchHeadShaRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetMergedBranchHeadSha, mockey.OptUnsafe).Return(getMergedBranchHeadShaRet1Mock).Build()

			var getTargetBranchHeadShaRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetTargetBranchHeadSha, mockey.OptUnsafe).Return(getTargetBranchHeadShaRet1Mock).Build()

			var getSourceBranchHeadShaRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetSourceBranchHeadSha, mockey.OptUnsafe).Return(getSourceBranchHeadShaRet1Mock).Build()

			var featureTrunkConflictMergeMethodMock dev.FeatureTrunkConflictMergeMethod
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetMergeMethod, mockey.OptUnsafe).Return(featureTrunkConflictMergeMethodMock).Build()

			var postFeatureTrunkConflictResponseMockPtrValue dev.PostFeatureTrunkConflictResponse
			postFeatureTrunkConflictResponseMock := &postFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewPostFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(postFeatureTrunkConflictResponseMock).Build()

			var getRepoPathRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetRepoPath, mockey.OptUnsafe).Return(getRepoPathRet1Mock).Build()

			var getDevBasicIDRet1Mock int64
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetDevBasicID, mockey.OptUnsafe).Return(getDevBasicIDRet1Mock).Build()

			var registerConflictRet1Mock error
			mockey.Mock(conflict.RegisterConflict).Return(registerConflictRet1Mock).Build()

			var getAuthorRet1Mock string
			mockey.Mock((*dev.PostFeatureTrunkConflictRequest).GetAuthor, mockey.OptUnsafe).Return(getAuthorRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.PostFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = PostFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

}

func TestUpdateFeatureTrunkConflictAutoGen(t *testing.T) {
	// Verify the successful execution of UpdateFeatureTrunkConflict function.
	t.Run("testUpdateFeatureTrunkConflict_Success", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var updateFeatureTrunkConflictRet1Mock error
			mockey.Mock(conflict.UpdateFeatureTrunkConflict).Return(updateFeatureTrunkConflictRet1Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.UpdateFeatureTrunkConflictRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getVirtualCommitShaRet1Mock string
			mockey.Mock((*dev.UpdateFeatureTrunkConflictRequest).GetVirtualCommitSha, mockey.OptUnsafe).Return(getVirtualCommitShaRet1Mock).Build()

			var updateFeatureTrunkConflictResponseMockPtrValue dev.UpdateFeatureTrunkConflictResponse
			updateFeatureTrunkConflictResponseMock := &updateFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewUpdateFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(updateFeatureTrunkConflictResponseMock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.UpdateFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = UpdateFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

	// Verify the successful execution of UpdateFeatureTrunkConflict function.
	t.Run("testUpdateFeatureTrunkConflict_Success", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			updateFeatureTrunkConflictRet1Mock := fmt.Errorf("error")
			mockey.Mock(conflict.UpdateFeatureTrunkConflict).Return(updateFeatureTrunkConflictRet1Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.UpdateFeatureTrunkConflictRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getVirtualCommitShaRet1Mock string
			mockey.Mock((*dev.UpdateFeatureTrunkConflictRequest).GetVirtualCommitSha, mockey.OptUnsafe).Return(getVirtualCommitShaRet1Mock).Build()

			var updateFeatureTrunkConflictResponseMockPtrValue dev.UpdateFeatureTrunkConflictResponse
			updateFeatureTrunkConflictResponseMock := &updateFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewUpdateFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(updateFeatureTrunkConflictResponseMock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.UpdateFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = UpdateFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

}

func TestApproveFeatureTrunkConflictAutoGen(t *testing.T) {
	// param2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var approveFeatureTrunkConflictResponseMockPtrValue dev.ApproveFeatureTrunkConflictResponse
			approveFeatureTrunkConflictResponseMock := &approveFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewApproveFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(approveFeatureTrunkConflictResponseMock).Build()

			var approveConflictRet1Mock error
			mockey.Mock(conflict.ApproveConflict).Return(approveConflictRet1Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.ApproveFeatureTrunkConflictRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*dev.ApproveFeatureTrunkConflictRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.ApproveFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = ApproveFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

	// param2 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/conflict:ApproveConflict()_ret-1 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var approveFeatureTrunkConflictResponseMockPtrValue dev.ApproveFeatureTrunkConflictResponse
			approveFeatureTrunkConflictResponseMock := &approveFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewApproveFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(approveFeatureTrunkConflictResponseMock).Build()

			approveConflictRet1Mock := fmt.Errorf("error")
			mockey.Mock(conflict.ApproveConflict).Return(approveConflictRet1Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.ApproveFeatureTrunkConflictRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*dev.ApproveFeatureTrunkConflictRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.ApproveFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = ApproveFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

}

func TestAddFeatureTrunkConflictReviewerAutoGen(t *testing.T) {
	// param2 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getUsernameRet1Mock string
			mockey.Mock((*dev.AddFeatureTrunkConflictReviewerRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var addFeatureTrunkConflictReviewerResponseMockPtrValue dev.AddFeatureTrunkConflictReviewerResponse
			addFeatureTrunkConflictReviewerResponseMock := &addFeatureTrunkConflictReviewerResponseMockPtrValue
			mockey.Mock(dev.NewAddFeatureTrunkConflictReviewerResponse, mockey.OptUnsafe).Return(addFeatureTrunkConflictReviewerResponseMock).Build()

			var addConflictReviewerRet1Mock error
			mockey.Mock(conflict.AddConflictReviewer).Return(addConflictReviewerRet1Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.AddFeatureTrunkConflictReviewerRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			// prepare parameters
			var reqPtrValue dev.AddFeatureTrunkConflictReviewerRequest
			req := &reqPtrValue
			ctx := context.Background()
			convey.So(func() { _ = AddFeatureTrunkConflictReviewer(ctx, req) }, convey.ShouldNotPanic)
		})
	})

	// param2 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/conflict:AddConflictReviewer()_ret-1 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			addConflictReviewerRet1Mock := fmt.Errorf("error")
			mockey.Mock(conflict.AddConflictReviewer).Return(addConflictReviewerRet1Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.AddFeatureTrunkConflictReviewerRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*dev.AddFeatureTrunkConflictReviewerRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var addFeatureTrunkConflictReviewerResponseMockPtrValue dev.AddFeatureTrunkConflictReviewerResponse
			addFeatureTrunkConflictReviewerResponseMock := &addFeatureTrunkConflictReviewerResponseMockPtrValue
			mockey.Mock(dev.NewAddFeatureTrunkConflictReviewerResponse, mockey.OptUnsafe).Return(addFeatureTrunkConflictReviewerResponseMock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.AddFeatureTrunkConflictReviewerRequest
			req := &reqPtrValue
			convey.So(func() { _ = AddFeatureTrunkConflictReviewer(ctx, req) }, convey.ShouldNotPanic)
		})
	})

}

func TestRemoveFeatureTrunkConflictReviewerAutoGen(t *testing.T) {
	// param2 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/conflict:RemoveConflictReviewer()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			removeConflictReviewerRet1Mock := fmt.Errorf("error")
			mockey.Mock(conflict.RemoveConflictReviewer).Return(removeConflictReviewerRet1Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.RemoveFeatureTrunkConflictReviewerRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*dev.RemoveFeatureTrunkConflictReviewerRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var removeFeatureTrunkConflictReviewerResponseMockPtrValue dev.RemoveFeatureTrunkConflictReviewerResponse
			removeFeatureTrunkConflictReviewerResponseMock := &removeFeatureTrunkConflictReviewerResponseMockPtrValue
			mockey.Mock(dev.NewRemoveFeatureTrunkConflictReviewerResponse, mockey.OptUnsafe).Return(removeFeatureTrunkConflictReviewerResponseMock).Build()

			// prepare parameters
			var reqPtrValue dev.RemoveFeatureTrunkConflictReviewerRequest
			req := &reqPtrValue
			ctx := context.Background()
			convey.So(func() { _ = RemoveFeatureTrunkConflictReviewer(ctx, req) }, convey.ShouldNotPanic)
		})
	})

	// param2 != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/conflict:RemoveConflictReviewer()_ret-1 == nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var removeFeatureTrunkConflictReviewerResponseMockPtrValue dev.RemoveFeatureTrunkConflictReviewerResponse
			removeFeatureTrunkConflictReviewerResponseMock := &removeFeatureTrunkConflictReviewerResponseMockPtrValue
			mockey.Mock(dev.NewRemoveFeatureTrunkConflictReviewerResponse, mockey.OptUnsafe).Return(removeFeatureTrunkConflictReviewerResponseMock).Build()

			var removeConflictReviewerRet1Mock error
			mockey.Mock(conflict.RemoveConflictReviewer).Return(removeConflictReviewerRet1Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.RemoveFeatureTrunkConflictReviewerRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*dev.RemoveFeatureTrunkConflictReviewerRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.RemoveFeatureTrunkConflictReviewerRequest
			req := &reqPtrValue
			convey.So(func() { _ = RemoveFeatureTrunkConflictReviewer(ctx, req) }, convey.ShouldNotPanic)
		})
	})

}

func TestGetFeatureTrunkConflictDetailAutoGen(t *testing.T) {
	// Verify the successful retrieval of feature trunk conflict detail.
	t.Run("testGetFeatureTrunkConflictDetail_Success", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			mockey.Mock((*dev.GetFeatureTrunkConflictDetailResponse).SetConflict, mockey.OptUnsafe).Return().Build()

			var featureTrunkConflictInfoMockPtrValue dev.FeatureTrunkConflictInfo
			featureTrunkConflictInfoMock := &featureTrunkConflictInfoMockPtrValue
			mockey.Mock(adaptFeatureTrunkConflictToIdl).Return(featureTrunkConflictInfoMock).Build()

			var getFeatureTrunkConflictDetailResponseMockPtrValue dev.GetFeatureTrunkConflictDetailResponse
			getFeatureTrunkConflictDetailResponseMock := &getFeatureTrunkConflictDetailResponseMockPtrValue
			mockey.Mock(dev.NewGetFeatureTrunkConflictDetailResponse, mockey.OptUnsafe).Return(getFeatureTrunkConflictDetailResponseMock).Build()

			var devSmrConflictMockPtrValue model.DevSmrConflict
			devSmrConflictMock := &devSmrConflictMockPtrValue
			var getConflictDetailRet2Mock []*model.DevSmrConflictReviewer
			var getConflictDetailRet3Mock error
			mockey.Mock(conflict.GetConflictDetail).Return(devSmrConflictMock, getConflictDetailRet2Mock, getConflictDetailRet3Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.GetFeatureTrunkConflictDetailRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.GetFeatureTrunkConflictDetailRequest
			req := &reqPtrValue
			convey.So(func() { _ = GetFeatureTrunkConflictDetail(ctx, req) }, convey.ShouldNotPanic)
		})
	})

	// Verify the successful retrieval of feature trunk conflict detail with a mocked error in GetConflictDetail.
	t.Run("testGetFeatureTrunkConflictDetail_Success_WithMockError", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var devSmrConflictMockPtrValue model.DevSmrConflict
			devSmrConflictMock := &devSmrConflictMockPtrValue
			var getConflictDetailRet2Mock []*model.DevSmrConflictReviewer
			getConflictDetailRet3Mock := fmt.Errorf("error")
			mockey.Mock(conflict.GetConflictDetail).Return(devSmrConflictMock, getConflictDetailRet2Mock, getConflictDetailRet3Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.GetFeatureTrunkConflictDetailRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			mockey.Mock((*dev.GetFeatureTrunkConflictDetailResponse).SetConflict, mockey.OptUnsafe).Return().Build()

			var featureTrunkConflictInfoMockPtrValue dev.FeatureTrunkConflictInfo
			featureTrunkConflictInfoMock := &featureTrunkConflictInfoMockPtrValue
			mockey.Mock(adaptFeatureTrunkConflictToIdl).Return(featureTrunkConflictInfoMock).Build()

			var getFeatureTrunkConflictDetailResponseMockPtrValue dev.GetFeatureTrunkConflictDetailResponse
			getFeatureTrunkConflictDetailResponseMock := &getFeatureTrunkConflictDetailResponseMockPtrValue
			mockey.Mock(dev.NewGetFeatureTrunkConflictDetailResponse, mockey.OptUnsafe).Return(getFeatureTrunkConflictDetailResponseMock).Build()

			// prepare parameters
			var reqPtrValue dev.GetFeatureTrunkConflictDetailRequest
			req := &reqPtrValue
			ctx := context.Background()
			convey.So(func() { _ = GetFeatureTrunkConflictDetail(ctx, req) }, convey.ShouldNotPanic)
		})
	})

}

func TestCancelApproveFeatureTrunkConflictAutoGen(t *testing.T) {
	// Verify the behavior when DisapproveConflict fails.
	t.Run("testCancelApproveFeatureTrunkConflict_DisapproveConflictFailure", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.CancelApproveFeatureTrunkConflictRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*dev.CancelApproveFeatureTrunkConflictRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var cancelApproveFeatureTrunkConflictResponseMockPtrValue dev.CancelApproveFeatureTrunkConflictResponse
			cancelApproveFeatureTrunkConflictResponseMock := &cancelApproveFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewCancelApproveFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(cancelApproveFeatureTrunkConflictResponseMock).Build()

			disapproveConflictRet1Mock := fmt.Errorf("error")
			mockey.Mock(conflict.DisapproveConflict).Return(disapproveConflictRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.CancelApproveFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = CancelApproveFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

	// Verify the behavior when DisapproveConflict succeeds.
	t.Run("testCancelApproveFeatureTrunkConflict_DisapproveConflictSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getUsernameRet1Mock string
			mockey.Mock((*dev.CancelApproveFeatureTrunkConflictRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var cancelApproveFeatureTrunkConflictResponseMockPtrValue dev.CancelApproveFeatureTrunkConflictResponse
			cancelApproveFeatureTrunkConflictResponseMock := &cancelApproveFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewCancelApproveFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(cancelApproveFeatureTrunkConflictResponseMock).Build()

			var disapproveConflictRet1Mock error
			mockey.Mock(conflict.DisapproveConflict).Return(disapproveConflictRet1Mock).Build()

			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.CancelApproveFeatureTrunkConflictRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.CancelApproveFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = CancelApproveFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

}

func TestRejectFeatureTrunkConflictAutoGen(t *testing.T) {
	// Verify the successful execution of RejectFeatureTrunkConflict function.
	t.Run("testRejectFeatureTrunkConflict_Success", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.RejectFeatureTrunkConflictRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*dev.RejectFeatureTrunkConflictRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var rejectFeatureTrunkConflictResponseMockPtrValue dev.RejectFeatureTrunkConflictResponse
			rejectFeatureTrunkConflictResponseMock := &rejectFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewRejectFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(rejectFeatureTrunkConflictResponseMock).Build()

			var rejectConflictRet1Mock error
			mockey.Mock(conflict.RejectConflict).Return(rejectConflictRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.RejectFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = RejectFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

	// Verify the successful execution of RejectFeatureTrunkConflict function with mocked error return.
	t.Run("testRejectFeatureTrunkConflict_Success", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var getConflictIDRet1Mock int64
			mockey.Mock((*dev.RejectFeatureTrunkConflictRequest).GetConflictID, mockey.OptUnsafe).Return(getConflictIDRet1Mock).Build()

			var getUsernameRet1Mock string
			mockey.Mock((*dev.RejectFeatureTrunkConflictRequest).GetUsername, mockey.OptUnsafe).Return(getUsernameRet1Mock).Build()

			var rejectFeatureTrunkConflictResponseMockPtrValue dev.RejectFeatureTrunkConflictResponse
			rejectFeatureTrunkConflictResponseMock := &rejectFeatureTrunkConflictResponseMockPtrValue
			mockey.Mock(dev.NewRejectFeatureTrunkConflictResponse, mockey.OptUnsafe).Return(rejectFeatureTrunkConflictResponseMock).Build()

			rejectConflictRet1Mock := fmt.Errorf("error")
			mockey.Mock(conflict.RejectConflict).Return(rejectConflictRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue dev.RejectFeatureTrunkConflictRequest
			req := &reqPtrValue
			convey.So(func() { _ = RejectFeatureTrunkConflict(ctx, req) }, convey.ShouldNotPanic)
		})
	})

}

