package service

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/mq/consumer"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/mq/producer"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/third_part"
)

func Init() {
	rpc.Init()
	consumer.Init()
	producer.Init()
	third_part.Init()
}

func Stop() {
	consumer.Stop()
}
