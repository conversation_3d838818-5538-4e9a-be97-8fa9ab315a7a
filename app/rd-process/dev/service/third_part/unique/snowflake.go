package unique

import (
	"strconv"
	"time"

	"github.com/howeyc/crc16"

	"code.byted.org/gopkg/logs"

	"github.com/google/uuid"

	"github.com/sony/sonyflake"
)

var flake *sonyflake.Sonyflake

func InitSnowFlake() {
	settings := sonyflake.Settings{
		StartTime: time.Now(),
		MachineID: func() (uint16, error) {
			var data []byte
			var err error
			if data, err = uuid.New().MarshalBinary(); err != nil {
				return 0, err
			}
			machineID := crc16.ChecksumCCITT(data) // CRC16 哈希校验
			logs.Info("machine id was created:%d", machineID)
			return machineID, nil
		},
		CheckMachineID: func(u uint16) bool {
			return u != 0
		},
	}
	flake = sonyflake.NewSonyflake(settings)
	if flake == nil {
		logs.Fatalf("[uniqueID] unique_id generator not created")
	}
}

func ID() (ID uint64, err error) {
	if ID, err = flake.NextID(); err != nil {
		logs.Errorf("[uniqueID] create unique id err:%v", err)
		return 0, err
	}
	return ID, err
}

func IDString() (string, error) {
	ID, err := ID()
	if err != nil {
		return "", err
	}
	return strconv.FormatUint(ID, 10), err
}
