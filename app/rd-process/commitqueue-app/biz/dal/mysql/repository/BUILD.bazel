load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "repository",
    srcs = ["commit_queue_app_merge_request.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/commitqueue-app/biz/dal/mysql/repository",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/commitqueue-app/biz/dal/mysql",
        "//app/rd-process/commitqueue-app/biz/dal/mysql/entity",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//schema",
        "@org_byted_code_lang_gg//gresult",
    ],
)
