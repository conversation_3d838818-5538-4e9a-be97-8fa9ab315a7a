package main

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/codebase/rpc/codebase"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/codebase/rpc/reviewapp"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/dal/cache"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/dal/tcc"
	"code.byted.org/gopkg/logs/v2/log"
)

func initDeps() {
	config := mustInitializeConfig()
	mysql.MustInitialize(config.MySQL)
	cache.MustInitialize(config.Cache)
	tcc.MustInitialize()

	if config.Codebase == nil {
		log.V1.Fatal("codebase config is nil")
	}
	codebase.GlobalCodebase = codebase.MustNewCodebaseImpl(codebase.NewCodebaseImplOpt{
		BaseURL:   config.Codebase.BaseURL,
		AppID:     config.Codebase.AppID,
		AppSecret: config.Codebase.AppSecret,
	})

	if config.ReviewApp == nil {
		log.V1.Fatal("review app config is nil")
	}
	reviewapp.GlobalReviewApp = reviewapp.MustNewReviewAppImpl(reviewapp.NewReviewAppImplOpt{
		BaseURL: config.ReviewApp.BaseURL,
	})
}
