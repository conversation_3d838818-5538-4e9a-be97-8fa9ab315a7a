package codebase

import (
	"context"
	"time"

	"github.com/samber/lo"

	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/kitex_gen/bytedance/bits/yacr"
)

var GlobalCodebase Codebase

type UserApproveSetting struct {
	ApproveLaterChanges bool `json:"approve_later_changes"`
}

type User struct {
	ID       int64  `json:"id,omitempty"`
	Name     string `json:"name,omitempty"`
	Email    string `json:"email,omitempty"`
	Username string `json:"username,omitempty"`
}

type ReviewRuleReviewer struct {
	UserID   int64   `json:"user_id"`
	Reason   *string `json:"reason"`
	Operator *int64  `json:"operator"`
}

type ReviewRuleInput struct {
	Name              string                `json:"name"`
	Reviewers         []*ReviewRuleReviewer `json:"reviewers"`
	ApprovalsRequired uint8                 `json:"approvals_required"`
}

type UpdateChangeReviewRulesForAppRequest struct {
	ReviewRules []*ReviewRuleInput `json:"review_rules"`
}

type ReviewSummaryStatus string

const (
	ReviewSummaryStatusPassed  ReviewSummaryStatus = "passed"
	ReviewSummaryStatusPending ReviewSummaryStatus = "pending"
)

func (s ReviewSummaryStatus) ToYACRReviewStatus() yacr.ReviewStatus {
	switch s {
	case ReviewSummaryStatusPassed:
		return yacr.ReviewStatus_APPROVED
	case ReviewSummaryStatusPending:
		return yacr.ReviewStatus_RUNNING
	default:
		return yacr.ReviewStatus_RUNNING
	}
}

type ReviewRuleType string

const (
	ReviewRuleTypeRegular   ReviewRuleType = "regular"
	ReviewRuleTypeFallBack  ReviewRuleType = "fallback"
	ReviewRuleTypeCodeOwner ReviewRuleType = "code_owner"
)

type ReviewRule struct {
	ID                int64            `json:"id,omitempty"`
	ParentID          *int64           `json:"parent_id,omitempty"`
	Default           bool             `json:"default,omitempty"`
	Type              ReviewRuleType   `json:"type,omitempty"`
	AppID             *int64           `json:"app_id,omitempty"` // bits app id
	Name              string           `json:"name,omitempty"`
	ApprovalsRequired uint8            `json:"approvals_required,omitempty"`
	Users             []*User          `json:"users"`
	UserReasonsByID   map[int64]string `json:"user_reasons"`
	UserOperatorsByID map[int64]int64  `json:"user_operators"`
	UserReasons       map[*User]string `json:"-"`
	UserOperators     map[*User]*User  `json:"-"`
}

func (r *ReviewRule) Satisfiable() bool {
	return len(r.Users) == 0 || len(r.Users) >= int(r.ApprovalsRequired)
}

type ReviewRuleApprovalInfo struct {
	ReviewRule *ReviewRule `json:"review_rule"`
	Approvers  []*User     `json:"approvers"`
}

type ReviewSummary struct {
	// 按review rule维度聚合，同一个人可能出现在多个info下
	Approvals []*ReviewRuleApprovalInfo `json:"approvals"`
	// reject的用户，暂时不支持传message
	Disapprovers []*User             `json:"disapprovers"`
	Status       ReviewSummaryStatus `json:"status"`
}

func (s *ReviewSummary) ReviewRules() []*ReviewRule {
	return lo.Map(s.Approvals, func(a *ReviewRuleApprovalInfo, _ int) *ReviewRule {
		return a.ReviewRule
	})
}

func (s *ReviewSummary) ApprovedUsers() []*User {
	return lo.UniqBy(
		lo.FlatMap(s.Approvals, func(a *ReviewRuleApprovalInfo, _ int) []*User {
			return a.Approvers
		}),
		func(item *User) int64 {
			return item.ID
		})
}

func (s *ReviewSummary) ApprovedUsernames() []string {
	return lo.Map(s.ApprovedUsers(), func(u *User, _ int) string {
		return u.Username
	})
}

func (s *ReviewSummary) ReviewedUsers() []*User {
	return lo.UniqBy(
		append(s.ApprovedUsers(), s.Disapprovers...),
		func(item *User) int64 {
			return item.ID
		})
}

type ReviewEvent string

const (
	ReviewEventApprove    ReviewEvent = "approve"
	ReviewEventDisapprove ReviewEvent = "disapprove"
	ReviewEventRevoke     ReviewEvent = "revoke"
)

type Review struct {
	ID        int64       `json:"id"`
	SHA       string      `json:"sha"`
	Reviewer  *User       `json:"reviewer"`
	Event     ReviewEvent `json:"event"`
	Content   *string     `json:"content"`
	CreatedAt *time.Time  `json:"timestamp"`
}

type ReviewInput struct {
	SHA                 *string     `json:"sha"`
	Event               ReviewEvent `json:"event"`
	Content             *string     `json:"content"`
	ApproveLaterChanges *bool       `json:"approve_later_changes"`
}

type Migration struct {
	Status string `json:"status"`
}

func (m *Migration) IsMigrated() bool {
	return m != nil && (m.Status == "migrated" || m.Status == "withdrawing")
}

type MigrationResult struct {
	Migrated             bool       `json:"migrated"`
	RepoMigration        *Migration `json:"repo_migration"`
	DevopsSpaceMigration *Migration `json:"devops_space_migration"`
}

type Codebase interface {
	MGetUsersByUsernames(ctx context.Context, usernames []string) (map[string]*User, error)
	MGetUsersByUserIDs(ctx context.Context, userIDs []int64) (map[int64]*User, error)
	UpdateChangeReviewRulesForApp(ctx context.Context, repoID int64, changeID int64, request UpdateChangeReviewRulesForAppRequest) error
	GetReviewSummary(ctx context.Context, repoID, changeID int64) (*ReviewSummary, error)
	ListRequestedReviewers(ctx context.Context, repoID, changeID int64) ([]*User, error)
	AddRequestedReviewers(ctx context.Context, repoID, changeID int64, userIDs ...int64) error
	RemoveRequestedReviewers(ctx context.Context, repoID, changeID int64, userIDs ...int64) error
	ListReviews(ctx context.Context, repoID, changeID int64) ([]*Review, error)
	CreateReview(ctx context.Context, repoID, changeID int64, username string, input ReviewInput) error
	ResetReviewsForApp(ctx context.Context, repoID, changeID int64, reviewerIDs ...int64) error
	MGetUserApproveSettings(ctx context.Context, repoID, changeID int64, userIDs []int64) (map[int64]*UserApproveSetting, error)
	UpdateUserApproveSetting(ctx context.Context, repoID, changeID int64, username string, setting UserApproveSetting) error
	GetMigration(ctx context.Context, repoID int64, bitsSpaceID int64) (*MigrationResult, error)
	GetAppID() int64
}
