package reviewapp

import (
	"context"
	"net/http"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/trace/go-stdlib/nethttp"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
)

type BytedTraceHTTPTransport struct {
	*nethttp.Transport
}

func (t *BytedTraceHTTPTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req, ht := nethttp.TraceRequestWrapper(req)
	defer ht.Finish()
	return t.Transport.RoundTrip(req)
}

type NewReviewAppImplOpt struct {
	BaseURL string
}

func MustNewReviewAppImpl(opt NewReviewAppImplOpt) *ReviewAppImpl {
	return &ReviewAppImpl{
		Client: resty.NewWithClient(
			&http.Client{
				Transport: &BytedTraceHTTPTransport{
					Transport: &nethttp.Transport{},
				},
			}).SetBaseURL(opt.BaseURL).
			SetAllowGetMethodPayload(true).
			EnableTrace().
			OnAfterResponse(func(_ *resty.Client, response *resty.Response) error {
				if response.IsError() {
					return errors.New(response.Status() + " " + string(response.Body()))
				}
				return nil
			}),
	}
}

type ReviewAppImpl struct {
	*resty.Client
}

func (r *ReviewAppImpl) AddReviewersByOpenAPI(ctx context.Context, req AddReviewersByOpenAPIRequest) error {
	_, err := r.Client.R().SetContext(ctx).SetBody(req).Post("/open-api/add-reviewers")
	if err != nil {
		log.V1.CtxError(ctx, "failed to add reviewers by open api: %v", err)
		return errors.WithMessage(err, "failed to add reviewers by open api")
	}
	return nil
}
