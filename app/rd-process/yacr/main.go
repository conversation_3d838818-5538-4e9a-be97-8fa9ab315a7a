package main

import (
	yacr "code.byted.org/devinfra/hagrid/app/rd-process/yacr/kitex_gen/bytedance/bits/yacr/codereviewservice"
	"log"

	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	"code.byted.org/kite/kitex/server"
)

func main() {
	initDeps()
	svr := yacr.NewServer(new(CodeReviewServiceImpl), server.WithMiddleware(kitexmw.LogRequestResponse), server.WithMiddleware(kitexmw.BitsMetrics))

	err := svr.Run()

	if err != nil {
		log.Println(err.Error())
	}
}
