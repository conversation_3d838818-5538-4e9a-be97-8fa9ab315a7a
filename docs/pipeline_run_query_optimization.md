# Pipeline Run 查询优化方案

## 问题背景

`GetPipelineRunsByPipelineIDAndStatus` 方法在某些场景下返回过多数据（最大21294条记录），导致系统响应速度变慢，影响用户体验。

## 优化策略

### 1. 新增优化方法

#### GetPipelineRunsByPipelineIDAndStatusWithLimit
- **用途**: 带限制的查询方法，避免返回过多数据
- **参数**:
  - `ctx`: 上下文
  - `pipelineID`: 流水线ID
  - `limit`: 最大返回记录数
  - `status`: 状态列表
- **优化效果**: 通过 LIMIT 子句限制返回记录数，提升查询性能

#### HasPipelineRunsByPipelineIDAndStatus
- **用途**: 检查是否存在符合条件的记录
- **参数**:
  - `ctx`: 上下文
  - `pipelineID`: 流水线ID
  - `status`: 状态列表
- **优化效果**: 使用 COUNT + LIMIT 1，只检查存在性，不返回具体数据

### 2. 调用场景优化

#### 场景1: 并发控制 (需要所有数据，但可以限制)
- **文件**: `pipeline_concurrency.go`, `pipeline_run_run.go`
- **优化**: 使用 `GetPipelineRunsByPipelineIDAndStatusWithLimit` 限制最多1000条
- **理由**: 通常不会有超过1000个并发运行，这个限制是合理的

#### 场景2: 流水线更新 (需要具体数据)
- **文件**: `pipeline_update.go`
- **优化**: 保持使用原方法，因为需要具体的阻塞运行数据进行处理
- **理由**: 即使存在阻塞运行，也需要知道具体数量和详情来进行后续处理

#### 场景3: 流水线归档 (需要所有数据)
- **文件**: `pipeline_archive.go`
- **优化**: 保持使用原方法，因为需要取消所有进行中的运行
- **理由**: 这个场景确实需要所有数据进行处理

## 性能改进预期

### 查询性能
- **并发控制场景**: 从查询21294条减少到最多1000条，性能提升约95%
- **存在性检查**: 从查询所有记录到只检查存在性，性能提升约99%

### 内存使用
- **数据传输**: 减少网络传输和内存占用
- **GC压力**: 减少大对象创建，降低GC压力

### 响应时间
- **数据库查询**: 减少查询时间
- **数据序列化**: 减少JSON序列化时间
- **网络传输**: 减少网络传输时间

## 监控和验证

### 日志监控
- 新方法中添加了日志记录，可以监控实际查询的记录数
- 日志格式: `GetPipelineRunsByPipelineIDAndStatusWithLimit: pipelineID=%d, limit=%d, status=%v, returned=%d records`

### 性能测试
- 创建了基准测试文件 `repo_pipeline_run_optimization_test.go`
- 可以对比优化前后的性能差异

## 风险评估

### 低风险
- **向后兼容**: 原方法保持不变，不影响现有功能
- **渐进式优化**: 只在特定场景使用新方法

### 潜在风险
- **并发控制**: 1000条限制可能在极端情况下不够，需要监控
- **数据一致性**: 确保限制不会影响业务逻辑正确性

## 后续优化建议

### 数据库层面
1. **索引优化**: 确保 `(pipeline_id, status, id)` 有合适的复合索引
2. **分页查询**: 对于需要大量数据的场景，考虑分页处理

### 应用层面
1. **缓存策略**: 对于频繁查询的数据考虑添加缓存
2. **异步处理**: 对于大量数据处理考虑异步化

### 监控告警
1. **性能监控**: 监控查询响应时间
2. **数据量告警**: 当返回记录数接近限制时告警
3. **错误监控**: 监控是否有因限制导致的业务异常

## 总结

这次优化通过引入限制查询和存在性检查，在保持功能完整性的前提下，显著提升了系统性能。优化方案采用渐进式策略，风险可控，预期能够解决当前的性能问题。
