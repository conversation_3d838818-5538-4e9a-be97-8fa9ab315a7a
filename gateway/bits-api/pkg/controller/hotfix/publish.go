package hotfix

import (
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/hotfix"
	json "github.com/bytedance/sonic"
	"io"
	"sort"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/lang/gg/gslice"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util/jsonconv"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"

	"code.byted.org/overpass/bits_optimus_core/kitex_gen/bits/optimus/core"
	"code.byted.org/overpass/bits_optimus_core/rpc/bits_optimus_core"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

type publishReq struct {
	VersionID int64                          `json:"version_id"`
	Repos     []*hotfix.CreatePublishRepoDTO `json:"repos"`
}

func Publish(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := publishReq{}
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid params"))
	}
	if json.Unmarshal(body, &query) != nil || len(query.Repos) == 0 { // todo: 兼容老接口逻辑，需要下掉
		query2 := struct {
			VersionID int64   `json:"version_id"`
			Repos     []int64 `json:"repos"`
		}{}
		if err := json.Unmarshal(body, &query2); err != nil {
			return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid params"))
		}
		repoList, err := rpc.OptimusClient.GetHotfixVersionRepoList(ctx, &hotfix.GetRepoListReq{VersionID: query.VersionID})
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		}
		repoModuleMap := make(map[int64][]string)
		gslice.ForEach(repoList.Repos, func(r *hotfix.HotfixRepo) { repoModuleMap[r.Id] = r.Modules })
		query.Repos = gslice.Map(query2.Repos, func(i int64) *hotfix.CreatePublishRepoDTO {
			return &hotfix.CreatePublishRepoDTO{Id: i, Modules: repoModuleMap[i]}
		})
	}
	res, err := rpc.OptimusClient.CreateHotfixPublish(ctx, &hotfix.CreatePublishReq{
		VersionID: query.VersionID,
		Repos:     query.Repos,
		Author:    username,
	})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(res.PublishID)
}

func GetPublishList(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := versionQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	list, err := rpc.OptimusClient.GetPublishList(ctx, &hotfix.GetPublishListReq{VersionID: query.VersionID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	sort.Slice(list.Publish, func(i, j int) bool {
		return list.Publish[i].CreateAt > list.Publish[j].CreateAt
	})
	return httpR.StatusOK(jsonconv.JsonSnakeCase{Value: list.Publish})
}

func GetLatestPublishID(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := versionQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	list, err := rpc.OptimusClient.GetPublishList(ctx, &hotfix.GetPublishListReq{VersionID: query.VersionID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	if len(list.Publish) == 0 {
		return httpR.StatusOK(0)
	}
	return httpR.StatusOK(list.Publish[len(list.Publish)-1].Id)
}

type publishIDQuery struct {
	PublishID int64 `json:"publish_id" form:"publish_id" binding:"required"`
}

func GetPublishBasic(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := publishIDQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	pub, err := rpc.OptimusClient.GetPublishInfoByID(ctx, &hotfix.GetPublishInfoReq{PublishID: query.PublishID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(jsonconv.JsonSnakeCase{Value: pub})
}

func GetPublishGraph(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := publishIDQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	graph, err := rpc.OptimusClient.GetPublishGraph(ctx, &hotfix.GetPublishGraphReq{PublishID: query.PublishID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	res := make(map[hotfix.Stage]jsonconv.JsonSnakeCase)
	for k, v := range graph.Nodes {
		res[k] = jsonconv.JsonSnakeCase{Value: v}
	}
	return httpR.StatusOK(res)
}

func GetPublishBranchList(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := publishIDQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	branchInfo, err := rpc.OptimusClient.GetHotfixBranchInfo(ctx, &hotfix.GetHotfixBranchInfoReq{PublishID: query.PublishID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(jsonconv.JsonSnakeCase{Value: branchInfo.Repos})
}

type taskStatusReq struct {
	PublishID int64  `json:"publish_id" form:"publish_id"`
	TaskName  string `json:"task_name" form:"task_name"`
}

type taskStatusResp struct {
	TaskName string  `json:"task_name"`
	TraceID  *string `json:"trace_id,omitempty"`
	State    string  `json:"state"`
}

func GetTaskStatus(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := taskStatusReq{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	bizType := core.BusinessType_Hotfix
	state, err := bits_optimus_core.RawCall.GetTaskState(ctx, &core.GetTaskStateQuery{
		BusinessID:   query.PublishID,
		TaskName:     query.TaskName,
		BusinessType: &bizType,
		Base:         nil,
	})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	ans := taskStatusResp{
		TaskName: query.TaskName,
		TraceID:  state.StartTraceID,
	}
	if state.HasError {
		ans.State = "error"
	} else if state.IsEnd {
		ans.State = "success"
	} else {
		ans.State = "running"
	}
	return httpR.StatusOK(ans)
}

func GetPublishTimeline(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := publishIDQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	timeline, err := rpc.OptimusClient.GetHotfixPublishTimeline(ctx, &hotfix.GetHotfixPublishTimelineReq{PublishID: query.PublishID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	gslice.SortBy(timeline.Timeline, func(i, j *hotfix.PublishTimeline) bool {
		if i.CreateAt == j.CreateAt {
			return i.Id < j.Id
		}
		return i.CreateAt < j.CreateAt
	})
	return httpR.StatusOK(jsonconv.JsonSnakeCase{Value: timeline.Timeline})
}

func TerminatePublish(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := publishIDQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	_, err := rpc.OptimusClient.TerminatePublish(ctx, &hotfix.TerminatePublishReq{PublishID: query.PublishID, Operator: username})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK("ok")
}

func GetIntegrationInfo(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := publishIDQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	res, err := rpc.OptimusClient.GetHotfixIntegrationInfo(ctx, &hotfix.GetHotfixIntegrationInfoReq{PublishID: query.PublishID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(jsonconv.JsonSnakeCase{Value: res})
}

type getBuildPackageInfoReq struct {
	publishIDQuery
	Type *string `json:"type" form:"type"`
}

func GetBuildPackageInfo(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := getBuildPackageInfoReq{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	buildType := hotfix.HotfixBuildType_Package
	if query.Type != nil && *query.Type == "automated_test" {
		buildType = hotfix.HotfixBuildType_AutomatedTest
	}
	res, err := rpc.OptimusClient.GetHotfixBuildDetail(ctx, &hotfix.GetHotfixBuildDetailReq{PublishID: query.PublishID, Type: buildType})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(jsonconv.JsonSnakeCase{Value: res})
}

func CreateAndroidPublish(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := struct {
		PackageID int64 `json:"package_id" form:"package_id" binding:"required"`
	}{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("params error"))
	}
	res, err := rpc.OptimusClient.CreateAndroidHotfixPublish(ctx, &hotfix.CreatAndroidHotfixPublisReq{PackageID: query.PackageID, Operator: username})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(res.PublishID)
}

type retryTaskReq struct {
	PublishID int64  `json:"publish_id" binding:"required"`
	Stage     string `json:"stage" binding:"required"`
}

func RetryTask(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := retryTaskReq{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid params"))
	}
	switch query.Stage {
	case hotfix.Stage_Build.String():
		_, err := rpc.OptimusClient.RetryHotfixBuild(ctx, &hotfix.RetryHotfixBuildReq{PublishID: query.PublishID, Operator: username})
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		}
	default:
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("not support"))
	}
	return httpR.StatusOK("ok")
}
