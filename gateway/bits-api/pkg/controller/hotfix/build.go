package hotfix

import (
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/hotfix"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util/jsonconv"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/lang/gg/gslice"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

type hotfixRepo struct {
	Modules []string `json:"modules"`
	RepoID  int64    `json:"repo_id"`
}

type createAndroidBuildReq struct {
	VersionID int64               `json:"version_id"`
	JavaOnly  bool                `json:"java_only"`
	Repos     []hotfixRepo        `json:"repos"`
	Variants  map[string][]string `json:"variants"`
	Channel   *string             `json:"channel"`
	EnvValue  map[string]string   `json:"env_value"`
}

func CreateAndroidBuild(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	req := &createAndroidBuildReq{}
	if ctx.ShouldBindJSON(req) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("Invalid Arguments"))
	}
	repoList := gslice.Map(req.Repos, func(i hotfixRepo) *hotfix.HotfixBuildRepo {
		return &hotfix.HotfixBuildRepo{RepoID: i.RepoID, Modules: i.Modules}
	})
	variantMap := make(map[hotfix.HotfixFramework][]hotfix.HotfixPackageArchitecture)
	for framework, arches := range req.Variants {
		f, err := hotfix.HotfixFrameworkFromString(framework)
		if err != nil {
			return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid framework, current support: Rebust, Old"))
		}
		archesIDL := make([]hotfix.HotfixPackageArchitecture, 0)
		for _, arch := range arches {
			a, err := hotfix.HotfixPackageArchitectureFromString(arch)
			if err != nil {
				return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("invalid architecture, current support: armhf, arm64"))
			}
			archesIDL = append(archesIDL, a)
		}
		variantMap[f] = archesIDL
	}
	res, err := rpc.OptimusClient.CreateHotfixBuild(ctx, &hotfix.CreateHotfixBuildReq{
		VersionID: req.VersionID,
		JavaNomi:  req.JavaOnly,
		Operator:  username,
		Repos:     repoList,
		Variants:  variantMap,
		Channel:   req.Channel,
		EnvValue:  req.EnvValue,
	})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(res.BuildID)
}

func GetAndroidBuildList(ctx *gin.Context) httpR.HttpRun {
	req := versionQuery{}
	if ctx.ShouldBindQuery(&req) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("Invalid Arguments"))
	}
	res, err := rpc.OptimusClient.GetHotfixBuildList(ctx, &hotfix.GetHotfixBuildListReq{VersionID: req.VersionID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	gslice.SortBy(res.List, func(i, j *hotfix.HotfixBuild) bool { return i.CreateAt > j.CreateAt })
	return httpR.StatusOK(jsonconv.JsonSnakeCase{Value: res.List})
}

type buildQuery struct {
	BuildID int64 `form:"build_id" json:"build_id" binding:"required"`
}

func GetAndroidBuildBranch(ctx *gin.Context) httpR.HttpRun {
	req := buildQuery{}
	if ctx.ShouldBindQuery(&req) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("Invalid Arguments"))
	}
	res, err := rpc.OptimusClient.GetHotfixBuildBranchInfo(ctx, &hotfix.GetHotfixBuildBranchInfoReq{BuildID: req.BuildID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(jsonconv.JsonSnakeCase{Value: res.Repos})
}

func GetLatestBuildID(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := versionQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("Invalid Arguments"))
	}
	res, err := rpc.OptimusClient.GetHotfixBuildList(ctx, &hotfix.GetHotfixBuildListReq{VersionID: query.VersionID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	if len(res.List) == 0 {
		return httpR.StatusOK(0)
	}
	gslice.SortBy(res.List, func(i, j *hotfix.HotfixBuild) bool { return i.CreateAt < j.CreateAt })
	return httpR.StatusOK(res.List[len(res.List)-1].Id)
}

type androidBuildDetail struct {
	*hotfix.HotfixBuild
	BaseInfo map[string]*hotfix.BaseInfo `json:"base_info"`
}

func GetAndroidBuildInfo(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	utils.LogCtxWarn(ctx, "mashuai: %s, %s", ctx.Request.Header.Get("authorization"), username)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	query := buildQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg("Invalid Arguments"))
	}
	res, err := rpc.OptimusClient.GetHotfixBuildInfoByID(ctx, &hotfix.GetHotfixBuildInfoByIDReq{BuildID: query.BuildID})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(jsonconv.JsonSnakeCase{Value: androidBuildDetail{res.Build, res.BaseInfo}})
}
