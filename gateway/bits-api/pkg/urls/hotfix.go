package urls

import (
	"net/http"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/controller/hotfix"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/model"
)

var HotFixUrls = model.GroupUrl{
	GroupName: "hotfix",
	Prefix:    "/api/hotfix",
	Urls: []model.Url{
		{
			//Comment:     "审批通过",
			Methods:     []string{http.MethodPost},
			Path:        "/approval",
			HandlerFunc: hotfix.Approve,
		},
		{
			//Comment:     "审批拒绝",
			Methods:     []string{http.MethodPost},
			Path:        "/reject",
			HandlerFunc: hotfix.Reject,
		},
		{
			//Comment:     "创建版本",
			Methods:     []string{http.MethodPut},
			Path:        "/version",
			HandlerFunc: hotfix.CreateVersion,
		},
		{
			//Comment:     "获取版本列表",
			Methods:     []string{http.MethodGet},
			Path:        "/version",
			HandlerFunc: hotfix.GetVersionList,
		},
		{
			//Comment:     "删除版本",
			Methods:     []string{http.MethodDelete},
			Path:        "/version",
			HandlerFunc: hotfix.DeleteVersion,
		},
		{
			//Comment:     "发布热修",
			Methods:     []string{http.MethodPost},
			Path:        "/publish",
			HandlerFunc: hotfix.Publish,
		},
		{
			//Comment:     "热修发布列表",
			Methods:     []string{http.MethodGet},
			Path:        "/publish/list",
			HandlerFunc: hotfix.GetPublishList,
		},
		{
			//Comment:     "获取最新的热修发布信息",
			Methods:     []string{http.MethodGet},
			Path:        "/publish/latest",
			HandlerFunc: hotfix.GetLatestPublishID,
		},
		{
			//Comment:     "获取发布详情",
			Methods:     []string{http.MethodGet},
			Path:        "/publish/basic",
			HandlerFunc: hotfix.GetPublishBasic,
		},
		{
			//Comment:     "获取发布graph",
			Methods:     []string{http.MethodGet},
			Path:        "/publish/graph",
			HandlerFunc: hotfix.GetPublishGraph,
		},
		{
			//Comment:     "根据VersionID获取仓库列表",
			Methods:     []string{http.MethodGet},
			Path:        "/version/repos",
			HandlerFunc: hotfix.GetRepoListByVersionID,
		},
		{
			//Comment:     "添加仓库",
			Methods:     []string{http.MethodPost},
			Path:        "/version/repo",
			HandlerFunc: hotfix.AddHotfixRepo,
		},
		{
			//Comment:     "编辑仓库",
			Methods:     []string{http.MethodPut},
			Path:        "/version/repo",
			HandlerFunc: hotfix.EditeHotfixRepo,
		},
		{
			//Comment:     "获取已添加的仓库列表",
			Methods:     []string{http.MethodGet},
			Path:        "/version/repo",
			HandlerFunc: hotfix.GetHotfixRepoList,
		},
		{
			//Comment:     "删除仓库",
			Methods:     []string{http.MethodDelete},
			Path:        "/version/repo",
			HandlerFunc: hotfix.DeleteRepo,
		},
		{
			//Comment:     "Mbox 打开",
			Methods:     []string{http.MethodGet},
			Path:        "/version/mbox",
			HandlerFunc: hotfix.GetMboxLink,
		},
		{
			//Comment:     "拉用户到群",
			Methods:     []string{http.MethodPost},
			Path:        "/join_group",
			HandlerFunc: hotfix.JoinLarkGroup,
		},
		{
			//Comment:     "branch接口",
			Methods:     []string{http.MethodGet},
			Path:        "/publish/branch",
			HandlerFunc: hotfix.GetPublishBranchList,
		},
		{
			//Comment:     "查询task状态",
			Methods:     []string{http.MethodGet},
			Path:        "/publish/task/status",
			HandlerFunc: hotfix.GetTaskStatus,
		},
		{
			//Comment:     "获取发布Timeline",
			Methods:     []string{http.MethodGet},
			Path:        "/publish/timeline",
			HandlerFunc: hotfix.GetPublishTimeline,
		},
		{
			//Comment:     "热修复列表页",
			Methods:     []string{http.MethodGet},
			Path:        "/list",
			HandlerFunc: hotfix.GetHotfixList,
		},
		{
			//Comment:     "Flutter列表页",
			Methods:     []string{http.MethodGet},
			Path:        "/flutter_list",
			HandlerFunc: hotfix.GetFlutterDynamicPackageList,
		},
		{
			//Comment:     "终止发布流程",
			Methods:     []string{http.MethodPost},
			Path:        "/publish/terminate",
			HandlerFunc: hotfix.TerminatePublish,
		},
		{
			//Comment:     "工单跳转task",
			Methods:     []string{http.MethodGet},
			Path:        "/task/redirect_ticket",
			HandlerFunc: hotfix.RedirectFromBPM,
			CustomResp:  true,
			CheckIAM:    false,
		},
		{
			//Comment:     "Integration信息",
			Methods:     []string{http.MethodGet},
			Path:        "/publish/integration",
			HandlerFunc: hotfix.GetIntegrationInfo,
		},
		{
			//Comment:     "Build 信息",
			Methods:     []string{http.MethodGet},
			Path:        "/publish/build_package",
			HandlerFunc: hotfix.GetBuildPackageInfo,
		},
		{
			//Comment:     "获取APP下所有版本列表",
			Methods:     []string{http.MethodGet},
			Path:        "/app/versions",
			HandlerFunc: hotfix.GetVersionListByAppID,
		},
		{
			//Comment:     "创建构建",
			Methods:     []string{http.MethodPost},
			Path:        "/build/create",
			HandlerFunc: hotfix.CreateAndroidBuild,
		},
		{
			//Comment:     "获取构建列表",
			Methods:     []string{http.MethodGet},
			Path:        "/build/list",
			HandlerFunc: hotfix.GetAndroidBuildList,
		},
		{
			//Comment:     "获取最新构建ID",
			Methods:     []string{http.MethodGet},
			Path:        "/build/latest",
			HandlerFunc: hotfix.GetLatestBuildID,
		},
		{
			//Comment:     "获取构建分支信息",
			Methods:     []string{http.MethodGet},
			Path:        "/build/branch",
			HandlerFunc: hotfix.GetAndroidBuildBranch,
		},
		{
			//Comment:     "创建发布（Android）",
			Methods:     []string{http.MethodPut},
			Path:        "/publish/android",
			HandlerFunc: hotfix.CreateAndroidPublish,
		},
		{
			//Comment:     "获取构建信息",
			Methods:     []string{http.MethodGet},
			Path:        "/build/info",
			HandlerFunc: hotfix.GetAndroidBuildInfo,
		},
		{
			//Comment:     "获取包信息",
			Methods:     []string{http.MethodGet},
			Path:        "/package/info",
			HandlerFunc: hotfix.GetPackageInfo,
		},
		{
			//Comment: "重试构建",
			Methods:     []string{http.MethodPost},
			Path:        "/publish/retry",
			HandlerFunc: hotfix.RetryTask,
		},
	},
}
